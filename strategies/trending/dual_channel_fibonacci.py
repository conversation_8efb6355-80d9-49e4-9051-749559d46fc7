#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双通道斐波那契突破策略

基于两个EMA通道（通道1: EMA144/169，通道2: EMA576/676）的突破策略。
通过正向遍历历史数据，识别从通道1下方开始，依次突破通道1和通道2的完整周期。

优化内容：
1. 修复性能问题：预计算成交量移动平均，向量化通道1突破点查找
2. 添加数据库兼容性方法
3. 添加配置验证
4. 清理未使用的导入和重复代码
5. 确保起点定义、信号强度公式、触发时机完全正确

作者: QuantFM Team
创建时间: 2025-01-06
优化时间: 2025-01-07
"""

import sys
import os
from datetime import datetime
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
import pandas as pd
import numpy as np
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入基础模块
from utils.logger import get_logger

logger = logging.getLogger(__name__)


@dataclass
class EnhancedSignal:
    """
    增强版信号数据结构
    包含所有关键时间点信息和数据库兼容性方法
    """
    stock_code: str                    # 股票代码
    stock_name: str                    # 股票名称
    strategy_name: str                 # 策略名称

    # 关键时间点
    break_t1_date: datetime           # 通道1突破日期
    break_t2_date: datetime           # 通道2突破日期（触发日期）
    start_low_date: datetime          # 起始低点日期
    target_high_date: datetime        # 目标高点日期

    # 关键价格点
    start_low_price: float            # 起始低点价格
    target_high_price: float          # 目标高点价格
    break_t1_price: float             # 通道1突破价格
    break_t2_price: float             # 通道2突破价格

    # 信号属性
    signal_strength: float            # 信号强度
    volume_ratio_t1: float            # 通道1突破时成交量比率
    volume_ratio_t2: float            # 通道2突破时成交量比率

    # 计算指标
    breakout_amplitude: float         # 突破幅度
    stability_score: float            # 稳定性评分
    structure_score: float            # 结构评分

    # 其他信息
    signal_note: str                  # 信号备注

    # 兼容性属性 - 为了与现有数据库保存逻辑兼容
    @property
    def signal_date(self) -> datetime:
        """信号日期 - 使用通道2突破日期作为信号日期"""
        return self.break_t2_date.date() if isinstance(self.break_t2_date, datetime) else self.break_t2_date

    @property
    def latest_close(self) -> float:
        """最新收盘价 - 使用通道2突破价格"""
        return self.break_t2_price

    @property
    def latest_volume(self) -> float:
        """最新成交量 - 使用通道2突破时的成交量比率"""
        return self.volume_ratio_t2 * 100000  # 估算值

    @property
    def avg_volume(self) -> float:
        """平均成交量 - 基于成交量比率计算"""
        return 100000  # 估算值

    @property
    def volume_ratio(self) -> float:
        """成交量比率 - 使用通道2突破时的成交量比率"""
        return self.volume_ratio_t2

    @property
    def max_high_20d(self) -> float:
        """20日最高价 - 使用目标高点价格"""
        return self.target_high_price

    @property
    def breakout_ratio(self) -> float:
        """突破比率 - 使用突破幅度"""
        return self.breakout_amplitude
    
    def to_database_dict(self) -> Dict[str, Any]:
        """
        转换为数据库兼容的字典格式
        
        Returns:
            数据库兼容的字典
        """
        return {
            'stock_code': self.stock_code,
            'break_t1_date': self.break_t1_date.isoformat() if isinstance(self.break_t1_date, datetime) else self.break_t1_date,
            'break_t2_date': self.break_t2_date.isoformat() if isinstance(self.break_t2_date, datetime) else self.break_t2_date,
            'start_low_date': self.start_low_date.isoformat() if isinstance(self.start_low_date, datetime) else self.start_low_date,
            'target_high_date': self.target_high_date.isoformat() if isinstance(self.target_high_date, datetime) else self.target_high_date,
            'start_low_price': float(self.start_low_price),
            'target_high_price': float(self.target_high_price),
            'break_t1_price': float(self.break_t1_price),
            'break_t2_price': float(self.break_t2_price),
            'signal_strength': float(self.signal_strength),
            'volume_ratio_t1': float(self.volume_ratio_t1),
            'volume_ratio_t2': float(self.volume_ratio_t2),
            'breakout_amplitude': float(self.breakout_amplitude),
            'stability_score': float(self.stability_score),
            'structure_score': float(self.structure_score),
            'signal_note': self.signal_note,
            'stock_name': self.stock_name,
            'strategy_name': self.strategy_name
        }


@dataclass 
class StrategyConfig:
    """策略配置"""
    # 通道参数
    ema_short_1: int = 144
    ema_long_1: int = 169
    ema_short_2: int = 576
    ema_long_2: int = 676
    
    # 时间窗参数
    min_days: int = 2
    max_days: int = 62
    pre_check_days: int = 120
    pivot_window: int = 10
    
    # 成交量参数
    volume_window: int = 20
    volume_ratio: float = 1.2
    
    # 容错参数
    max_intrusion_days: int = 1
    
    # 信号强度权重（根据用户要求）
    weight_volume: float = 0.5        # 成交量权重
    weight_amplitude: float = 0.2     # 突破幅度权重
    weight_stability: float = 0.2     # 稳定性权重
    weight_structure: float = 0.1     # 结构权重
    
    def validate(self) -> List[str]:
        """
        验证配置参数的合理性
        
        Returns:
            错误信息列表
        """
        errors = []
        
        try:
            # 验证权重总和
            total_weight = (self.weight_volume + self.weight_amplitude + 
                           self.weight_stability + self.weight_structure)
            if abs(total_weight - 1.0) > 0.001:
                errors.append(f"权重总和应为1.0，当前为{total_weight:.3f}")
            
            # 验证EMA周期顺序
            if not (self.ema_short_1 < self.ema_long_1 < self.ema_short_2 < self.ema_long_2):
                errors.append("EMA周期顺序不正确，应该满足: ema_short_1 < ema_long_1 < ema_short_2 < ema_long_2")
            
            # 验证时间窗参数
            if self.min_days <= 0:
                errors.append("min_days应大于0")
            
            if self.max_days <= self.min_days:
                errors.append("max_days应大于min_days")
            
            # 验证成交量参数
            if self.volume_ratio <= 1.0:
                errors.append("成交量比率应大于1.0")
            
            if self.volume_window <= 0:
                errors.append("成交量窗口应大于0")
            
            # 验证其他参数
            if self.pre_check_days <= 0:
                errors.append("历史检查天数应大于0")
            
            if self.pivot_window <= 0:
                errors.append("关键点搜索窗口应大于0")
            
            if self.max_intrusion_days < 0:
                errors.append("最大穿透天数不能为负数")
            
        except Exception as e:
            errors.append(f"配置验证过程中发生错误: {e}")
        
        return errors


class DualChannelFibonacciStrategy:
    """双通道斐波那契突破策略（优化版实现）"""
    
    def __init__(self, config: Optional[StrategyConfig] = None):
        """初始化策略"""
        self.logger = get_logger(
            name="dual_channel_fibonacci",
            level="info",
            log_dir="logs",
            console=True
        )
        
        self.config = config or StrategyConfig()
        
        # 验证配置
        config_errors = self.config.validate()
        if config_errors:
            error_msg = "配置验证失败: " + "; ".join(config_errors)
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        
        self.logger.info("双通道斐波那契策略初始化完成")
    
    def get_strategy_name(self) -> str:
        """返回策略名称"""
        return "双通道斐波那契突破"
    
    def analyze(self, stock_code: str, stock_name: str, 
                preprocessed_df: pd.DataFrame) -> Optional[EnhancedSignal]:
        """
        分析股票，基于最新K线检测信号
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            preprocessed_df: 预处理后的DataFrame
            
        Returns:
            EnhancedSignal对象或None
        """
        try:
            self.logger.debug(f"开始分析股票: {stock_code} ({stock_name})")
            
            # 数据验证
            if not self._validate_data(preprocessed_df, stock_code):
                return None

            # 预计算指标（性能优化）
            df_with_indicators = self._precompute_indicators(preprocessed_df)

            # 验证预计算指标
            if not self._validate_precomputed_indicators(df_with_indicators, stock_code):
                return None
            
            # 检查最新K线是否突破通道2（信号触发时机）
            # 只有最新K线突破通道2时才触发信号
            latest_index = len(df_with_indicators) - 1
            if not self._check_latest_channel2_breakout(df_with_indicators, latest_index):
                self.logger.debug(f"股票 {stock_code} 最新K线未突破通道2，不触发信号")
                return None

            # 确保前一天没有突破通道2（确保是新突破）
            if latest_index > 0:
                prev_close = df_with_indicators.iloc[latest_index - 1]['close']
                prev_channel2_upper = df_with_indicators.iloc[latest_index - 1]['channel2_upper']
                if prev_close > prev_channel2_upper:
                    self.logger.debug(f"股票 {stock_code} 前一天已突破通道2，不是新突破")
                    return None
            
            # 向前回溯寻找通道1突破点（优化版）
            t1_break_index = self._find_channel1_breakout_backward_optimized(
                df_with_indicators, latest_index
            )
            
            if t1_break_index == -1:
                self.logger.debug(f"股票 {stock_code} 未找到通道1突破点")
                return None
            
            # 确定起点：通道1突破K线的前一根K线
            start_index = t1_break_index - 1
            if start_index < 0:
                self.logger.debug(f"股票 {stock_code} 起点索引无效")
                return None

            # 修复：验证起点是否真的在tunnel1下方
            if not self._validate_start_point(df_with_indicators, start_index):
                self.logger.debug(f"股票 {stock_code} 起点不在tunnel1下方")
                return None

            # 验证突破序列
            if not self._validate_breakout_sequence(
                df_with_indicators, start_index, t1_break_index, latest_index
            ):
                self.logger.debug(f"股票 {stock_code} 突破序列验证失败")
                return None
            
            # 验证历史结构
            if not self._validate_historical_structure(df_with_indicators, start_index):
                self.logger.debug(f"股票 {stock_code} 历史结构验证失败")
                return None
            
            # 提取关键时间点
            key_points = self._extract_key_timepoints(
                df_with_indicators, start_index, t1_break_index, latest_index
            )
            
            if not key_points:
                self.logger.debug(f"股票 {stock_code} 关键时间点提取失败")
                return None
            
            # 计算信号强度（使用正确的公式）
            signal_strength = self._calculate_enhanced_signal_strength(
                df_with_indicators, key_points
            )
            
            if signal_strength < 0.3:  # 最低信号强度阈值
                self.logger.debug(f"股票 {stock_code} 信号强度过低: {signal_strength:.3f}")
                return None
            
            # 创建增强信号对象
            signal = self._create_enhanced_signal(
                stock_code, stock_name, df_with_indicators, key_points, signal_strength
            )
            
            self.logger.info(f"股票 {stock_code} 成功生成双通道斐波那契信号")
            return signal
            
        except Exception as e:
            self.logger.error(f"分析股票 {stock_code} 失败: {e}")
            import traceback
            self.logger.debug(f"异常详情: {traceback.format_exc()}")
            return None
    
    def _validate_data(self, df: pd.DataFrame, stock_code: str) -> bool:
        """验证数据完整性"""
        try:
            required_columns = [
                'trade_time', 'open', 'high', 'low', 'close', 'volume',
                'channel1_upper', 'channel1_lower', 'channel2_upper', 'channel2_lower'
            ]
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                self.logger.error(f"股票 {stock_code} 缺少必要列: {missing_columns}")
                return False
            
            min_length = max(self.config.ema_long_2, self.config.pre_check_days) + 100
            if len(df) < min_length:
                self.logger.warning(f"股票 {stock_code} 数据长度不足: {len(df)} < {min_length}")
                return False
            
            # 检查数据质量
            if df[['close', 'volume']].isnull().any().any():
                self.logger.warning(f"股票 {stock_code} 存在空值数据")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            return False
    
    def _precompute_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        预计算常用指标，避免重复计算（性能优化）

        Args:
            df: 输入DataFrame

        Returns:
            包含预计算指标的DataFrame
        """
        try:
            df_copy = df.copy()

            # 检查是否已有预计算的成交量移动平均
            volume_ma_col = f'volume_ma_{self.config.volume_window}'
            if volume_ma_col in df_copy.columns:
                # 使用预计算的成交量移动平均
                df_copy['volume_ma'] = df_copy[volume_ma_col]
                self.logger.debug(f"使用预计算的成交量移动平均: {volume_ma_col}")
            else:
                # 自己计算成交量移动平均
                df_copy['volume_ma'] = df_copy['volume'].rolling(
                    window=self.config.volume_window, min_periods=1
                ).mean()
                self.logger.debug(f"自行计算成交量移动平均，窗口: {self.config.volume_window}")

            # 预计算成交量比率（处理除零情况）
            volume_ma_safe = df_copy['volume_ma'].replace(0, np.nan)
            df_copy['volume_ratio'] = df_copy['volume'] / volume_ma_safe
            df_copy['volume_ratio'] = df_copy['volume_ratio'].fillna(1.0)  # 用1.0填充NaN

            # 预计算价格变化率（用于稳定性计算）
            df_copy['price_change_pct'] = df_copy['close'].pct_change().fillna(0.0)

            # 验证关键指标
            required_cols = ['volume_ma', 'volume_ratio']
            missing_cols = [col for col in required_cols if col not in df_copy.columns]
            if missing_cols:
                self.logger.warning(f"预计算后缺少关键指标: {missing_cols}")

            return df_copy

        except Exception as e:
            self.logger.error(f"预计算指标失败: {e}")
            import traceback
            self.logger.debug(f"预计算异常详情: {traceback.format_exc()}")
            return df

    def _validate_precomputed_indicators(self, df: pd.DataFrame, stock_code: str) -> bool:
        """
        验证预计算指标的有效性

        Args:
            df: 包含预计算指标的DataFrame
            stock_code: 股票代码

        Returns:
            是否验证通过
        """
        try:
            # 检查必需的列
            required_cols = ['volume_ma', 'volume_ratio']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                self.logger.error(f"股票 {stock_code} 缺少必需的预计算指标: {missing_cols}")
                return False

            # 检查数据有效性
            latest_rows = df.tail(100)  # 检查最近100行

            # 检查volume_ma是否有过多的NaN或零值
            volume_ma_invalid = latest_rows['volume_ma'].isna().sum() + (latest_rows['volume_ma'] == 0).sum()
            if volume_ma_invalid > 50:  # 超过50%无效
                self.logger.warning(f"股票 {stock_code} volume_ma指标质量较差，无效值比例: {volume_ma_invalid/100:.1%}")
                return False

            # 检查volume_ratio是否有过多的异常值
            volume_ratio_invalid = latest_rows['volume_ratio'].isna().sum()
            if volume_ratio_invalid > 50:
                self.logger.warning(f"股票 {stock_code} volume_ratio指标质量较差，无效值比例: {volume_ratio_invalid/100:.1%}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"股票 {stock_code} 指标验证失败: {e}")
            return False

    def _validate_start_point(self, df: pd.DataFrame, start_index: int) -> bool:
        """
        验证起点是否在tunnel1下方或下轨上

        Args:
            df: 包含指标的DataFrame
            start_index: 起点索引

        Returns:
            是否满足起点要求
        """
        try:
            start_close = df.iloc[start_index]['close']
            start_channel1_upper = df.iloc[start_index]['channel1_upper']

            # 修正：起点的收盘价不能高于tunnel1上轨（允许在通道内或下轨上）
            if start_close > start_channel1_upper:
                return False

            return True

        except Exception as e:
            self.logger.error(f"验证起点失败: {e}")
            return False

    def _check_latest_channel2_breakout(self, df: pd.DataFrame, latest_index: int) -> bool:
        """检查最新K线是否突破通道2"""
        try:
            latest_close = df.iloc[latest_index]['close']
            latest_channel2_upper = df.iloc[latest_index]['channel2_upper']
            latest_volume_ratio = df.iloc[latest_index]['volume_ratio']
            
            # 检查价格突破
            if latest_close <= latest_channel2_upper:
                return False
            
            # 检查成交量条件
            if latest_volume_ratio < self.config.volume_ratio:
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查通道2突破失败: {e}")
            return False
    
    def _find_channel1_breakout_backward_optimized(self, df: pd.DataFrame, 
                                                 from_index: int) -> int:
        """
        优化版向前回溯寻找通道1突破点（向量化实现）
        
        Args:
            df: 包含预计算指标的DataFrame
            from_index: 开始搜索的索引
            
        Returns:
            通道1突破点索引，-1表示未找到
        """
        try:
            search_start = max(0, from_index - self.config.max_days)
            search_end = from_index
            
            if search_end <= search_start:
                return -1
            
            # 获取搜索范围的数据
            search_slice = slice(search_start, search_end)
            close_prices = df.iloc[search_slice]['close'].values
            channel1_upper = df.iloc[search_slice]['channel1_upper'].values
            volume_ratios = df.iloc[search_slice]['volume_ratio'].values
            
            # 向量化找到突破条件
            price_breakout_mask = close_prices > channel1_upper
            volume_breakout_mask = volume_ratios > self.config.volume_ratio
            combined_mask = price_breakout_mask & volume_breakout_mask
            
            # 找到满足条件的索引
            breakout_indices = np.where(combined_mask)[0]
            
            if len(breakout_indices) == 0:
                return -1
            
            # 验证前一天确实在通道1下方
            for idx in reversed(breakout_indices):  # 从最近的开始检查
                actual_index = search_start + idx
                if actual_index > 0:
                    prev_close = df.iloc[actual_index - 1]['close']
                    prev_channel1_upper = df.iloc[actual_index - 1]['channel1_upper']
                    if prev_close <= prev_channel1_upper:
                        return actual_index
            
            return -1
            
        except Exception as e:
            self.logger.error(f"优化版寻找通道1突破点失败: {e}")
            return -1
    
    def _validate_breakout_sequence(self, df: pd.DataFrame,
                                  start_index: int, t1_index: int, t2_index: int) -> bool:
        """验证突破序列的连续性"""
        try:
            # 检查时间窗限制
            days_between = t2_index - t1_index
            if not (self.config.min_days <= days_between <= self.config.max_days):
                return False

            # 向量化检查通道1突破后的连续性
            validation_slice = slice(t1_index + 1, t2_index)
            close_prices = df.iloc[validation_slice]['close'].values
            low_prices = df.iloc[validation_slice]['low'].values
            channel1_upper = df.iloc[validation_slice]['channel1_upper'].values
            channel1_lower = df.iloc[validation_slice]['channel1_lower'].values
            channel2_upper = df.iloc[validation_slice]['channel2_upper'].values

            # 向量化检查收盘价连续性（必须连续高于通道1上轨）
            close_above_channel = close_prices > channel1_upper
            if not np.all(close_above_channel):
                return False

            # 修复：向量化统计影线穿透次数（检查是否跌破通道1下轨）
            intrusion_mask = low_prices <= channel1_lower
            intrusion_count = np.sum(intrusion_mask)

            if intrusion_count > self.config.max_intrusion_days:
                return False

            # 新增：检查在通道1突破后到通道2突破之间是否已经有过通道2突破
            # 这是修复的关键bug - 确保通道2突破是首次突破
            channel2_early_breakout_mask = close_prices > channel2_upper
            if np.any(channel2_early_breakout_mask):
                self.logger.debug(f"在通道1突破后到当前通道2突破之间已经有过通道2突破，跳过信号")
                return False

            return True

        except Exception as e:
            self.logger.error(f"验证突破序列失败: {e}")
            return False
    
    def _validate_historical_structure(self, df: pd.DataFrame, start_index: int) -> bool:
        """验证历史结构"""
        try:
            check_start = max(0, start_index - self.config.pre_check_days)
            
            # 向量化检查历史压制结构
            check_slice = slice(check_start, start_index)
            channel1_upper = df.iloc[check_slice]['channel1_upper'].values
            channel2_lower = df.iloc[check_slice]['channel2_lower'].values
            
            # 向量化计算结构违反次数
            structure_violations = np.sum(channel1_upper >= channel2_lower)

            # 修正：允许合理的违反比例（市场现实情况）
            max_allowed_violations = len(channel1_upper) * 0.6  # 允许60%的违反（适应真实市场）
            if structure_violations > max_allowed_violations:
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证历史结构失败: {e}")
            return False
    
    def _extract_key_timepoints(self, df: pd.DataFrame, 
                              start_index: int, t1_index: int, t2_index: int) -> Dict[str, Any]:
        """提取关键时间点（安全版本）"""
        try:
            # 边界条件检查
            if start_index < 0 or t1_index < 0 or t2_index < 0:
                raise ValueError("索引不能为负数")
            
            if t2_index >= len(df):
                raise ValueError("t2_index超出数据范围")
            
            if start_index >= t1_index or t1_index >= t2_index:
                raise ValueError("时间顺序不正确")
            
            # 1. 寻找起始低点（在起点附近）
            low_search_start = max(0, start_index - self.config.pivot_window)
            low_search_end = min(len(df), start_index + self.config.pivot_window + 1)
            
            if low_search_end <= low_search_start:
                raise ValueError("起始低点搜索范围无效")
            
            low_slice = df.iloc[low_search_start:low_search_end]
            start_low_idx = low_slice['low'].idxmin()
            start_low_price = df.loc[start_low_idx, 'low']
            start_low_date = df.loc[start_low_idx, 'trade_time']
            
            # 2. 寻找目标高点（在通道2突破后）
            high_search_start = t2_index
            high_search_end = min(len(df), t2_index + self.config.pivot_window + 1)
            
            if high_search_end <= high_search_start:
                # 如果没有足够的未来数据，使用当前点作为目标高点
                target_high_idx = t2_index
            else:
                high_slice = df.iloc[high_search_start:high_search_end]
                target_high_idx = high_slice['high'].idxmax()
            
            target_high_price = df.loc[target_high_idx, 'high']
            target_high_date = df.loc[target_high_idx, 'trade_time']
            
            return {
                'start_index': start_index,
                't1_index': t1_index,
                't2_index': t2_index,
                'start_low_date': start_low_date,
                'start_low_price': start_low_price,
                'target_high_date': target_high_date,
                'target_high_price': target_high_price,
                'break_t1_date': df.iloc[t1_index]['trade_time'],
                'break_t1_price': df.iloc[t1_index]['close'],
                'break_t2_date': df.iloc[t2_index]['trade_time'],
                'break_t2_price': df.iloc[t2_index]['close']
            }
            
        except Exception as e:
            self.logger.error(f"提取关键时间点失败: {e}")
            return {}
    
    def _calculate_enhanced_signal_strength(self, df: pd.DataFrame, 
                                          key_points: Dict[str, Any]) -> float:
        """
        根据用户要求计算信号强度
        信号强度 = 成交量权重(0.5) + 突破幅度权重(0.2) + 稳定性权重(0.2) + 结构权重(0.1)
        """
        try:
            t1_index = key_points['t1_index']
            t2_index = key_points['t2_index']
            
            # 1. 成交量权重 (0.5) - 使用预计算的volume_ratio
            t1_volume_ratio = df.iloc[t1_index]['volume_ratio']
            t2_volume_ratio = df.iloc[t2_index]['volume_ratio']
            avg_volume_ratio = (t1_volume_ratio + t2_volume_ratio) / 2
            volume_strength = min((avg_volume_ratio - 1.0) / 2.0, 1.0)
            volume_weight = max(volume_strength, 0.0) * self.config.weight_volume
            
            # 2. 突破幅度权重 (0.2)
            start_low_price = key_points['start_low_price']
            target_high_price = key_points['target_high_price']
            amplitude = (target_high_price - start_low_price) / start_low_price
            amplitude_strength = min(amplitude / 0.5, 1.0)  # 50%涨幅对应满分
            amplitude_weight = max(amplitude_strength, 0.0) * self.config.weight_amplitude
            
            # 3. 稳定性权重 (0.2) - 使用预计算的price_change_pct
            stability_slice = slice(t1_index, t2_index + 1)
            price_changes = df.iloc[stability_slice]['price_change_pct'].dropna()
            if len(price_changes) > 0:
                volatility = price_changes.std()
                stability_strength = max(1.0 - volatility * 10, 0.0)
            else:
                stability_strength = 0.0
            stability_weight = min(stability_strength, 1.0) * self.config.weight_stability
            
            # 4. 结构权重 (0.1)
            structure_strength = self._calculate_structure_score(df, key_points)
            structure_weight = structure_strength * self.config.weight_structure
            
            total_strength = (volume_weight + amplitude_weight + 
                            stability_weight + structure_weight)
            
            return min(total_strength, 1.0)
            
        except Exception as e:
            self.logger.error(f"计算信号强度失败: {e}")
            return 0.0
    
    def _calculate_structure_score(self, df: pd.DataFrame, key_points: Dict[str, Any]) -> float:
        """计算结构评分（向量化版本）"""
        try:
            start_index = key_points['start_index']
            check_start = max(0, start_index - self.config.pre_check_days)
            
            if start_index <= check_start:
                return 0.0
            
            # 向量化检查历史压制结构
            check_slice = slice(check_start, start_index)
            channel1_upper = df.iloc[check_slice]['channel1_upper'].values
            channel2_lower = df.iloc[check_slice]['channel2_lower'].values
            
            # 向量化计算结构违反次数
            structure_violations = np.sum(channel1_upper >= channel2_lower)
            total_checks = len(channel1_upper)
            
            if total_checks == 0:
                return 0.0
            
            # 结构清晰度评分
            structure_clarity = 1.0 - (structure_violations / total_checks)
            
            return max(structure_clarity, 0.0)
            
        except Exception as e:
            self.logger.error(f"计算结构评分失败: {e}")
            return 0.0
    
    def _create_enhanced_signal(self, stock_code: str, stock_name: str, 
                              df: pd.DataFrame, key_points: Dict[str, Any], 
                              signal_strength: float) -> EnhancedSignal:
        """创建增强版信号对象"""
        try:
            t1_index = key_points['t1_index']
            t2_index = key_points['t2_index']
            
            # 使用预计算的成交量比率
            volume_ratio_t1 = df.iloc[t1_index]['volume_ratio']
            volume_ratio_t2 = df.iloc[t2_index]['volume_ratio']
            
            # 计算各项指标
            start_low_price = key_points['start_low_price']
            target_high_price = key_points['target_high_price']
            breakout_amplitude = (target_high_price - start_low_price) / start_low_price
            
            # 稳定性评分
            stability_slice = slice(t1_index, t2_index + 1)
            price_changes = df.iloc[stability_slice]['price_change_pct'].dropna()
            if len(price_changes) > 0:
                volatility = price_changes.std()
                stability_score = max(1.0 - volatility * 10, 0.0)
            else:
                stability_score = 0.0
            
            # 结构评分
            structure_score = self._calculate_structure_score(df, key_points)
            
            # 创建信号备注
            signal_note = (
                f"通道1突破:{key_points['break_t1_date'].strftime('%Y-%m-%d')},"
                f"通道2突破:{key_points['break_t2_date'].strftime('%Y-%m-%d')},"
                f"起始低点:{start_low_price:.2f},"
                f"目标高点:{target_high_price:.2f}"
            )
            
            return EnhancedSignal(
                stock_code=stock_code,
                stock_name=stock_name,
                strategy_name="双通道斐波那契突破",
                break_t1_date=key_points['break_t1_date'],
                break_t2_date=key_points['break_t2_date'],
                start_low_date=key_points['start_low_date'],
                target_high_date=key_points['target_high_date'],
                start_low_price=start_low_price,
                target_high_price=target_high_price,
                break_t1_price=key_points['break_t1_price'],
                break_t2_price=key_points['break_t2_price'],
                signal_strength=signal_strength,
                volume_ratio_t1=volume_ratio_t1,
                volume_ratio_t2=volume_ratio_t2,
                breakout_amplitude=breakout_amplitude,
                stability_score=stability_score,
                structure_score=structure_score,
                signal_note=signal_note
            )
            
        except Exception as e:
            self.logger.error(f"创建增强信号失败: {e}")
            raise


# 便捷函数
def create_optimized_strategy(config: Optional[StrategyConfig] = None) -> DualChannelFibonacciStrategy:
    """
    创建优化版策略实例
    
    Args:
        config: 可选的策略配置
        
    Returns:
        策略实例
    """
    return DualChannelFibonacciStrategy(config)


if __name__ == "__main__":
    # 简单测试
    print("优化版双通道斐波那契策略加载成功")
    
    # 测试配置验证
    config = StrategyConfig()
    errors = config.validate()
    
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✅ 配置验证通过")
        
        # 测试策略创建
        try:
            strategy = create_optimized_strategy(config)
            print("✅ 策略创建成功")
        except Exception as e:
            print(f"❌ 策略创建失败: {e}")