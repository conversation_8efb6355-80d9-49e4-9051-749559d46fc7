# 双通道斐波那契突破策略

## 概述

双通道斐波那契突破策略是一个基于技术分析的量化交易策略，通过分析股票价格在两个不同EMA通道之间的突破行为，识别具有潜在上涨动能的股票。

## 策略原理

### 核心思想

该策略基于以下核心思想：
1. **双通道系统**：使用两个不同敏感度的EMA通道来捕捉不同级别的趋势
2. **完整突破周期**：寻找从通道1下方开始，依次突破通道1和通道2的完整过程
3. **成交量确认**：通过成交量放大来确认突破的有效性
4. **历史结构验证**：确保通道压制结构的真实性

### 通道定义

- **通道1（敏感通道）**：
  - 上轨：EMA144（默认）
  - 下轨：EMA169（默认）
  - 用途：捕捉短期趋势变化

- **通道2（稳定通道）**：
  - 上轨：EMA576（默认）
  - 下轨：EMA676（默认）
  - 用途：确认长期趋势方向

### 信号生成流程

1. **寻找起点**：股价跌破通道1下轨，形成潜在起点
2. **通道1突破**：股价突破通道1上轨，且成交量放大
3. **连续性验证**：突破后股价持续在通道1上轨之上
4. **通道2突破**：股价进一步突破通道2上轨，且成交量放大
5. **历史结构验证**：确保通道1上轨在历史上确实被通道2下轨压制
6. **关键点提取**：识别起始低点和目标高点
7. **信号强度计算**：基于突破幅度和成交量计算信号强度

## 配置参数

### 通道参数
- `ema_short_1`: 通道1短期EMA周期（默认144）
- `ema_long_1`: 通道1长期EMA周期（默认169）
- `ema_short_2`: 通道2短期EMA周期（默认576）
- `ema_long_2`: 通道2长期EMA周期（默认676）

### 时间窗参数
- `min_days`: 通道1到通道2突破的最小天数（默认2天）
- `max_days`: 通道1到通道2突破的最大天数（默认62天）
- `pre_check_days`: 历史结构验证天数（默认120天）
- `pivot_window`: 关键点提取窗口（默认10天）

### 成交量参数
- `volume_window`: 成交量移动平均窗口（默认20天）
- `volume_ratio`: 突破时成交量放大倍数（默认1.2倍）

### 容错参数
- `max_intrusion_days`: 允许的最大影线穿透次数（默认1次）

## 使用方法

### 基本使用

```python
from strategies.trending.dual_channel_fibonacci import DualChannelFibonacciStrategy

# 使用默认配置
strategy = DualChannelFibonacciStrategy()

# 分析股票
signal = strategy.analyze(
    stock_code="000001",
    stock_name="平安银行",
    preprocessed_df=df  # 包含所有指标的DataFrame
)

if signal:
    print(f"发现信号: {signal.stock_code}, 强度: {signal.signal_strength}")
```

### 自定义配置

```python
from strategies.trending.dual_channel_fibonacci import StrategyConfig, DualChannelFibonacciStrategy

# 创建自定义配置
config = StrategyConfig(
    ema_short_1=100,
    ema_long_1=120,
    ema_short_2=400,
    ema_long_2=500,
    volume_ratio=1.5
)

# 验证配置
if config.validate():
    strategy = DualChannelFibonacciStrategy(config)
else:
    print("配置无效:", config.get_validation_errors())
```

### 配置文件使用

在 `config/main.toml` 中添加配置：

```toml
[dual_channel_fibonacci]
ema_short_1 = 144
ema_long_1 = 169
ema_short_2 = 576
ema_long_2 = 676
min_days = 2
max_days = 62
volume_ratio = 1.2
```

然后加载配置：

```python
strategy = DualChannelFibonacciStrategy(config_file="config/main.toml")
```

## 数据要求

### 输入数据格式

策略需要包含以下字段的DataFrame：

**基础K线数据**：
- `trade_time`: 交易时间
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `close`: 收盘价
- `volume`: 成交量

**技术指标**（由indicators模块计算）：
- `ema_144`, `ema_169`, `ema_576`, `ema_676`: EMA指标
- `channel1_upper`, `channel1_lower`: 通道1上下轨
- `channel2_upper`, `channel2_lower`: 通道2上下轨
- `volume_ma_20`: 成交量移动平均

### 数据长度要求

- 最小数据长度：max(ema_long_2, pre_check_days) + 100
- 推荐数据长度：1000+ 交易日
- 数据质量：无缺失值，时间连续

## 性能优化

### 缓存机制

策略使用多级缓存来提高性能：

1. **指标计算缓存**：避免重复计算EMA等指标
2. **数据预处理缓存**：缓存预处理后的DataFrame
3. **算法结果缓存**：缓存中间计算结果

### 向量化优化

- 使用numpy向量化操作替代循环
- 批量处理多个股票的数据
- 内存优化的数据结构

### 内存管理

- 及时释放不需要的中间数据
- 使用float32减少内存占用
- 分批处理大量股票数据

## 信号输出

### Signal对象字段

策略返回的Signal对象包含以下信息：

- `stock_code`: 股票代码
- `stock_name`: 股票名称
- `strategy_name`: 策略名称（"双通道斐波那契突破"）
- `signal_date`: 信号日期
- `signal_strength`: 信号强度（0-1）
- `latest_close`: 最新收盘价
- `latest_volume`: 最新成交量
- `volume_ratio`: 成交量比率
- `signal_note`: 详细信号信息

### 信号强度计算

信号强度基于以下因素计算：
1. 突破幅度（价格相对于通道的突破程度）
2. 成交量放大倍数
3. 时间窗口内的价格稳定性
4. 历史结构的清晰度

## 回测和验证

### 历史回测

```python
# 回测示例
def backtest_strategy(stock_list, start_date, end_date):
    strategy = DualChannelFibonacciStrategy()
    results = []
    
    for stock_code in stock_list:
        # 获取历史数据
        df = get_historical_data(stock_code, start_date, end_date)
        
        # 计算指标
        df = preprocess_data(df)
        
        # 执行策略
        signal = strategy.analyze(stock_code, "股票名称", preprocessed_df=df)
        
        if signal:
            results.append(signal)
    
    return results
```

### 性能指标

- **信号准确率**：信号后N天内的上涨概率
- **平均收益率**：信号后的平均收益表现
- **最大回撤**：策略的最大亏损幅度
- **夏普比率**：风险调整后的收益率

## 风险控制

### 内置风险控制

1. **数据验证**：严格的数据完整性检查
2. **参数验证**：配置参数的合理性验证
3. **异常处理**：完善的异常捕获和处理
4. **日志记录**：详细的执行日志

### 使用建议

1. **分散投资**：不要将所有资金投入单一信号
2. **止损设置**：设置合理的止损位
3. **定期回测**：定期验证策略的有效性
4. **参数调优**：根据市场环境调整参数

## 常见问题

### Q: 为什么选择这些EMA周期？
A: 144、169、576、676这些数字基于斐波那契数列的特性，能够更好地捕捉市场的自然节奏。

### Q: 如何处理数据缺失？
A: 策略会自动跳过数据不完整的股票，确保分析结果的可靠性。

### Q: 信号频率如何？
A: 该策略属于中长期策略，信号频率相对较低，但质量较高。

### Q: 如何优化参数？
A: 建议通过历史回测来优化参数，但要避免过度拟合。

## 更新日志

### v1.0.0 (2025-01-06)
- 初始版本发布
- 实现基础双通道突破逻辑
- 添加配置管理功能
- 实现性能优化

### v1.1.0 (2025-01-06)
- 添加缓存机制
- 实现向量化优化
- 完善文档和示例
- 添加性能测试

## 技术支持

如有问题或建议，请联系开发团队：
- 邮箱：<EMAIL>
- 文档：查看项目文档目录
- 测试：运行test_*.py文件进行验证