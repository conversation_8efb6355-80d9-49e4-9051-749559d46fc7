"""
数据库管理器

提供统一的数据库连接管理和操作接口。
主要功能：
- 高效的连接池管理
- 健康监控和自动恢复
- 统计收集和性能监控
- 通用的数据库操作方法
"""

import os
import time
import threading
import logging
import traceback
import psycopg2
import psycopg2.extras
import atexit
import queue
from contextlib import contextmanager
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Any, Optional, Union

# 设置PostgreSQL客户端编码环境变量
os.environ['PGCLIENTENCODING'] = 'GBK'

# 导入配置管理模块
from config.config_manager import get_config

# 获取日志记录器
logger = logging.getLogger(__name__)


class DatabaseType(Enum):
    """支持的数据库类型"""
    TIMESCALEDB = "timescaledb"
    POSTGRESQL = "postgresql"
    DOLPHINDB = "dolphindb"


@dataclass
class ConnectionConfig:
    """数据库连接配置"""
    host: str = "localhost"
    port: int = 5432
    database: str = "postgres"
    user: str = "postgres"
    password: str = "postgres"
    connect_timeout: int = 10
    client_encoding: str = "UTF8"

    # 连接池配置
    min_connections: int = 1
    max_connections: int = 20
    acquire_timeout: int = 10
    max_lifetime: int = 3600
    max_idle_time: int = 600
    health_check_interval: int = 60
    preload_pool: bool = True

    # 监控配置
    enable_monitoring: bool = True
    collect_stats: bool = True
    log_slow_queries: bool = True
    slow_query_threshold_ms: int = 500
    log_all_queries: bool = False
    enable_leak_detection: bool = True
    leak_detection_threshold: int = 300

    # 高级配置
    use_prepared_statements: bool = True
    auto_reconnect: bool = True
    max_reconnect_attempts: int = 3
    reconnect_delay: int = 1


@dataclass
class ConnectionStats:
    """连接统计信息"""
    connections_created: int = 0
    connections_closed: int = 0
    connections_reused: int = 0
    queries_executed: int = 0
    query_errors: int = 0
    connection_errors: int = 0
    reconnect_attempts: int = 0
    reconnect_successes: int = 0
    slow_queries: int = 0
    connection_leaks_detected: int = 0
    peak_connections_used: int = 0
    current_connections_used: int = 0
    connection_wait_time_total: float = 0
    connection_wait_count: int = 0

    def get_avg_wait_time(self) -> float:
        """获取平均等待时间"""
        return self.connection_wait_time_total / self.connection_wait_count if self.connection_wait_count > 0 else 0


class ConnectionPool:
    """优化的数据库连接池"""

    def __init__(self, config: ConnectionConfig, db_type: DatabaseType = DatabaseType.TIMESCALEDB):
        """
        初始化连接池

        Args:
            config: 连接配置
            db_type: 数据库类型
        """
        self.config = config
        self.db_type = db_type
        self.stats = ConnectionStats()
        self._lock = threading.RLock()
        self._pool_lock = threading.Lock()

        # 连接池实现
        self._available_connections = queue.Queue(maxsize=config.max_connections)
        self._in_use_connections = {}  # connection_id -> connection_info
        self._connection_counter = 0

        # 健康监控
        self._health_check_thread: Optional[threading.Thread] = None
        self._health_check_running = False

        # 初始化连接池
        self._initialize_pool()

        # 启动健康检查
        if config.health_check_interval > 0:
            self._start_health_check()

        logger.info(f"连接池初始化完成: {config.host}:{config.port}/{config.database}, "
                   f"池大小: {config.min_connections}-{config.max_connections}")

    def _initialize_pool(self):
        """初始化连接池，预创建连接"""
        if self.config.preload_pool:
            for _ in range(self.config.min_connections):
                try:
                    conn = self._create_connection()
                    if conn:
                        self._available_connections.put(conn)
                        logger.debug(f"预创建连接成功 ({self._available_connections.qsize()}/{self.config.max_connections})")
                except Exception as e:
                    logger.warning(f"预创建连接失败: {e}")

    def _create_connection(self):
        """创建新的数据库连接"""
        try:
            # 构建连接参数
            conn_params = {
                'host': self.config.host,
                'port': self.config.port,
                'database': self.config.database,
                'user': self.config.user,
                'password': self.config.password,
                'connect_timeout': self.config.connect_timeout,
                'client_encoding': self.config.client_encoding,
                'cursor_factory': psycopg2.extras.RealDictCursor
            }

            # 创建连接
            conn = psycopg2.connect(**conn_params)
            # 设置autocommit为True，避免长时间事务阻塞
            conn.autocommit = True

            # 设置连接超时参数，防止连接挂起
            with conn.cursor() as cursor:
                cursor.execute("SET statement_timeout = '300s'")  # 5分钟查询超时
                cursor.execute("SET idle_in_transaction_session_timeout = '60s'")  # 1分钟空闲事务超时

            # 测试连接
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1 as test_value")
                result = cursor.fetchone()
                # 处理RealDictCursor返回字典的情况
                if isinstance(result, dict):
                    test_value = result.get('test_value')
                else:
                    test_value = result[0] if result else None

                if not (result and test_value == 1):
                    raise Exception("连接测试失败")

            # 更新统计
            with self._lock:
                self.stats.connections_created += 1
                self._connection_counter += 1

            return conn

        except Exception as e:
            with self._lock:
                self.stats.connection_errors += 1

            # 提供更详细的错误信息
            error_msg = f"创建数据库连接失败: {type(e).__name__}: {str(e)}"
            if hasattr(e, 'pgcode') and e.pgcode:
                error_msg += f" (PostgreSQL错误码: {e.pgcode})"
            if hasattr(e, 'pgerror') and e.pgerror:
                error_msg += f" (PostgreSQL错误: {e.pgerror})"

            logger.error(error_msg)
            logger.error(f"连接参数: host={self.config.host}, port={self.config.port}, "
                        f"database={self.config.database}, user={self.config.user}")

            # 记录详细的异常信息用于调试
            logger.debug(f"连接创建异常详情:\n{traceback.format_exc()}")

            return None

    def _is_connection_valid(self, conn) -> bool:
        """检查连接是否有效"""
        try:
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1 as test_value")
                result = cursor.fetchone()
                # 处理RealDictCursor返回字典的情况
                if isinstance(result, dict):
                    test_value = result.get('test_value')
                else:
                    test_value = result[0] if result else None

                return bool(result and test_value == 1)
        except Exception:
            return False

    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn: Optional[Any] = None
        conn_id: Optional[Any] = None
        start_time = time.time()

        try:
            # 尝试从池中获取连接
            try:
                conn = self._available_connections.get(timeout=self.config.acquire_timeout)
                with self._lock:
                    self.stats.connections_reused += 1
            except queue.Empty:
                # 池中无可用连接，创建新连接
                if len(self._in_use_connections) < self.config.max_connections:
                    conn = self._create_connection()
                    if not conn:
                        raise Exception("无法创建新连接")
                else:
                    raise Exception("连接池已满，无法获取连接")

            # 验证连接有效性
            if not self._is_connection_valid(conn):
                logger.warning("获取到无效连接，重新创建")
                try:
                    conn.close()  # 尝试关闭无效连接
                except Exception:
                    pass  # 忽略关闭时的错误

                # 重新创建连接
                conn = self._create_connection()
                if not conn:
                    raise Exception("无法重新创建连接")

            # 更新统计
            with self._lock:
                conn_id = self._connection_counter
                self._connection_counter += 1
                self.stats.current_connections_used += 1
                if self.stats.current_connections_used > self.stats.peak_connections_used:
                    self.stats.peak_connections_used = self.stats.current_connections_used

                # 记录连接等待时间
                wait_time = time.time() - start_time
                self.stats.connection_wait_time_total += wait_time
                self.stats.connection_wait_count += 1

                # 记录在使用的连接
                self._in_use_connections[conn_id] = {
                    'conn': conn,
                    'acquire_time': time.time(),
                    'stack_trace': ''.join(traceback.format_stack()) if self.config.enable_leak_detection else None
                }

            # 返回连接
            yield conn

        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            # 如果连接已创建但出现其他错误，确保连接被归还或关闭
            if conn and conn_id not in self._in_use_connections:
                try:
                    self._available_connections.put(conn, block=False)
                except queue.Full:
                    try:
                        conn.close()
                    except Exception:
                        pass
            raise  # 重新抛出异常，让调用者处理

        finally:
            # 确保连接被归还到池中
            if conn and conn_id in self._in_use_connections:
                try:
                    self._in_use_connections.pop(conn_id)
                    with self._lock:
                        self.stats.current_connections_used -= 1

                    # 归还连接到池中
                    try:
                        self._available_connections.put(conn, block=False)
                    except queue.Full:
                        logger.warning("连接池已满，关闭多余连接")
                        try:
                            conn.close()
                        except Exception:
                            pass
                except Exception as e:
                    logger.error(f"归还连接到池中失败: {e}")

    def _start_health_check(self):
        """启动健康检查线程"""
        if self._health_check_running:
            return

        self._health_check_running = True

        def health_check_worker():
            while self._health_check_running:
                try:
                    self._perform_health_check()
                except Exception as e:
                    logger.error(f"健康检查失败: {e}")

                time.sleep(self.config.health_check_interval)

        self._health_check_thread = threading.Thread(target=health_check_worker, daemon=True)
        self._health_check_thread.start()
        logger.info(f"健康检查线程已启动，检查间隔: {self.config.health_check_interval}秒")

    def _perform_health_check(self):
        """执行健康检查"""
        current_time = time.time()

        # 检查使用中的连接是否超时
        with self._lock:
            expired_connections = []
            for conn_id, conn_info in self._in_use_connections.items():
                acquired_at = conn_info['acquire_time']
                if current_time - acquired_at > self.config.leak_detection_threshold:
                    expired_connections.append(conn_id)
                    self.stats.connection_leaks_detected += 1
                    logger.warning(f"检测到连接泄漏: 连接 {conn_id} 已使用 {current_time - acquired_at:.1f} 秒")

        # 清理泄漏的连接
        for conn_id in expired_connections:
            try:
                with self._lock:
                    if conn_id in self._in_use_connections:
                        conn_info = self._in_use_connections[conn_id]
                        conn = conn_info['conn']
                        del self._in_use_connections[conn_id]
                        self.stats.current_connections_used = len(self._in_use_connections)

                # 关闭泄漏的连接
                try:
                    conn.close()
                    with self._lock:
                        self.stats.connections_closed += 1
                except Exception:
                    pass
            except Exception as e:
                logger.error(f"清理泄漏连接失败: {e}")

        # 检查并清理idle in transaction连接
        self._cleanup_idle_in_transaction()

    def _cleanup_idle_in_transaction(self):
        """清理idle in transaction状态的连接"""
        try:
            # 创建临时连接来检查数据库状态
            temp_conn = self._create_connection()
            with temp_conn.cursor() as cursor:
                # 查找idle in transaction超过30秒的连接
                cursor.execute("""
                    SELECT pid, state, now() - state_change as duration
                    FROM pg_stat_activity
                    WHERE state = 'idle in transaction'
                    AND (now() - state_change) > interval '30 seconds'
                    AND pid != pg_backend_pid()
                """)

                idle_connections = cursor.fetchall()

                if idle_connections:
                    logger.warning(f"发现 {len(idle_connections)} 个idle in transaction连接")

                    # 终止这些连接
                    for conn_info in idle_connections:
                        pid = conn_info[0]
                        duration = conn_info[2]
                        try:
                            cursor.execute("SELECT pg_terminate_backend(%s)", (pid,))
                            logger.info(f"已终止idle in transaction连接 PID: {pid}, 持续时间: {duration}")
                        except Exception as e:
                            logger.warning(f"终止连接 PID {pid} 失败: {e}")

            temp_conn.close()

        except Exception as e:
            logger.error(f"清理idle in transaction连接失败: {e}")

    def close(self):
        """关闭连接池"""
        # 停止健康检查
        self._health_check_running = False
        if self._health_check_thread:
            self._health_check_thread.join(timeout=5)

        # 关闭所有连接
        with self._lock:
            # 关闭使用中的连接
            for conn_info in self._in_use_connections.values():
                try:
                    conn_info['conn'].close()
                except Exception:
                    pass
            self._in_use_connections.clear()

            # 关闭池中的连接
            while not self._available_connections.empty():
                try:
                    conn = self._available_connections.get_nowait()
                    conn.close()
                    self.stats.connections_closed += 1
                except Exception:
                    pass

        logger.info("连接池已关闭")

    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self._lock:
            return {
                'connections_created': self.stats.connections_created,
                'connections_closed': self.stats.connections_closed,
                'connections_reused': self.stats.connections_reused,
                'queries_executed': self.stats.queries_executed,
                'query_errors': self.stats.query_errors,
                'connection_errors': self.stats.connection_errors,
                'slow_queries': self.stats.slow_queries,
                'connection_leaks_detected': self.stats.connection_leaks_detected,
                'current_connections_used': self.stats.current_connections_used,
                'peak_connections_used': self.stats.peak_connections_used,
                'avg_wait_time_ms': self.stats.get_avg_wait_time() * 1000,
                'available_connections': self._available_connections.qsize(),
                'max_connections': self.config.max_connections
            }


class UnifiedDatabaseManager:
    """统一数据库管理器"""

    _instance: Optional[Any] = None
    _lock = threading.Lock()
    _write_lock = threading.Lock()  # 全局写入锁，防止并发写入冲突

    def __init__(self):
        """初始化数据库管理器"""
        self._pools = {}  # pool_key -> ConnectionPool
        self._config_cache = {}
        self._default_pool_key: Optional[Any] = None

        # 死锁重试配置（可通过配置文件调整）
        db_config = get_config("database")
        deadlock_config = db_config.get("deadlock_retry", {})
        self.deadlock_max_retries = deadlock_config.get("max_retries", 3)
        self.deadlock_base_delay = deadlock_config.get("base_delay", 0.1)
        self.deadlock_max_delay = deadlock_config.get("max_delay", 2.0)

        # 加载默认配置
        self._load_default_config()

        # 注册退出清理
        atexit.register(self.close_all)

        logger.info("统一数据库管理器初始化完成")

    def _is_deadlock_error(self, error_str: str) -> bool:
        """检查是否为死锁错误"""
        error_str = error_str.lower()
        return any(keyword in error_str for keyword in [
            '死锁', 'deadlock', 'deadlockdetected',
            'lock timeout', 'could not serialize'
        ])

    def _calculate_retry_delay(self, attempt: int) -> float:
        """计算重试延迟（指数退避 + 随机抖动）"""
        import random
        delay = min(self.deadlock_base_delay * (2 ** attempt), self.deadlock_max_delay)
        jitter = random.uniform(0.8, 1.2)  # ±20%随机抖动
        return delay * jitter

    @classmethod
    def get_instance(cls) -> 'UnifiedDatabaseManager':
        """获取单例实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    def _load_default_config(self):
        """加载默认数据库配置"""
        try:
            # 从项目配置中读取数据库配置
            db_config = get_config("database")

            config = ConnectionConfig(
                # 优先使用统一参数名，如果不存在则使用兼容参数名
                host=db_config.get("host", db_config.get("timescaledb_host", "localhost")),
                port=db_config.get("port", db_config.get("timescaledb_port", 5432)),
                database=db_config.get("database", db_config.get("timescaledb_database", "postgres")),
                user=db_config.get("user", db_config.get("timescaledb_user", "postgres")),
                password=db_config.get("password", db_config.get("timescaledb_password", "postgres")),
                connect_timeout=db_config.get("connect_timeout", 10),
                client_encoding=db_config.get("client_encoding", "UTF8"),

                # 连接池配置
                min_connections=db_config.get("min_connections", 1),
                max_connections=db_config.get("max_connections", db_config.get("pool_size", 20)),
                acquire_timeout=db_config.get("acquire_timeout", 10),
                max_lifetime=db_config.get("max_lifetime", 3600),
                max_idle_time=db_config.get("max_idle_time", 600),
                health_check_interval=db_config.get("health_check_interval", 60),
                preload_pool=db_config.get("preload_pool", True),

                # 监控配置
                enable_monitoring=db_config.get("enable_monitoring", True),
                collect_stats=db_config.get("collect_stats", True),
                log_slow_queries=db_config.get("log_slow_queries", True),
                slow_query_threshold_ms=db_config.get("slow_query_threshold_ms", 500),
                enable_leak_detection=db_config.get("enable_leak_detection", True),
                leak_detection_threshold=db_config.get("leak_detection_threshold", 300)
            )

            # 创建默认连接池
            pool_key = f"default_{config.host}_{config.port}_{config.database}"
            self._pools[pool_key] = ConnectionPool(config, DatabaseType.TIMESCALEDB)
            self._default_pool_key = pool_key
            self._config_cache[pool_key] = config

            logger.info(f"默认数据库连接池已创建: {config.host}:{config.port}/{config.database}")

        except Exception as e:
            logger.error(f"加载默认数据库配置失败: {e}")
            raise

    def get_pool(self, pool_key: str | None = None) -> ConnectionPool:
        """获取连接池"""
        if pool_key is None:
            pool_key = self._default_pool_key

        if pool_key not in self._pools:
            raise ValueError(f"连接池不存在: {pool_key}")

        return self._pools[pool_key]

    def create_pool(self, config: ConnectionConfig, pool_key: str | None = None,
                   db_type: DatabaseType = DatabaseType.TIMESCALEDB) -> str:
        """创建新的连接池"""
        if pool_key is None:
            pool_key = f"{db_type.value}_{config.host}_{config.port}_{config.database}_{int(time.time())}"

        if pool_key in self._pools:
            logger.warning(f"连接池已存在，将替换: {pool_key}")
            self._pools[pool_key].close()

        self._pools[pool_key] = ConnectionPool(config, db_type)
        self._config_cache[pool_key] = config

        logger.info(f"新连接池已创建: {pool_key}")
        return pool_key

    @contextmanager
    def get_connection(self, pool_key: str | None = None):
        """获取数据库连接的上下文管理器"""
        pool = self.get_pool(pool_key)
        with pool.get_connection() as conn:
            yield conn

    def execute(self, query: str, params: Any = None, pool_key: str | None = None) -> Any:
        """执行SQL查询（带死锁重试机制）"""
        start_time = time.time()
        pool = self.get_pool(pool_key)

        # 死锁重试配置
        max_retries = self.deadlock_max_retries

        for attempt in range(max_retries + 1):
            try:
                with pool.get_connection() as conn:
                    with conn.cursor() as cursor:
                        cursor.execute(query, params)

                        # 更新统计
                        with pool._lock:
                            pool.stats.queries_executed += 1

                            # 检查慢查询
                            query_time = (time.time() - start_time) * 1000
                            if query_time > pool.config.slow_query_threshold_ms:
                                pool.stats.slow_queries += 1
                                if pool.config.log_slow_queries:
                                    logger.warning(f"慢查询检测: {query_time:.1f}ms - {query[:100]}...")

                        # 返回结果
                        if cursor.description:
                            return cursor.fetchall()
                        return cursor.rowcount

            except Exception as e:
                # 检查是否为死锁错误
                is_deadlock = self._is_deadlock_error(str(e))

                if is_deadlock and attempt < max_retries:
                    # 计算重试延迟
                    final_delay = self._calculate_retry_delay(attempt)

                    logger.warning(f"检测到死锁，第 {attempt + 1} 次重试，延迟 {final_delay:.3f}秒")
                    time.sleep(final_delay)
                    continue
                else:
                    # 非死锁错误或重试次数用尽
                    with pool._lock:
                        pool.stats.query_errors += 1

                    if is_deadlock:
                        logger.error(f"死锁重试 {max_retries} 次后仍然失败: {e}")
                    else:
                        logger.error(f"执行查询失败: {e}")
                    raise

        # 这里不应该到达，但为了安全起见
        raise Exception("查询执行失败：重试次数用尽")

    def query(self, sql: str, params: Any = None, pool_key: str | None = None) -> List[Dict[str, Any]]:
        """查询数据，返回字典列表"""
        result = self.execute(sql, params, pool_key)
        return result if isinstance(result, list) else []

    def fetch_all(self, sql: str, params: Any = None, pool_key: str | None = None) -> List[Dict[str, Any]]:
        """
        查询所有数据，返回字典列表

        Args:
            sql: SQL查询语句
            params: 查询参数
            pool_key: 连接池键

        Returns:
            List[Dict[str, Any]]: 查询结果列表
        """
        try:
            pool = self.get_pool(pool_key)

            with pool.get_connection() as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    cursor.execute(sql, params)
                    results = cursor.fetchall()

                    # 转换为普通字典列表
                    return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"查询数据失败: {e}")
            logger.error(f"SQL: {sql}")
            logger.error(f"参数: {params}")
            return []

    def fetch_one(self, sql: str, params: Any = None, pool_key: str | None = None) -> Optional[Dict[str, Any]]:
        """
        查询单条数据，返回字典

        Args:
            sql: SQL查询语句
            params: 查询参数
            pool_key: 连接池键

        Returns:
            Optional[Dict[str, Any]]: 查询结果字典，如果没有结果则返回None
        """
        try:
            pool = self.get_pool(pool_key)

            with pool.get_connection() as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    cursor.execute(sql, params)
                    result = cursor.fetchone()

                    # 转换为普通字典
                    return dict(result) if result else None

        except Exception as e:
            logger.error(f"查询单条数据失败: {e}")
            logger.error(f"SQL: {sql}")
            logger.error(f"参数: {params}")
            return None

    def execute_query(self, sql: str, params: Any = None, pool_key: str | None = None) -> int:
        """
        执行SQL语句（通常用于INSERT、UPDATE、DELETE）

        Args:
            sql: SQL语句
            params: 查询参数
            pool_key: 连接池键

        Returns:
            int: 受影响的行数
        """
        try:
            result = self.execute(sql, params, pool_key)
            return result if isinstance(result, int) else 0

        except Exception as e:
            logger.error(f"执行SQL语句失败: {e}")
            logger.error(f"SQL: {sql}")
            logger.error(f"参数: {params}")
            return 0

    def execute_many(self, query: str, params_list: List[Any], pool_key: str | None = None) -> int:
        """批量执行查询（带死锁重试机制）"""
        pool = self.get_pool(pool_key)

        # 死锁重试配置
        max_retries = 3
        base_delay = 0.1  # 基础延迟100ms
        max_delay = 2.0   # 最大延迟2秒

        for attempt in range(max_retries + 1):
            try:
                with pool.get_connection() as conn:
                    with conn.cursor() as cursor:
                        cursor.executemany(query, params_list)

                        # 更新统计
                        with pool._lock:
                            pool.stats.queries_executed += len(params_list)

                        return cursor.rowcount

            except Exception as e:
                # 检查是否为死锁错误
                error_str = str(e).lower()
                is_deadlock = any(keyword in error_str for keyword in [
                    '死锁', 'deadlock', 'deadlockdetected',
                    'lock timeout', 'could not serialize'
                ])

                if is_deadlock and attempt < max_retries:
                    # 计算重试延迟（指数退避 + 随机抖动）
                    import random
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    jitter = random.uniform(0.8, 1.2)  # ±20%随机抖动
                    final_delay = delay * jitter

                    logger.warning(f"检测到死锁，第 {attempt + 1} 次重试，延迟 {final_delay:.3f}秒")
                    time.sleep(final_delay)
                    continue
                else:
                    # 非死锁错误或重试次数用尽
                    with pool._lock:
                        pool.stats.query_errors += 1

                    if is_deadlock:
                        logger.error(f"死锁重试 {max_retries} 次后仍然失败: {e}")
                    else:
                        logger.error(f"批量执行查询失败: {e}")
                    raise

        # 这里不应该到达，但为了安全起见
        raise Exception("批量查询执行失败：重试次数用尽")

    def insert(self, table: str, data: Dict[str, Any], pool_key: str | None = None,
               on_conflict: str | None = None) -> bool:
        """
        插入数据到指定表（带死锁重试机制）

        Args:
            table: 表名
            data: 要插入的数据字典
            pool_key: 连接池键
            on_conflict: 冲突处理策略，如 "DO NOTHING" 或 "DO UPDATE SET ..."

        Returns:
            bool: 插入是否成功
        """
        if not data:
            return True

        # 死锁重试配置
        max_retries = 3
        base_delay = 0.05  # 基础延迟50ms
        max_delay = 1.0    # 最大延迟1秒

        for attempt in range(max_retries + 1):
            try:
                # 构建插入SQL
                columns = list(data.keys())
                placeholders = [f"%({col})s" for col in columns]

                sql = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"

                if on_conflict:
                    sql += f" ON CONFLICT {on_conflict}"

                # 执行插入
                result = self.execute(sql, data, pool_key)
                return result is not None

            except Exception as e:
                # 检查是否为死锁错误
                error_str = str(e).lower()
                is_deadlock = any(keyword in error_str for keyword in [
                    '死锁', 'deadlock', 'deadlockdetected',
                    'lock timeout', 'could not serialize'
                ])

                if is_deadlock and attempt < max_retries:
                    # 计算重试延迟（指数退避 + 随机抖动）
                    import random
                    import time
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    jitter = random.uniform(0.8, 1.2)  # ±20%随机抖动
                    final_delay = delay * jitter

                    logger.warning(f"检测到死锁，第 {attempt + 1} 次重试，延迟 {final_delay:.3f}秒")
                    time.sleep(final_delay)
                    continue
                else:
                    # 非死锁错误或重试次数用尽
                    logger.error(f"插入数据到表 {table} 失败: {e}")
                    if is_deadlock:
                        logger.error(f"死锁重试 {max_retries} 次后仍然失败")
                    return False

        return False

    def insert_many(self, table: str, data_list: List[Dict[str, Any]], pool_key: str | None = None,
                   on_conflict: str | None = None, batch_size: int = 1000) -> bool:
        """
        批量插入数据到指定表（带死锁重试机制）

        Args:
            table: 表名
            data_list: 要插入的数据列表
            pool_key: 连接池键
            on_conflict: 冲突处理策略
            batch_size: 批处理大小

        Returns:
            bool: 插入是否成功
        """
        if not data_list:
            return True

        # 死锁重试配置
        max_retries = 3
        base_delay = 0.1  # 基础延迟100ms
        max_delay = 2.0   # 最大延迟2秒

        for attempt in range(max_retries + 1):
            try:
                # [修复死锁] 使用全局写入锁，确保同一时间只有一个写入操作
                with self._write_lock:
                    # 获取列名（使用第一条记录的键）
                    columns = list(data_list[0].keys())

                # 构建基础SQL（不包含VALUES部分）
                base_sql = f"INSERT INTO {table} ({', '.join(columns)}) VALUES %s"

                if on_conflict:
                    base_sql += f" ON CONFLICT {on_conflict}"

                # 准备数据元组并去重
                data_tuples = []
                seen_keys = set()

                # 定义不同表的唯一键列
                unique_key_columns = {
                    'stock_tick_data': ['trade_time', 'stock_code'],
                    'stock_kline_data': ['trade_date', 'stock_code'],
                    'strategy_signals': ['signal_time', 'stock_code', 'strategy_name'],
                    'dual_channel_fibonacci_signals': ['stock_code', 'break_t2_date']
                }

                for data in data_list:
                    data_tuple = tuple(data.get(col) for col in columns)

                    # 检查是否需要去重
                    if table in unique_key_columns:
                        unique_cols = unique_key_columns[table]
                        # 获取唯一键的索引
                        unique_indices = [columns.index(col) for col in unique_cols if col in columns]
                        if unique_indices:
                            unique_key = tuple(data_tuple[i] for i in unique_indices)
                            if unique_key in seen_keys:
                                logger.debug(f"跳过重复记录 {table}: {unique_key}")
                                continue
                            seen_keys.add(unique_key)

                    data_tuples.append(data_tuple)

                # 如果去重后没有数据，直接返回成功
                if not data_tuples:
                    logger.debug(f"去重后没有数据需要插入到表 {table}")
                    return True

                # [修复死锁] 使用更小的批次，保持autocommit模式
                pool = self.get_pool(pool_key)
                with pool.get_connection() as conn:
                    # 确保连接处于autocommit模式，避免长时间事务
                    if not conn.autocommit:
                        conn.autocommit = True

                    with conn.cursor() as cursor:
                        # 使用PostgreSQL优化的批次大小，充分利用批量插入性能
                        small_batch_size = min(batch_size, 2000)  # 2000条记录一批，充分利用PostgreSQL批量插入优化

                        # 分批插入，每批都是独立的autocommit操作
                        for i in range(0, len(data_tuples), small_batch_size):
                            batch_data = data_tuples[i:i + small_batch_size]
                            try:
                                psycopg2.extras.execute_values(
                                    cursor, base_sql, batch_data,
                                    template=None, page_size=small_batch_size
                                )
                            except Exception as batch_e:
                                # 如果批次失败，记录错误但继续处理其他批次
                                logger.warning(f"批次 {i//small_batch_size + 1} 插入失败: {batch_e}")
                                # 对于autocommit模式，失败的批次不会影响其他批次
                                continue

                    logger.debug(f"成功批量插入 {len(data_tuples)} 条数据到表 {table}")
                    return True

            except Exception as e:
                # 检查是否为死锁错误
                error_str = str(e).lower()
                is_deadlock = any(keyword in error_str for keyword in [
                    '死锁', 'deadlock', 'deadlockdetected',
                    'lock timeout', 'could not serialize'
                ])

                if is_deadlock and attempt < max_retries:
                    # 计算重试延迟（指数退避 + 随机抖动）
                    import random
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    jitter = random.uniform(0.8, 1.2)  # ±20%随机抖动
                    final_delay = delay * jitter

                    logger.warning(f"检测到死锁，第 {attempt + 1} 次重试，延迟 {final_delay:.3f}秒")

                    import time
                    time.sleep(final_delay)
                    continue
                else:
                    # 非死锁错误或重试次数用尽
                    logger.error(f"批量插入数据到表 {table} 失败: {e}")
                    if 'base_sql' in locals():
                        logger.error(f"SQL语句: {base_sql}")
                    if 'data_tuples' in locals():
                        logger.error(f"数据样本: {data_tuples[:2] if data_tuples else '无数据'}")
                    logger.error(f"详细错误: {traceback.format_exc()}")

                    if is_deadlock:
                        logger.error(f"死锁重试 {max_retries} 次后仍然失败")

                    return False

        return False

    def update(self, table: str, data: Dict[str, Any], where_clause: str,
               where_params: Any = None, pool_key: str | None = None) -> int:
        """
        更新表中的数据

        Args:
            table: 表名
            data: 要更新的数据字典
            where_clause: WHERE条件子句
            where_params: WHERE条件参数
            pool_key: 连接池键

        Returns:
            int: 受影响的行数
        """
        if not data:
            return 0

        try:
            # 构建更新SQL
            set_clauses = [f"{col} = %({col})s" for col in data.keys()]
            sql = f"UPDATE {table} SET {', '.join(set_clauses)} WHERE {where_clause}"

            # 合并参数
            params = data.copy()
            if where_params:
                if isinstance(where_params, dict):
                    params.update(where_params)
                else:
                    # 如果where_params不是字典，需要使用位置参数
                    sql = f"UPDATE {table} SET {', '.join(set_clauses)} WHERE {where_clause}"
                    if isinstance(where_params, (list, tuple)):
                        params = list(data.values()) + list(where_params)
                    else:
                        params = list(data.values()) + [where_params]

            # 执行更新
            result = self.execute(sql, params, pool_key)
            return result if isinstance(result, int) else 0

        except Exception as e:
            logger.error(f"更新表 {table} 数据失败: {e}")
            return 0

    def upsert(self, table: str, data: Dict[str, Any], conflict_columns: List[str],
               update_columns: List[str] | None = None, pool_key: str | None = None) -> bool:
        """
        插入或更新数据（UPSERT操作）

        Args:
            table: 表名
            data: 数据字典
            conflict_columns: 冲突检测列
            update_columns: 需要更新的列（如果为None，则更新除冲突列外的所有列）
            pool_key: 连接池键

        Returns:
            bool: 操作是否成功
        """
        if not data:
            return True

        try:
            # 构建插入SQL
            columns = list(data.keys())
            placeholders = [f"%({col})s" for col in columns]

            sql = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"

            # 构建冲突处理
            conflict_clause = f"({', '.join(conflict_columns)})"

            if update_columns is None:
                # 更新除冲突列外的所有列
                update_columns = [col for col in columns if col not in conflict_columns]

            if update_columns:
                update_clauses = [f"{col} = EXCLUDED.{col}" for col in update_columns]
                sql += f" ON CONFLICT {conflict_clause} DO UPDATE SET {', '.join(update_clauses)}"
            else:
                sql += f" ON CONFLICT {conflict_clause} DO NOTHING"

            # 执行UPSERT
            result = self.execute(sql, data, pool_key)
            return result is not None

        except Exception as e:
            logger.error(f"UPSERT操作到表 {table} 失败: {e}")
            return False

    def close_pool(self, pool_key: str):
        """关闭指定连接池"""
        if pool_key in self._pools:
            self._pools[pool_key].close()
            del self._pools[pool_key]
            if pool_key in self._config_cache:
                del self._config_cache[pool_key]
            logger.info(f"连接池已关闭: {pool_key}")

    def close_all(self):
        """关闭所有连接池"""
        for pool_key in list(self._pools.keys()):
            self.close_pool(pool_key)
        logger.info("所有连接池已关闭")

    def get_latest_stock_price(self, stock_code: str) -> float:
        """
        获取股票最新价格

        参数:
            stock_code: 股票代码

        返回:
            最新价格，如果获取失败返回None
        """
        try:
            # 优先从日K线获取最新收盘价
            result = self.fetch_one(
                "SELECT close FROM stock_kline_day WHERE stock_code = %s ORDER BY trade_time DESC LIMIT 1",
                (stock_code,)
            )

            if result:
                return float(result['close']) if isinstance(result, dict) else float(result[0])

            # 如果日K线没有数据，尝试从5分钟K线获取
            result = self.fetch_one(
                "SELECT close FROM stock_kline_5min WHERE stock_code = %s ORDER BY trade_time DESC LIMIT 1",
                (stock_code,)
            )

            if result:
                return float(result['close']) if isinstance(result, dict) else float(result[0])

            return None

        except Exception as e:
            logger.error(f"获取股票 {stock_code} 最新价格失败: {e}")
            return None

    # =====================================================
    # 股票数据获取方法 - 统一数据获取接口
    # =====================================================

    def get_all_stocks_with_names(self) -> List[Dict[str, str]]:
        """
        获取整体市场股票列表和名称（从stock_info表）

        返回:
            包含股票代码和名称的字典列表
        """
        try:
            result = self.fetch_all("SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code")

            stocks = []
            for row in result:
                stocks.append({
                    'stock_code': row['stock_code'] if isinstance(row, dict) else row[0],
                    'stock_name': (row['stock_name'] if isinstance(row, dict) else row[1]) or f'股票{row["stock_code"] if isinstance(row, dict) else row[0]}'
                })

            logger.info(f"获取到 {len(stocks)} 只市场股票")
            return stocks

        except Exception as e:
            logger.error(f"获取市场股票列表失败: {e}")
            return []

    def get_active_technical_stocks(self) -> List[Dict[str, str]]:
        """
        获取技术指标活跃股票列表（is_active=true）

        返回:
            包含股票代码和名称的字典列表
        """
        try:
            result = self.fetch_all("SELECT DISTINCT stock_code, stock_code as stock_name FROM stock_kline_day WHERE trade_time >= CURRENT_DATE - INTERVAL '30 days' ORDER BY stock_code")

            stocks = []
            for row in result:
                stocks.append({
                    'stock_code': row['stock_code'] if isinstance(row, dict) else row[0],
                    'stock_name': (row['stock_name'] if isinstance(row, dict) else row[1]) or f'股票{row["stock_code"] if isinstance(row, dict) else row[0]}'
                })

            logger.info(f"获取到 {len(stocks)} 只活跃技术指标股票")
            return stocks

        except Exception as e:
            logger.error(f"获取活跃技术指标股票列表失败: {e}")
            return []

    def get_stock_kline_data(self, stock_code: str, min_length: int = 700) -> Optional[List[Dict[str, Any]]]:
        """
        获取单只股票的历史K线数据 - 优化版本

        主要优化：
        1. 支持获取所有历史数据（当min_length=0时）
        2. 使用更高效的查询策略
        3. 增强数据验证和错误处理
        4. 为talib技术指标计算提供充足的历史数据

        参数:
            stock_code: 股票代码
            min_length: 最小数据长度，设置为0时获取所有可用数据

        返回:
            K线数据列表或None，按时间升序排列
        """
        try:
            # 优化：根据min_length选择不同的查询策略
            if min_length == 0:
                # 获取所有历史数据，用于技术指标计算
                # 这样可以确保长周期EMA（如576、676周期）有足够的历史数据
                data_result = self.fetch_all(
                    "SELECT trade_time, open, high, low, close, volume, amount FROM stock_kline_day WHERE stock_code = %s ORDER BY trade_time ASC",
                    (stock_code,)
                )

                if data_result:
                    logger.debug(f"股票 {stock_code} 获取到所有历史数据: {len(data_result)} 条")
                    return data_result
                else:
                    logger.warning(f"股票 {stock_code} 没有历史数据")
                    return None
            else:
                # 获取指定长度的数据
                data_result = self.fetch_all(
                    "SELECT trade_time, open, high, low, close, volume, amount FROM stock_kline_day WHERE stock_code = %s ORDER BY trade_time DESC LIMIT %s",
                    (stock_code, min_length)
                )

                if data_result and len(data_result) >= min_length:
                    # 将数据按时间升序排列，便于技术指标计算
                    data_result.reverse()
                    return data_result
                else:
                    logger.warning(f"股票 {stock_code} 数据不足: {len(data_result) if data_result else 0} < {min_length}")
                    return None

        except Exception as e:
            logger.error(f"获取股票 {stock_code} K线数据失败: {e}")
            logger.error(f"查询参数: min_length={min_length}")
            return None

    def get_multiple_stocks_kline_data(self, stock_list: List[str] = None, min_length: int = 700,
                                     max_stocks: int = 100) -> Dict[str, List[Dict[str, Any]]]:
        """
        批量获取多只股票的K线数据

        参数:
            stock_list: 股票代码列表，如果为None则使用活跃股票列表
            min_length: 最小数据长度
            max_stocks: 最大处理股票数量

        返回:
            股票代码到K线数据列表的字典
        """
        try:
            if stock_list is None:
                # 使用活跃股票列表
                result = self.fetch_all("SELECT DISTINCT stock_code FROM stock_kline_day WHERE trade_time >= CURRENT_DATE - INTERVAL '7 days' ORDER BY stock_code")
                stock_list = [row['stock_code'] if isinstance(row, dict) else row[0] for row in result]

            # 限制处理数量
            stock_list = stock_list[:max_stocks]

            stock_data = {}
            for stock_code in stock_list:
                data = self.get_stock_kline_data(stock_code, min_length)
                if data is not None:
                    stock_data[stock_code] = data

            logger.info(f"成功获取 {len(stock_data)} 只股票的K线数据")
            return stock_data

        except Exception as e:
            logger.error(f"批量获取股票K线数据失败: {e}")
            return {}

    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有连接池的统计信息"""
        stats = {}
        for pool_key, pool in self._pools.items():
            stats[pool_key] = pool.get_stats()
        return stats


# 全局函数
def get_db_manager() -> UnifiedDatabaseManager:
    """获取数据库管理器实例"""
    return UnifiedDatabaseManager.get_instance()


def get_db_config(for_pool: bool = True) -> Dict[str, Any]:
    """
    获取数据库配置，从config/main.toml的[database]部分读取

    Args:
        for_pool: 是否用于连接池。如果为True，包含pool_size参数；如果为False，不包含(用于DSN连接)

    Returns:
        Dict[str, Any]: 数据库连接配置
    """
    db_config = get_config("database")

    # 优先使用统一参数名，如果不存在则使用兼容参数名
    connection_params = {
        "host": db_config.get("host", db_config.get("timescaledb_host", "localhost")),
        "port": db_config.get("port", db_config.get("timescaledb_port", 5432)),
        "database": db_config.get("database", db_config.get("timescaledb_database", "postgres")),
        "user": db_config.get("user", db_config.get("timescaledb_user", "postgres")),
        "password": db_config.get("password", db_config.get("timescaledb_password", "postgres")),
        "connect_timeout": db_config.get("connect_timeout", 10),
        "client_encoding": db_config.get("client_encoding", "UTF8"),
    }

    # 仅在用于连接池时添加pool_size参数
    if for_pool:
        connection_params["pool_size"] = db_config.get("max_connections", db_config.get("pool_size", 20))
        logger.debug(f"读取数据库配置(连接池): {connection_params['host']}:{connection_params['port']}/{connection_params['database']}, 连接池大小: {connection_params['pool_size']}")
    else:
        logger.debug(f"读取数据库配置(DSN连接): {connection_params['host']}:{connection_params['port']}/{connection_params['database']}")

    return connection_params


def close_all_db_connections():
    """关闭所有数据库连接"""
    manager = get_db_manager()
    manager.close_all()
