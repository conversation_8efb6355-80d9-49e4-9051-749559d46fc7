#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线计算器 - 基于Tick数据计算周期K线

该模块提供高性能的K线计算功能，支持多种周期的K线生成。
采用纯Python实现，直接从数据库读取tick数据计算K线。

核心功能：
1. 从tick数据计算OHLC价格
2. 聚合成交量和成交额
3. 时间对齐和标准化
4. 支持多种K线周期（5分钟、日线、2小时等）
5. 数据验证和异常处理

作者: QuantFM Team
创建时间: 2025-07-13
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging

from utils.logger import get_logger


class KlineCalculator:
    """
    K线计算器
    
    基于tick数据计算各种周期的K线数据，保留原有计算逻辑
    """
    
    def __init__(self, db_manager):
        """
        初始化K线计算器
        
        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger = get_logger(f"KlineCalculator_{id(self)}")
        
        # K线周期映射
        self.period_map = {
            '1min': '1min',
            '5min': '5min', 
            '15min': '15min',
            '30min': '30min',
            '1hour': '1H',
            '2hour': '2H',
            '1day': '1D'
        }
        
        # 时间对齐配置
        self.time_alignment = {
            '5min': {'freq': 5, 'unit': 'minute'},
            '15min': {'freq': 15, 'unit': 'minute'},
            '30min': {'freq': 30, 'unit': 'minute'},
            '1hour': {'freq': 1, 'unit': 'hour'},
            '2hour': {'freq': 2, 'unit': 'hour'},
            '1day': {'freq': 1, 'unit': 'day'}
        }
        
        self.logger.info("K线计算器初始化完成")
    
    def calculate_klines_from_ticks(self, stock_code: str, period: str, 
                                   start_time: Optional[datetime] = None,
                                   end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        从tick数据计算K线
        
        Args:
            stock_code: 股票代码
            period: K线周期 ('5min', '1day', '2hour' 等)
            start_time: 开始时间（可选）
            end_time: 结束时间（可选）
            
        Returns:
            K线数据列表
        """
        try:
            # 获取tick数据
            tick_data = self._get_tick_data(stock_code, start_time, end_time)
            
            if not tick_data:
                self.logger.debug(f"股票 {stock_code} 没有tick数据")
                return []
            
            # 转换为DataFrame
            df = pd.DataFrame(tick_data)
            
            # 数据预处理
            df = self._preprocess_tick_data(df)
            
            if df.empty:
                return []
            
            # 计算K线
            klines = self._calculate_klines(df, period)
            
            # 数据验证
            validated_klines = self._validate_klines(klines, stock_code, period)
            
            return validated_klines
            
        except Exception as e:
            self.logger.error(f"计算股票 {stock_code} {period} K线失败: {e}")
            return []
    
    def calculate_realtime_klines(self, tick_df: pd.DataFrame, period: str) -> Dict[str, List[Dict]]:
        """
        基于实时tick数据计算K线
        
        Args:
            tick_df: 实时tick数据DataFrame
            period: K线周期
            
        Returns:
            {stock_code: [kline_data]} 格式的字典
        """
        try:
            if tick_df.empty:
                return {}
            
            result = {}
            
            # 按股票代码分组处理
            for stock_code in tick_df['code'].unique():
                stock_ticks = tick_df[tick_df['code'] == stock_code].copy()
                
                # 数据预处理
                stock_ticks = self._preprocess_tick_data(stock_ticks, use_code_column=True)
                
                if not stock_ticks.empty:
                    # 计算K线
                    klines = self._calculate_klines(stock_ticks, period)
                    
                    # 数据验证
                    validated_klines = self._validate_klines(klines, stock_code, period)
                    
                    if validated_klines:
                        result[stock_code] = validated_klines
            
            return result
            
        except Exception as e:
            self.logger.error(f"计算实时 {period} K线失败: {e}")
            return {}
    
    def _get_tick_data(self, stock_code: str, start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None) -> List[Dict]:
        """
        从数据库获取tick数据
        
        Args:
            stock_code: 股票代码
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            tick数据列表
        """
        try:
            # 构建查询条件
            conditions = ["stock_code = %s"]
            params = [stock_code]
            
            # 默认查询当天数据
            if start_time is None and end_time is None:
                conditions.append("trade_date = CURRENT_DATE")
            else:
                if start_time:
                    conditions.append("trade_time >= %s")
                    params.append(start_time)
                if end_time:
                    conditions.append("trade_time <= %s")
                    params.append(end_time)
            
            # 构建SQL查询
            sql = f"""
            SELECT trade_time, price, volume, amount, cur_vol
            FROM stock_tick_data 
            WHERE {' AND '.join(conditions)}
            ORDER BY trade_time ASC
            """
            
            # 执行查询
            result = self.db_manager.fetch_all(sql, params)
            
            # 转换为字典列表
            tick_data = []
            for row in result:
                tick = {
                    'trade_time': row['trade_time'] if isinstance(row, dict) else row[0],
                    'price': row['price'] if isinstance(row, dict) else row[1],
                    'volume': row['volume'] if isinstance(row, dict) else row[2],
                    'amount': row['amount'] if isinstance(row, dict) else row[3],
                    'cur_vol': row['cur_vol'] if isinstance(row, dict) else row[4]
                }
                tick_data.append(tick)
            
            return tick_data
            
        except Exception as e:
            self.logger.error(f"获取股票 {stock_code} tick数据失败: {e}")
            return []
    
    def _preprocess_tick_data(self, df: pd.DataFrame, use_code_column: bool = False) -> pd.DataFrame:
        """
        预处理tick数据
        
        Args:
            df: tick数据DataFrame
            use_code_column: 是否使用code列作为股票代码
            
        Returns:
            预处理后的DataFrame
        """
        try:
            if df.empty:
                return df
            
            # 复制数据避免修改原始数据
            df = df.copy()
            
            # 统一列名
            if use_code_column and 'code' in df.columns:
                df['stock_code'] = df['code']
            
            # 确保必要的列存在
            required_columns = ['trade_time', 'price', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    self.logger.warning(f"缺少必要列: {col}")
                    return pd.DataFrame()
            
            # 数据类型转换
            df['price'] = pd.to_numeric(df['price'], errors='coerce')
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
            
            # 处理amount列
            if 'amount' in df.columns:
                df['amount'] = pd.to_numeric(df['amount'], errors='coerce')
            else:
                # 如果没有amount列，用price*volume估算
                df['amount'] = df['price'] * df['volume']
            
            # 处理cur_vol列
            if 'cur_vol' not in df.columns:
                df['cur_vol'] = df['volume']
            else:
                df['cur_vol'] = pd.to_numeric(df['cur_vol'], errors='coerce')
            
            # 计算成交额：cur_vol * price
            df['amount_calc'] = df['cur_vol'] * df['price']
            
            # 时间处理
            if not pd.api.types.is_datetime64_any_dtype(df['trade_time']):
                df['trade_time'] = pd.to_datetime(df['trade_time'])
            
            # 数据清洗
            df = df.dropna(subset=['trade_time', 'price', 'volume'])
            df = df[df['price'] > 0]
            df = df[df['volume'] > 0]
            
            # 按时间排序
            df = df.sort_values('trade_time')
            
            # 设置时间索引
            df.set_index('trade_time', inplace=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"预处理tick数据失败: {e}")
            return pd.DataFrame()
    
    def _calculate_klines(self, df: pd.DataFrame, period: str) -> List[Dict[str, Any]]:
        """
        计算K线数据
        
        Args:
            df: 预处理后的tick数据DataFrame
            period: K线周期
            
        Returns:
            K线数据列表
        """
        try:
            if df.empty:
                return []
            
            # 获取pandas重采样频率
            pandas_freq = self.period_map.get(period)
            if not pandas_freq:
                self.logger.warning(f"不支持的K线周期: {period}")
                return []
            
            # 重采样计算OHLC
            resampled = df.resample(pandas_freq).agg({
                'price': ['first', 'max', 'min', 'last'],  # OHLC
                'cur_vol': 'sum',  # 成交量：cur_vol字段累加
                'amount_calc': 'sum'  # 成交额：cur_vol*price累加
            })
            
            # 展平多级列名
            resampled.columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            
            # 删除空数据
            resampled = resampled.dropna()
            
            # 转换为字典列表
            klines = []
            for timestamp, row in resampled.iterrows():
                kline = {
                    'trade_time': timestamp,
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': int(row['volume']),
                    'amount': float(row['amount'])
                }
                klines.append(kline)
            
            return klines
            
        except Exception as e:
            self.logger.error(f"计算 {period} K线失败: {e}")
            return []

    def _validate_klines(self, klines: List[Dict[str, Any]], stock_code: str, period: str) -> List[Dict[str, Any]]:
        """
        验证K线数据的有效性

        Args:
            klines: K线数据列表
            stock_code: 股票代码
            period: K线周期

        Returns:
            验证后的K线数据列表
        """
        try:
            if not klines:
                return []

            validated_klines = []
            for kline in klines:
                # 基本数据验证
                if (kline['open'] > 0 and kline['high'] > 0 and 
                    kline['low'] > 0 and kline['close'] > 0 and
                    kline['volume'] >= 0 and kline['amount'] >= 0):
                    
                    # OHLC逻辑验证
                    if (kline['low'] <= kline['open'] <= kline['high'] and
                        kline['low'] <= kline['close'] <= kline['high']):
                        validated_klines.append(kline)
                    else:
                        self.logger.warning(f"股票 {stock_code} OHLC逻辑错误: {kline}")
                else:
                    self.logger.warning(f"股票 {stock_code} 数据无效: {kline}")

            return validated_klines

        except Exception as e:
            self.logger.error(f"验证K线数据失败: {e}")
            return []

    def get_aligned_time(self, timestamp: datetime, period: str) -> datetime:
        """
        获取对齐的时间点

        Args:
            timestamp: 原始时间戳
            period: K线周期

        Returns:
            对齐后的时间点
        """
        try:
            alignment = self.time_alignment.get(period)
            if not alignment:
                return timestamp

            freq = alignment['freq']
            unit = alignment['unit']

            if unit == 'minute':
                # 分钟向上对齐：9:35:01 -> 9:40:00
                aligned_minute = ((timestamp.minute // freq) + 1) * freq
                if aligned_minute >= 60:
                    # 处理跨小时情况
                    return timestamp.replace(hour=timestamp.hour + 1, minute=0, second=0, microsecond=0)
                else:
                    return timestamp.replace(minute=aligned_minute, second=0, microsecond=0)
            elif unit == 'hour':
                # 小时对齐
                aligned_hour = (timestamp.hour // freq) * freq
                return timestamp.replace(hour=aligned_hour, minute=0, second=0, microsecond=0)
            elif unit == 'day':
                # 日对齐
                return timestamp.replace(hour=0, minute=0, second=0, microsecond=0)
            else:
                return timestamp

        except Exception as e:
            self.logger.error(f"时间对齐失败: {e}")
            return timestamp

    def calculate_incremental_kline(self, stock_code: str, period: str = '5min',
                                   tick_data: List[Dict] = None) -> Optional[Dict[str, Any]]:
        """
        增量计算K线 - 专为实时处理优化

        Args:
            stock_code: 股票代码
            period: K线周期
            tick_data: 可选的tick数据，如果不提供则从数据库获取

        Returns:
            当前周期的K线数据
        """
        try:
            current_time = datetime.now()

            # 计算当前周期的开始时间
            period_start = self._get_period_start_time(current_time, period)

            # 如果提供了tick数据，直接使用；否则从数据库获取
            if tick_data:
                df = pd.DataFrame(tick_data)
            else:
                # 获取当前周期的tick数据
                end_time = current_time
                start_time = period_start

                tick_data = self._get_tick_data(stock_code, start_time, end_time)
                if not tick_data:
                    return None

                df = pd.DataFrame(tick_data)

            # 数据预处理
            df = self._preprocess_tick_data(df)
            if df.empty:
                return None

            # 过滤当前周期的数据
            df = df[df['trade_time'] >= period_start]
            if df.empty:
                return None

            # 计算K线
            kline = self._calculate_single_period_kline(df, period_start, period)

            if kline:
                kline['is_complete'] = self._is_period_complete(current_time, period)
                kline['last_update'] = current_time

            return kline

        except Exception as e:
            self.logger.error(f"增量计算K线失败 {stock_code}: {e}")
            return None

    def _get_period_start_time(self, current_time: datetime, period: str) -> datetime:
        """
        获取当前周期的开始时间

        Args:
            current_time: 当前时间
            period: 周期

        Returns:
            周期开始时间
        """
        try:
            if period == '5min':
                # 5分钟对齐
                minute = (current_time.minute // 5) * 5
                return current_time.replace(minute=minute, second=0, microsecond=0)
            elif period == '15min':
                # 15分钟对齐
                minute = (current_time.minute // 15) * 15
                return current_time.replace(minute=minute, second=0, microsecond=0)
            elif period == '30min':
                # 30分钟对齐
                minute = (current_time.minute // 30) * 30
                return current_time.replace(minute=minute, second=0, microsecond=0)
            elif period == '1hour':
                # 小时对齐
                return current_time.replace(minute=0, second=0, microsecond=0)
            elif period == '2hour':
                # 2小时对齐
                hour = (current_time.hour // 2) * 2
                return current_time.replace(hour=hour, minute=0, second=0, microsecond=0)
            elif period == '1day':
                # 日对齐
                return current_time.replace(hour=0, minute=0, second=0, microsecond=0)
            else:
                return current_time.replace(second=0, microsecond=0)

        except Exception as e:
            self.logger.error(f"计算周期开始时间失败: {e}")
            return current_time.replace(second=0, microsecond=0)

    def _is_period_complete(self, current_time: datetime, period: str) -> bool:
        """
        判断当前周期是否完成

        Args:
            current_time: 当前时间
            period: 周期

        Returns:
            周期是否完成
        """
        try:
            period_start = self._get_period_start_time(current_time, period)

            if period == '5min':
                period_end = period_start + timedelta(minutes=5)
            elif period == '15min':
                period_end = period_start + timedelta(minutes=15)
            elif period == '30min':
                period_end = period_start + timedelta(minutes=30)
            elif period == '1hour':
                period_end = period_start + timedelta(hours=1)
            elif period == '2hour':
                period_end = period_start + timedelta(hours=2)
            elif period == '1day':
                period_end = period_start + timedelta(days=1)
            else:
                return False

            return current_time >= period_end

        except Exception as e:
            self.logger.error(f"判断周期完成状态失败: {e}")
            return False

    def _calculate_single_period_kline(self, df: pd.DataFrame, period_start: datetime, period: str) -> Optional[Dict]:
        """
        计算单个周期的K线

        Args:
            df: tick数据DataFrame
            period_start: 周期开始时间
            period: 周期类型

        Returns:
            K线数据
        """
        try:
            if df.empty:
                return None

            # 按时间排序
            df = df.sort_values('trade_time')

            # 计算OHLC
            open_price = df.iloc[0]['price']  # 第一个价格
            high_price = df['price'].max()
            low_price = df['price'].min()
            close_price = df.iloc[-1]['price']  # 最后一个价格

            # 计算成交量和成交额
            volume = df['cur_vol'].sum()
            amount = df['amount'].sum() if 'amount' in df.columns else volume * close_price

            return {
                'period_start': period_start,
                'period': period,
                'open': float(open_price),
                'high': float(high_price),
                'low': float(low_price),
                'close': float(close_price),
                'volume': int(volume),
                'amount': float(amount),
                'tick_count': len(df)
            }

        except Exception as e:
            self.logger.error(f"计算单周期K线失败: {e}")
            return None

    def get_stats(self) -> Dict[str, Any]:
        """
        获取K线计算器统计信息

        Returns:
            统计信息字典
        """
        return {
            'supported_periods': list(self.period_map.keys()),
            'time_alignment_config': self.time_alignment,
            'calculator_id': id(self),
            'incremental_support': True
        }
