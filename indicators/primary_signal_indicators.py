"""
主信号技术指标计算模块

专门为 stock_primary_signals 表提供完整的技术指标计算功能。
主要功能：
- 计算所有主信号表需要的技术指标
- 提供标准化的指标计算接口
- 支持批量计算和缓存优化
- 集成现有的指标计算模块

Author: System
Date: 2025-08-18
"""

import pandas as pd
import numpy as np
import talib
from typing import Dict, Optional, List, Any
import logging
from datetime import datetime

from .talib_wrapper import calculate_ema_series

logger = logging.getLogger(__name__)


def calculate_all_primary_indicators(df: pd.DataFrame, 
                                   stock_code: str = "",
                                   use_cache: bool = True) -> Dict[str, Any]:
    """
    计算主信号表所需的所有技术指标
    
    Args:
        df: 包含OHLCV数据的DataFrame
        stock_code: 股票代码（用于日志）
        use_cache: 是否使用缓存
        
    Returns:
        包含所有技术指标的字典
        
    Raises:
        ValueError: 当输入数据无效时
    """
    if df.empty or len(df) < 100:
        raise ValueError(f"数据不足，需要至少100条记录，当前: {len(df)}")
    
    required_columns = ['open', 'high', 'low', 'close', 'volume']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"缺少必要的数据列: {missing_columns}")
    
    try:
        indicators = {}
        
        # 1. EMA系列指标
        ema_indicators = calculate_ema_indicators(df)
        indicators.update(ema_indicators)
        
        # 2. 价格指标 (TWAP, VWAP)
        price_indicators = calculate_price_indicators(df)
        indicators.update(price_indicators)
        
        # 3. 技术分析指标
        technical_indicators = calculate_technical_indicators(df)
        indicators.update(technical_indicators)
        
        # 4. 斐波那契指标
        fibonacci_indicators = calculate_fibonacci_indicators(df)
        indicators.update(fibonacci_indicators)
        
        # 5. 枢轴点位
        pivot_indicators = calculate_pivot_indicators(df)
        indicators.update(pivot_indicators)
        
        # 6. 趋势和结构指标
        structure_indicators = calculate_structure_indicators(df)
        indicators.update(structure_indicators)
        
        logger.debug(f"股票 {stock_code} 技术指标计算完成，共 {len(indicators)} 个指标")
        return indicators
        
    except Exception as e:
        logger.error(f"计算股票 {stock_code} 技术指标失败: {e}")
        raise


def calculate_ema_indicators(df: pd.DataFrame) -> Dict[str, float]:
    """计算EMA系列指标"""
    try:
        # 使用现有的EMA计算模块
        ema_periods = [12, 62, 144, 169, 377, 576, 676]
        ema_df = calculate_ema_series(df, ema_periods, 'close')
        
        indicators = {}
        for period in ema_periods:
            col_name = f'ema_{period}'
            if col_name in ema_df.columns:
                indicators[f'ema{period}'] = float(ema_df[col_name].iloc[-1])
            else:
                # 备用计算方法
                indicators[f'ema{period}'] = float(df['close'].ewm(span=period).mean().iloc[-1])
        
        return indicators
        
    except Exception as e:
        logger.error(f"计算EMA指标失败: {e}")
        # 返回默认值
        return {f'ema{period}': 0.0 for period in [12, 62, 144, 169, 377, 576, 676]}


def calculate_price_indicators(df: pd.DataFrame) -> Dict[str, float]:
    """计算价格相关指标"""
    try:
        indicators = {}
        
        # VWAP (成交量加权平均价格)
        vwap = (df['close'] * df['volume']).cumsum() / df['volume'].cumsum()
        indicators['vwap'] = float(vwap.iloc[-1])
        
        # TWAP (时间加权平均价格) - 使用20日简单移动平均作为近似
        twap = df['close'].rolling(window=20).mean()
        indicators['twap'] = float(twap.iloc[-1])
        
        return indicators
        
    except Exception as e:
        logger.error(f"计算价格指标失败: {e}")
        return {'vwap': 0.0, 'twap': 0.0}


def calculate_technical_indicators(df: pd.DataFrame) -> Dict[str, float]:
    """
    计算技术分析指标

    增强的技术指标计算，包含更好的错误处理和数据验证

    Args:
        df: 包含OHLCV数据的DataFrame

    Returns:
        技术指标字典
    """
    try:
        indicators = {}

        # 确保所有数据都是正确的类型（talib要求float64）
        close_prices = df['close'].values.astype(np.float64)
        high_prices = df['high'].values.astype(np.float64)
        low_prices = df['low'].values.astype(np.float64)
        open_prices = df['open'].values.astype(np.float64)
        volume_data = df['volume'].values.astype(np.float64)  # 关键修复：volume转为float64

        # 确保数据长度足够
        if len(close_prices) < 50:
            logger.warning(f"数据长度不足 ({len(close_prices)})，某些指标可能不准确")

        # MACD指标 - 使用更保守的参数
        try:
            macd_line, macd_signal, macd_hist = talib.MACD(
                close_prices,
                fastperiod=12,
                slowperiod=26,
                signalperiod=9
            )

            # 检查并处理NaN值
            if len(macd_line) > 0 and not np.isnan(macd_line[-1]):
                indicators['macd'] = float(macd_line[-1])
            else:
                # 使用简单的EMA差值作为备用
                ema12 = df['close'].ewm(span=12).mean().iloc[-1]
                ema26 = df['close'].ewm(span=26).mean().iloc[-1]
                indicators['macd'] = float(ema12 - ema26)

            if len(macd_signal) > 0 and not np.isnan(macd_signal[-1]):
                indicators['macd_signal'] = float(macd_signal[-1])
            else:
                indicators['macd_signal'] = 0.0

            if len(macd_hist) > 0 and not np.isnan(macd_hist[-1]):
                indicators['macd_hist'] = float(macd_hist[-1])
            else:
                indicators['macd_hist'] = indicators['macd'] - indicators['macd_signal']

        except Exception as e:
            logger.warning(f"MACD计算失败: {e}，使用备用方法")
            indicators['macd'] = 0.0
            indicators['macd_signal'] = 0.0
            indicators['macd_hist'] = 0.0

        # RSI指标 - 增强计算
        try:
            rsi = talib.RSI(close_prices, timeperiod=14)

            if len(rsi) > 0 and not np.isnan(rsi[-1]):
                indicators['rsi'] = float(rsi[-1])
            else:
                # 使用手动RSI计算作为备用
                indicators['rsi'] = _calculate_rsi_manual(df['close'], 14)

        except Exception as e:
            logger.warning(f"RSI计算失败: {e}，使用备用方法")
            indicators['rsi'] = _calculate_rsi_manual(df['close'], 14)
        
        # 布林带
        bb_upper, bb_middle, bb_lower = talib.BBANDS(close_prices, timeperiod=20)
        indicators['boll_upper'] = float(bb_upper[-1]) if not np.isnan(bb_upper[-1]) else 0.0
        indicators['boll_middle'] = float(bb_middle[-1]) if not np.isnan(bb_middle[-1]) else 0.0
        indicators['boll_lower'] = float(bb_lower[-1]) if not np.isnan(bb_lower[-1]) else 0.0

        # ATR (平均真实波幅)
        atr = talib.ATR(high_prices, low_prices, close_prices, timeperiod=14)
        indicators['atr'] = float(atr[-1]) if not np.isnan(atr[-1]) else 0.0

        # ADX (平均方向指数)
        adx = talib.ADX(high_prices, low_prices, close_prices, timeperiod=14)
        indicators['adx'] = float(adx[-1]) if not np.isnan(adx[-1]) else 0.0

        # Aroon指标
        aroon_down, aroon_up = talib.AROON(high_prices, low_prices, timeperiod=14)
        indicators['aroon_up'] = float(aroon_up[-1]) if not np.isnan(aroon_up[-1]) else 0.0
        indicators['aroon_down'] = float(aroon_down[-1]) if not np.isnan(aroon_down[-1]) else 0.0

        # OBV (能量潮) - 使用修复后的volume数据
        obv = talib.OBV(close_prices, volume_data)
        indicators['obv'] = float(obv[-1]) if not np.isnan(obv[-1]) else 0.0

        # MFI (资金流量指数) - 使用修复后的volume数据
        mfi = talib.MFI(high_prices, low_prices, close_prices, volume_data, timeperiod=14)
        indicators['mfi'] = float(mfi[-1]) if not np.isnan(mfi[-1]) else 0.0
        
        return indicators
        
    except Exception as e:
        logger.error(f"计算技术指标失败: {e}")
        return {
            'macd': 0.0, 'macd_signal': 0.0, 'macd_hist': 0.0,
            'rsi': 0.0, 'boll_upper': 0.0, 'boll_middle': 0.0, 'boll_lower': 0.0,
            'atr': 0.0, 'adx': 0.0, 'aroon_up': 0.0, 'aroon_down': 0.0,
            'obv': 0.0, 'mfi': 0.0
        }


def _calculate_rsi_manual(prices: pd.Series, period: int = 14) -> float:
    """
    手动计算RSI指标

    当talib计算失败时使用的备用方法

    Args:
        prices: 价格序列
        period: 计算周期

    Returns:
        RSI值
    """
    try:
        if len(prices) < period + 1:
            return 50.0  # 数据不足时返回中性值

        # 计算价格变化
        delta = prices.diff()

        # 分离上涨和下跌
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        # 计算平均收益和损失
        avg_gain = gain.rolling(window=period).mean().iloc[-1]
        avg_loss = loss.rolling(window=period).mean().iloc[-1]

        # 计算RSI
        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return float(rsi) if not np.isnan(rsi) else 50.0

    except Exception as e:
        logger.warning(f"手动RSI计算失败: {e}")
        return 50.0


def calculate_fibonacci_indicators(df: pd.DataFrame,
                                start_low_price: Optional[float] = None,
                                lookback_period: int = 100) -> Dict[str, float]:
    """
    计算斐波那契回撤位和扩展位

    如果提供了start_low_price，将使用增强的斐波那契计算；
    否则使用传统的高低点计算方法。

    Args:
        df: K线数据DataFrame
        start_low_price: 起始低点价格（来自信号表）
        lookback_period: 回看周期

    Returns:
        斐波那契指标字典
    """
    try:
        if start_low_price is not None:
            # 使用增强的斐波那契计算（已合并到fibonacci_comprehensive.py）
            from .fibonacci_comprehensive import calculate_fibonacci_levels_enhanced
            return calculate_fibonacci_levels_enhanced(df, start_low_price, lookback_period=lookback_period)
        else:
            # 使用传统方法
            return _calculate_fibonacci_traditional(df, lookback_period)

    except Exception as e:
        logger.error(f"斐波那契指标计算失败: {e}")
        return _calculate_fibonacci_fallback(df)


def _calculate_fibonacci_traditional(df: pd.DataFrame, lookback_period: int = 50) -> Dict[str, float]:
    """传统的斐波那契计算方法"""
    try:
        # 获取最近一段时间的高低点
        recent_data = df.tail(lookback_period)
        high_price = recent_data['high'].max()
        low_price = recent_data['low'].min()

        # 直接计算斐波那契回撤位和扩展位
        diff = high_price - low_price
        indicators = {
            'fib_level_0_236': float(high_price - diff * 0.236),
            'fib_level_0_382': float(high_price - diff * 0.382),
            'fib_level_0_5': float(high_price - diff * 0.5),
            'fib_level_0_618': float(high_price - diff * 0.618),
            'fib_level_0_786': float(high_price - diff * 0.786),
            'fib_level_1_272': float(high_price + diff * 0.272),
            'fib_level_1_382': float(high_price + diff * 0.382),
            'fib_level_1_618': float(high_price + diff * 0.618),
            'f_fib_level_1_272': float(high_price + diff * 1.272),
            'f_fib_level_1_382': float(high_price + diff * 1.382),
            'f_fib_level_1_618': float(high_price + diff * 1.618)
        }
        
        return indicators
        
    except Exception as e:
        logger.error(f"计算斐波那契指标失败: {e}")
        return {
            'fib_level_0_236': 0.0, 'fib_level_0_382': 0.0, 'fib_level_0_5': 0.0,
            'fib_level_0_618': 0.0, 'fib_level_0_786': 0.0, 'fib_level_1_272': 0.0,
            'fib_level_1_382': 0.0, 'fib_level_1_618': 0.0, 'f_fib_level_1_272': 0.0,
            'f_fib_level_1_382': 0.0, 'f_fib_level_1_618': 0.0
        }


def calculate_pivot_indicators(df: pd.DataFrame) -> Dict[str, float]:
    """计算枢轴点位"""
    try:
        if len(df) < 2:
            return {'pivot_point': 0.0, 'pivot_r1': 0.0, 'pivot_s1': 0.0, 'pivot_r2': 0.0, 'pivot_s2': 0.0}
        
        # 使用前一日的高低收计算枢轴点
        prev_high = df['high'].iloc[-2]
        prev_low = df['low'].iloc[-2]
        prev_close = df['close'].iloc[-2]
        
        # 直接计算枢轴点位
        pivot = (prev_high + prev_low + prev_close) / 3
        indicators = {
            'pivot_point': float(pivot),
            'pivot_r1': float(2 * pivot - prev_low),
            'pivot_s1': float(2 * pivot - prev_high),
            'pivot_r2': float(pivot + (prev_high - prev_low)),
            'pivot_s2': float(pivot - (prev_high - prev_low))
        }
        
        return indicators
        
    except Exception as e:
        logger.error(f"计算枢轴点位失败: {e}")
        return {'pivot_point': 0.0, 'pivot_r1': 0.0, 'pivot_s1': 0.0, 'pivot_r2': 0.0, 'pivot_s2': 0.0}


def calculate_structure_indicators(df: pd.DataFrame) -> Dict[str, Any]:
    """计算结构和趋势指标"""
    try:
        indicators = {}
        
        # 突破幅度 (最近20日高低点幅度)
        recent_high = df['high'].rolling(window=20).max().iloc[-1]
        recent_low = df['low'].rolling(window=20).min().iloc[-1]
        if recent_low > 0:
            indicators['breakout_amplitude'] = float((recent_high - recent_low) / recent_low)
        else:
            indicators['breakout_amplitude'] = 0.0
        
        # 稳定性评分 (基于价格波动)
        price_changes = df['close'].pct_change().dropna()
        if len(price_changes) > 0:
            volatility = price_changes.tail(20).std()
            indicators['stability_score'] = float(max(1.0 - volatility * 10, 0.0))
        else:
            indicators['stability_score'] = 0.0
        
        # 结构评分 (简化版本)
        indicators['structure_score'] = 0.5  # 默认中性评分
        
        # 趋势标志 (简化版本)
        indicators['upup_high'] = False
        indicators['upup_low'] = False
        indicators['downdown_high'] = False
        indicators['downdown_low'] = False
        
        return indicators
        
    except Exception as e:
        logger.error(f"计算结构指标失败: {e}")
        return {
            'breakout_amplitude': 0.0,
            'stability_score': 0.0,
            'structure_score': 0.0,
            'upup_high': False,
            'upup_low': False,
            'downdown_high': False,
            'downdown_low': False
        }


def calculate_twap_from_start_date(df: pd.DataFrame, start_date: datetime) -> float:
    """
    从指定开始日期计算TWAP
    
    Args:
        df: K线数据DataFrame
        start_date: 开始计算的日期
        
    Returns:
        TWAP值
    """
    try:
        # 过滤从start_date开始的数据
        if 'trade_time' in df.columns:
            mask = pd.to_datetime(df['trade_time']) >= start_date
            filtered_df = df[mask]
        else:
            # 如果没有时间列，使用索引
            filtered_df = df
        
        if filtered_df.empty:
            return 0.0
        
        # 计算TWAP (成交量加权平均价格)
        total_amount = ((filtered_df['close'] + filtered_df['high'] + filtered_df['low'])/3 * filtered_df['volume']).sum()
        total_volume = filtered_df['volume'].sum()
        return float(total_amount / total_volume)

            
    except Exception as e:
        logger.error(f"计算TWAP失败: {e}")
        return 0.0


def _calculate_fibonacci_fallback(df: pd.DataFrame) -> Dict[str, float]:
    """
    斐波那契计算的备用方法

    当所有其他方法都失败时使用的最简单计算

    Args:
        df: K线数据DataFrame

    Returns:
        基础斐波那契指标字典
    """
    try:
        high_price = df['high'].max()
        low_price = df['low'].min()
        diff = high_price - low_price

        return {
            'fib_level_0_236': float(high_price - diff * 0.236),
            'fib_level_0_382': float(high_price - diff * 0.382),
            'fib_level_0_5': float(high_price - diff * 0.5),
            'fib_level_0_618': float(high_price - diff * 0.618),
            'fib_level_0_786': float(high_price - diff * 0.786),
            'fib_level_1_272': float(high_price + diff * 0.272),
            'fib_level_1_382': float(high_price + diff * 0.382),
            'fib_level_1_618': float(high_price + diff * 0.618),
            'f_fib_level_1_272': float(high_price + diff * 1.272),
            'f_fib_level_1_382': float(high_price + diff * 1.382),
            'f_fib_level_1_618': float(high_price + diff * 1.618)
        }

    except Exception as e:
        logger.error(f"备用斐波那契计算失败: {e}")
        return {
            'fib_level_0_236': 0.0, 'fib_level_0_382': 0.0, 'fib_level_0_5': 0.0,
            'fib_level_0_618': 0.0, 'fib_level_0_786': 0.0, 'fib_level_1_272': 0.0,
            'fib_level_1_382': 0.0, 'fib_level_1_618': 0.0, 'f_fib_level_1_272': 0.0,
            'f_fib_level_1_382': 0.0, 'f_fib_level_1_618': 0.0
        }
