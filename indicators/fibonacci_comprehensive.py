#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
斐波那契技术分析综合工具模块

提供基于斐波那契数列的全面技术分析工具集，整合了增强计算和通用分析功能。

主要功能:
==========

1. **增强计算模块** (来自fibonacci_enhanced.py)
   - 基于scipy峰值检测的增强斐波那契计算
   - 支持start_low_price的精确回撤位计算
   - 智能峰值识别和价格关系验证

2. **通用分析模块** (原fibonacci_ultra.py功能)
   - 回调位策略 - 基于斐波那契回调位的支撑和阻力
   - 扩展位策略 - 基于斐波那契扩展位的目标价格
   - 支撑阻力策略 - 识别关键支撑和阻力位
   - 时间区策略 - 基于斐波那契时间区的时间分析
   - 扇形线策略 - 基于斐波那契扇形线的趋势分析
   - 弧线策略 - 基于斐波那契弧线的价格运动分析

3. **高级分析功能**
   - 综合分析 - 多维度斐波那契分析
   - 位置分析 - 价格位置分析辅助函数
   - 信号生成 - 智能交易建议生成
   - 峰值检测 - 基于scipy的高级峰值识别

技术特性:
=========
- 兼容scipy和非scipy环境
- 支持多种数据输入格式
- 完整的错误处理和日志记录
- 高性能向量化计算
- 详细的函数文档和类型注解

作者: QuantFM Team
创建时间: 2025-06-18
合并时间: 2025-08-18
版本: 2.0.0 (合并增强版)
"""


from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from utils.logger import get_logger

logger = get_logger("fibonacci_ultra")

try:
    from scipy.signal import find_peaks, argrelextrema
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    logger.warning("scipy未安装，部分高级功能将不可用")


# ============================================================================
# 增强斐波那契计算模块 (来自fibonacci_enhanced.py)
# ============================================================================

def calculate_fibonacci_levels_enhanced(df: pd.DataFrame,
                                       start_low_price: float,
                                       start_low_date: Optional[datetime] = None,
                                       lookback_period: int = 100) -> Dict[str, float]:
    """
    增强的斐波那契回撤位计算

    使用scipy进行峰值检测，从指定的低点价格计算到当前时期内的峰值，
    然后计算斐波那契回撤位和扩展位。这是原fibonacci_enhanced.py的核心功能。

    Args:
        df: 包含OHLCV数据的DataFrame，必须包含'high', 'low', 'close'列
        start_low_price: 起始低点价格（来自信号表）
        start_low_date: 起始低点日期（可选，用于精确定位）
        lookback_period: 回看周期，默认100个交易日

    Returns:
        包含斐波那契指标的字典，包括回撤位、扩展位和额外指标

    Raises:
        ValueError: 当输入数据无效时
    """
    if df.empty or len(df) < 20:
        raise ValueError(f"数据不足，需要至少20条记录，当前: {len(df)}")

    required_columns = ['high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"缺少必要的数据列: {missing_columns}")

    try:
        # 1. 确定分析区间
        analysis_df = df.tail(lookback_period).copy()

        # 2. 寻找峰值（高点）
        peak_price = _find_peak_price_enhanced(analysis_df, start_low_price)

        # 3. 验证价格关系
        if peak_price <= start_low_price:
            logger.warning(f"峰值价格 {peak_price} 不高于起始低点 {start_low_price}，使用最高价作为峰值")
            peak_price = analysis_df['high'].max()

        # 4. 计算斐波那契指标
        fibonacci_levels = _calculate_fibonacci_ratios_enhanced(start_low_price, peak_price)

        # 5. 添加额外的技术指标
        additional_indicators = _calculate_additional_fibonacci_indicators(
            analysis_df, start_low_price, peak_price
        )
        fibonacci_levels.update(additional_indicators)

        logger.debug(f"增强斐波那契计算完成: 低点={start_low_price:.4f}, 峰值={peak_price:.4f}")
        return fibonacci_levels

    except Exception as e:
        logger.error(f"增强斐波那契指标计算失败: {e}")
        # 返回基于简单高低点的默认值
        return _calculate_fallback_fibonacci_enhanced(df, start_low_price)


def _find_peak_price_enhanced(df: pd.DataFrame, start_low_price: float) -> float:
    """
    使用增强算法寻找峰值价格

    优先使用scipy的find_peaks算法，如果不可用则使用简化算法

    Args:
        df: K线数据DataFrame
        start_low_price: 起始低点价格

    Returns:
        峰值价格
    """
    try:
        high_prices = df['high'].values

        if SCIPY_AVAILABLE:
            # 使用scipy进行峰值检测
            peaks, properties = find_peaks(
                high_prices,
                height=start_low_price * 1.02,  # 至少比低点高2%
                distance=5,  # 峰值间至少间隔5个周期
                prominence=start_low_price * 0.01  # 突出度至少为低点的1%
            )

            if len(peaks) > 0:
                # 选择最高的峰值
                peak_heights = high_prices[peaks]
                max_peak_idx = peaks[np.argmax(peak_heights)]
                peak_price = high_prices[max_peak_idx]

                logger.debug(f"scipy检测到 {len(peaks)} 个峰值，最高峰值: {peak_price:.4f}")
                return peak_price
            else:
                logger.debug("scipy未检测到有效峰值，使用最高价")
                return df['high'].max()
        else:
            # 简化的峰值检测算法
            return _find_peak_price_simple_enhanced(df, start_low_price)

    except Exception as e:
        logger.warning(f"峰值检测失败: {e}，使用最高价")
        return df['high'].max()


def _find_peak_price_simple_enhanced(df: pd.DataFrame, start_low_price: float) -> float:
    """
    简化的峰值检测算法（增强版）

    当scipy不可用时使用的备用算法

    Args:
        df: K线数据DataFrame
        start_low_price: 起始低点价格

    Returns:
        峰值价格
    """
    try:
        # 寻找局部高点
        high_prices = df['high'].values
        peaks = []

        # 简单的局部最大值检测
        for i in range(2, len(high_prices) - 2):
            if (high_prices[i] > high_prices[i-1] and
                high_prices[i] > high_prices[i-2] and
                high_prices[i] > high_prices[i+1] and
                high_prices[i] > high_prices[i+2] and
                high_prices[i] > start_low_price * 1.02):  # 至少比低点高2%
                peaks.append(high_prices[i])

        if peaks:
            peak_price = max(peaks)
            logger.debug(f"简化算法检测到 {len(peaks)} 个峰值，最高峰值: {peak_price:.4f}")
            return peak_price
        else:
            # 如果没有找到峰值，使用最高价
            return df['high'].max()

    except Exception as e:
        logger.warning(f"简化峰值检测失败: {e}，使用最高价")
        return df['high'].max()


def _calculate_fibonacci_ratios_enhanced(low_price: float, high_price: float) -> Dict[str, float]:
    """
    计算标准斐波那契回撤位和扩展位（增强版）

    Args:
        low_price: 低点价格
        high_price: 高点价格

    Returns:
        斐波那契比例字典
    """
    price_range = high_price - low_price

    # 标准斐波那契回撤位
    fibonacci_levels = {
        # 回撤位（从高点向下）
        'fib_level_0_236': high_price - price_range * 0.236,  # 23.6%回撤
        'fib_level_0_382': high_price - price_range * 0.382,  # 38.2%回撤
        'fib_level_0_5': high_price - price_range * 0.5,      # 50%回撤
        'fib_level_0_618': high_price - price_range * 0.618,  # 61.8%回撤
        'fib_level_0_786': high_price - price_range * 0.786,  # 78.6%回撤

        # 扩展位（从高点向上）
        'fib_ext_1_272': high_price + price_range * 0.272,    # 127.2%扩展
        'fib_ext_1_414': high_price + price_range * 0.414,    # 141.4%扩展
        'fib_ext_1_618': high_price + price_range * 0.618,    # 161.8%扩展

        # 关键价位
        'fib_high': high_price,                               # 峰值价格
        'fib_low': low_price,                                 # 低点价格
        'fib_range': price_range,                             # 价格区间
    }

    return fibonacci_levels


def _calculate_additional_fibonacci_indicators(df: pd.DataFrame,
                                             low_price: float,
                                             high_price: float) -> Dict[str, float]:
    """
    计算额外的斐波那契相关指标（增强版）

    Args:
        df: K线数据DataFrame
        low_price: 低点价格
        high_price: 高点价格

    Returns:
        额外指标字典
    """
    try:
        current_price = df['close'].iloc[-1]
        price_range = high_price - low_price

        additional_indicators = {
            # 当前价格相对位置
            'fib_current_ratio': (current_price - low_price) / price_range if price_range > 0 else 0.0,

            # 价格强度指标
            'fib_strength': (high_price - low_price) / low_price if low_price > 0 else 0.0,

            # 回撤深度（如果当前价格低于峰值）
            'fib_retracement_depth': (high_price - current_price) / price_range if current_price < high_price and price_range > 0 else 0.0,
        }

        return additional_indicators

    except Exception as e:
        logger.warning(f"计算额外斐波那契指标失败: {e}")
        return {
            'fib_current_ratio': 0.0,
            'fib_strength': 0.0,
            'fib_retracement_depth': 0.0,
        }


def _calculate_fallback_fibonacci_enhanced(df: pd.DataFrame, start_low_price: float) -> Dict[str, float]:
    """
    备用的斐波那契计算方法（增强版）

    当主要计算方法失败时使用

    Args:
        df: K线数据DataFrame
        start_low_price: 起始低点价格

    Returns:
        基础斐波那契指标字典
    """
    try:
        high_price = df['high'].max()
        price_range = high_price - start_low_price

        return {
            'fib_level_0_236': high_price - price_range * 0.236,
            'fib_level_0_382': high_price - price_range * 0.382,
            'fib_level_0_5': high_price - price_range * 0.5,
            'fib_level_0_618': high_price - price_range * 0.618,
            'fib_level_0_786': high_price - price_range * 0.786,
            'fib_ext_1_272': high_price + price_range * 0.272,
            'fib_ext_1_414': high_price + price_range * 0.414,
            'fib_ext_1_618': high_price + price_range * 0.618,
            'fib_high': high_price,
            'fib_low': start_low_price,
            'fib_range': price_range,
            'fib_current_ratio': 0.0,
            'fib_strength': price_range / start_low_price if start_low_price > 0 else 0.0,
            'fib_retracement_depth': 0.0,
        }

    except Exception as e:
        logger.error(f"备用斐波那契计算失败: {e}")
        return {key: 0.0 for key in [
            'fib_level_0_236', 'fib_level_0_382', 'fib_level_0_5',
            'fib_level_0_618', 'fib_level_0_786', 'fib_ext_1_272',
            'fib_ext_1_414', 'fib_ext_1_618', 'fib_high', 'fib_low',
            'fib_range', 'fib_current_ratio', 'fib_strength', 'fib_retracement_depth'
        ]}


# ============================================================================
# 通用斐波那契分析模块 (原fibonacci_ultra.py功能)
# ============================================================================

# 回调位策略
def calculate_fibonacci_retracement(
    start_price: float,
    end_price: float,
    levels: Optional[List[float]] = None,
    prefix: str = 'fib_',
    include_full_range: bool = True,
    trend: Optional[str] = None,
    precision: int = 4
) -> Dict[float, float]:
    """
    通用斐波那契回调位计算函数 - 优化版

    Args:
        start_price: 起始价格
        end_price: 结束价格
        levels: 回调位水平列表，默认为常用水平
        prefix: 输出列前缀
        include_full_range: 是否包含0.0和1.0级别
        trend: 价格趋势，可选值为"up"或"down"，如果为None则自动计算
        precision: 价格精度，小数点位数

    Returns:
        回调位水平到价格的字典
    """
    # 设置默认回调位水平
    if levels is None:
        if include_full_range:
            levels = [0.0, 0.236, 0.382, 0.5, 0.618, 0.786, 1.0]
        else:
            levels = [0.236, 0.382, 0.5, 0.618, 0.786]
    elif include_full_range and not (0.0 in levels and 1.0 in levels):
        # 确保包含完整范围时，0.0和1.0在levels中
        if 0.0 not in levels:
            levels = [0.0] + levels
        if 1.0 not in levels:
            levels = levels + [1.0]

    # 确定趋势方向
    if trend is None:
        trend = "up" if end_price > start_price else "down"

    # 计算价格差
    price_diff = abs(end_price - start_price)

    # 计算各个回调位水平对应的价格
    retracement_prices = {}

    if trend == "up":
        # 上升趋势：从低到高，回调位从高向低
        for level in levels:
            price = round(end_price - price_diff * level, precision)
            retracement_prices[level] = price
    else:
        # 下降趋势：从高到低，回调位从低向高
        for level in levels:
            price = round(start_price + price_diff * level, precision)
            retracement_prices[level] = price

    return retracement_prices

# 扩展位策略
def calculate_fibonacci_extension(
    start_price: float,
    end_price: float,
    retracement_price: Optional[float] = None,
    levels: Optional[List[float]] = None,
    prefix: str = 'fib_ext_',
    trend: Optional[str] = None,
    precision: int = 4
) -> Dict[float, float]:
    """
    通用斐波那契扩展位计算函数 - 优化版

    Args:
        start_price: 起始价格
        end_price: 中间价格（回调起点）
        retracement_price: 回调价格，如果为None则只计算简单扩展
        levels: 扩展位水平列表，默认为常用水平
        prefix: 输出列前缀
        trend: 价格趋势，可选值为"up"或"down"，如果为None则自动计算
        precision: 价格精度，小数点位数

    Returns:
        扩展位水平到价格的字典
    """
    # 设置默认扩展位水平
    if levels is None:
        levels = [1.0, 1.272, 1.382, 1.618, 2.0, 2.618, 3.618, 4.236]

    # 确定趋势方向
    if trend is None:
        trend = "up" if end_price > start_price else "down"

    # 计算价格差
    ab_diff = abs(end_price - start_price)

    # 计算各个扩展位水平对应的价格
    extension_prices = {}

    if retracement_price is None:
        # 简单扩展（不考虑回调）
        if trend == "up":
            # 上升趋势：从低到高，扩展位继续向上
            for level in levels:
                price = round(end_price + ab_diff * (level - 1.0), precision)
                extension_prices[level] = price
        else:
            # 下降趋势：从高到低，扩展位继续向下
            for level in levels:
                price = round(end_price - ab_diff * (level - 1.0), precision)
                extension_prices[level] = price
    else:
        # 完整的回调后扩展（ABC模式）
        bc_diff = abs(retracement_price - end_price)

        if trend == "up":
            # 上升趋势：从低到高，回调后扩展
            for level in levels:
                price = round(retracement_price + bc_diff * level, precision)
                extension_prices[level] = price
        else:
            # 下降趋势：从高到低，回调后扩展
            for level in levels:
                price = round(retracement_price - bc_diff * level, precision)
                extension_prices[level] = price

    return extension_prices

# 支撑阻力策略
def identify_support_resistance(
    current_price: float,
    fibonacci_levels: Dict[float, float],
    threshold_percent: float = 1.0,
    max_levels: int = 3
) -> Dict[str, Any]:
    """
    基于斐波那契水平识别支撑位和阻力位 - 优化版

    Args:
        current_price: 当前价格
        fibonacci_levels: 斐波那契水平字典
        threshold_percent: 距离当前价格的阈值百分比
        max_levels: 返回的最大支撑/阻力位数量

    Returns:
        包含支撑位和阻力位信息的字典
    """
    support_levels = []
    resistance_levels = []

    for level, price in fibonacci_levels.items():
        # 计算距离百分比
        distance_percent = abs(price - current_price) / current_price * 100
        level_info = {
            'level': level,
            'price': price,
            'distance_percent': distance_percent,
            'distance': abs(price - current_price)
        }

        # 分类为支撑位或阻力位
        if price < current_price:
            support_levels.append(level_info)
        elif price > current_price:
            resistance_levels.append(level_info)

    # 按距离排序
    support_levels.sort(key=lambda x: x['distance_percent'])
    resistance_levels.sort(key=lambda x: x['distance_percent'])

    # 筛选距离在阈值内的水平
    valid_supports = [level for level in support_levels if level['distance_percent'] <= threshold_percent]
    valid_resistances = [level for level in resistance_levels if level['distance_percent'] <= threshold_percent]

    # 限制返回的水平数量
    valid_supports = valid_supports[:max_levels]
    valid_resistances = valid_resistances[:max_levels]

    return {
        'support_levels': valid_supports,
        'resistance_levels': valid_resistances,
        'nearest_support': valid_supports[0] if valid_supports else None,
        'nearest_resistance': valid_resistances[0] if valid_resistances else None,
        'has_nearby_support': len(valid_supports) > 0,
        'has_nearby_resistance': len(valid_resistances) > 0
    }

# 时间区策略
def calculate_fibonacci_time_zones(
    start_time: datetime,
    end_time: Optional[datetime] = None,
    levels: Optional[List[int]] = None,
    time_unit: str = 'D',
    max_periods: int = 89
) -> Dict[int, datetime]:
    """
    通用斐波那契时间区计算函数 - 优化版

    Args:
        start_time: 起始时间
        end_time: 结束时间，如果为None则使用start_time
        levels: 斐波那契数列水平，默认使用经典斐波那契数列
        time_unit: 时间单位 ('D'=天, 'W'=周, 'M'=月, 'H'=小时)
        max_periods: 最大周期数

    Returns:
        斐波那契数列值到时间戳的字典
    """
    # 设置默认的斐波那契数列
    if levels is None:
        levels = [1, 2, 3, 5, 8, 13, 21, 34, 55, 89]
        # 限制最大周期
        levels = [level for level in levels if level <= max_periods]

    # 如果没有提供end_time，使用start_time
    if end_time is None:
        end_time = start_time

    time_zones = {}

    for level in levels:
        if time_unit == 'D':
            time_zones[level] = end_time + timedelta(days=level)
        elif time_unit == 'W':
            time_zones[level] = end_time + timedelta(weeks=level)
        elif time_unit == 'H':
            time_zones[level] = end_time + timedelta(hours=level)
        elif time_unit == 'M':
            # 月份计算需要特殊处理
            year = end_time.year
            month = end_time.month + level
            day = min(end_time.day, 28)  # 避免月份天数问题

            # 处理月份溢出
            years_to_add = (month - 1) // 12
            month = ((month - 1) % 12) + 1
            year += years_to_add

            time_zones[level] = datetime(year, month, day)

    return time_zones

# 新增：价格位置分析函数
def analyze_price_position(
    current_price: float,
    fibonacci_levels: Dict[float, float],
    tolerance_percent: float = 0.5
) -> Dict[str, Any]:
    """
    分析当前价格相对于斐波那契水平的位置

    Args:
        current_price: 当前价格
        fibonacci_levels: 斐波那契水平字典
        tolerance_percent: 价格接近斐波那契水平的容差百分比

    Returns:
        价格位置分析结果
    """
    # 将水平按价格排序
    sorted_levels = sorted([(level, price) for level, price in fibonacci_levels.items()],
                           key=lambda x: x[1])

    # 找到当前价格所在的区间
    current_zone = None
    for i in range(len(sorted_levels) - 1):
        lower_level, lower_price = sorted_levels[i]
        upper_level, upper_price = sorted_levels[i + 1]

        if lower_price <= current_price <= upper_price:
            current_zone = {
                'lower_level': lower_level,
                'lower_price': lower_price,
                'upper_level': upper_level,
                'upper_price': upper_price,
                'zone_size': upper_price - lower_price,
                'position_in_zone': (current_price - lower_price) / (upper_price - lower_price)
            }
            break

    # 检查价格是否接近任何斐波那契水平
    near_levels = []
    for level, price in fibonacci_levels.items():
        distance_percent = abs(price - current_price) / current_price * 100
        if distance_percent <= tolerance_percent:
            near_levels.append({
                'level': level,
                'price': price,
                'distance_percent': distance_percent
            })

    # 排序接近的水平
    near_levels.sort(key=lambda x: x['distance_percent'])

    return {
        'current_price': current_price,
        'current_zone': current_zone,
        'near_levels': near_levels,
        'is_near_level': len(near_levels) > 0,
        'nearest_level': near_levels[0] if near_levels else None
    }


def calculate_fibonacci_extension_with_second_peak(
    low_price: float,
    second_high_price: float,
    levels: Optional[List[float]] = None,
    precision: int = 4
) -> Dict[float, float]:
    """
    基于倒数第二个高点计算斐波那契扩展位

    这个函数专门用于双通道突破策略，使用scipy找到的倒数第二个高点
    作为计算基准，提供更准确的扩展位目标。

    参数:
        low_price: 低点价格（通常是突破前的最低点）
        second_high_price: 倒数第二个高点价格（由scipy分析得出）
        levels: 扩展位水平列表，默认为[1.272, 1.382, 1.618]
        precision: 价格精度，小数点位数

    返回:
        扩展位字典 {level: price}
    """
    try:
        if levels is None:
            levels = [1.272, 1.382, 1.618]

        if low_price <= 0 or second_high_price <= 0:
            logger.error("价格必须为正数")
            return {}

        if second_high_price <= low_price:
            logger.error("倒数第二个高点价格必须高于低点价格")
            return {}

        # 计算价格差
        price_diff = second_high_price - low_price

        # 计算各个扩展位
        extension_levels = {}
        for level in levels:
            try:
                # 扩展位 = 倒数第二高点 + (价格差 * 扩展比例)
                extension_price = second_high_price + (price_diff * (level - 1.0))
                extension_levels[level] = round(extension_price, precision)

                logger.debug(f"扩展位 {level}: {extension_price:.{precision}f}")

            except Exception as e:
                logger.warning(f"计算扩展位 {level} 时出错: {e}")
                extension_levels[level] = None

        logger.info(f"基于倒数第二高点计算扩展位完成: 低点={low_price}, 第二高点={second_high_price}")

        return extension_levels

    except Exception as e:
        logger.error(f"计算基于倒数第二高点的斐波那契扩展位失败: {e}")
        return {}


def calculate_fibonacci_retracement_with_second_peak(
    low_price: float,
    second_high_price: float,
    levels: Optional[List[float]] = None,
    precision: int = 4
) -> Dict[float, float]:
    """
    基于倒数第二个高点计算斐波那契回调位

    参数:
        low_price: 低点价格
        second_high_price: 倒数第二个高点价格
        levels: 回调位水平列表，默认为[0.236, 0.382, 0.5, 0.618, 0.786]
        precision: 价格精度

    返回:
        回调位字典 {level: price}
    """
    try:
        if levels is None:
            levels = [0.236, 0.382, 0.5, 0.618, 0.786]

        # 使用现有的回调位计算函数
        return calculate_fibonacci_retracement(
            start_price=low_price,
            end_price=second_high_price,
            levels=levels,
            precision=precision
        )

    except Exception as e:
        logger.error(f"基于倒数第二高点计算回调位失败: {e}")
        return {}


# 扇形线策略
def calculate_fibonacci_fan_lines(
    start_price: float,
    end_price: float,
    start_time: datetime,
    end_time: datetime,
    levels: Optional[List[float]] = None,
    precision: int = 4
) -> Dict[float, Dict[str, Any]]:
    """
    计算斐波那契扇形线

    扇形线是从起始点向不同斐波那契比例方向延伸的直线，
    用于识别动态支撑和阻力位。

    Args:
        start_price: 起始价格
        end_price: 结束价格
        start_time: 起始时间
        end_time: 结束时间
        levels: 斐波那契比例列表
        precision: 价格精度

    Returns:
        扇形线信息字典
    """
    try:
        if levels is None:
            levels = [0.236, 0.382, 0.5, 0.618, 0.786]

        # 计算时间和价格差
        time_diff = (end_time - start_time).total_seconds()
        price_diff = end_price - start_price

        fan_lines = {}

        for level in levels:
            # 计算扇形线的斜率
            target_price = start_price + (price_diff * level)
            slope = (target_price - start_price) / time_diff if time_diff != 0 else 0

            fan_lines[level] = {
                'start_price': start_price,
                'target_price': round(target_price, precision),
                'slope': slope,
                'level': level,
                'start_time': start_time,
                'end_time': end_time
            }

        logger.debug(f"计算斐波那契扇形线完成，共 {len(fan_lines)} 条线")
        return fan_lines

    except Exception as e:
        logger.error(f"计算斐波那契扇形线失败: {e}")
        return {}


# 弧线策略
def calculate_fibonacci_arcs(
    start_price: float,
    end_price: float,
    center_price: Optional[float] = None,
    levels: Optional[List[float]] = None,
    precision: int = 4
) -> Dict[float, Dict[str, Any]]:
    """
    计算斐波那契弧线

    弧线是以特定点为中心，以斐波那契比例为半径的圆弧，
    用于识别曲线支撑和阻力位。

    Args:
        start_price: 起始价格
        end_price: 结束价格
        center_price: 中心价格，如果为None则使用中点
        levels: 斐波那契比例列表
        precision: 价格精度

    Returns:
        弧线信息字典
    """
    try:
        if levels is None:
            levels = [0.236, 0.382, 0.5, 0.618, 0.786]

        if center_price is None:
            center_price = (start_price + end_price) / 2

        # 计算基础半径
        base_radius = abs(end_price - start_price) / 2

        arcs = {}

        for level in levels:
            radius = base_radius * level

            arcs[level] = {
                'center_price': round(center_price, precision),
                'radius': round(radius, precision),
                'upper_bound': round(center_price + radius, precision),
                'lower_bound': round(center_price - radius, precision),
                'level': level
            }

        logger.debug(f"计算斐波那契弧线完成，共 {len(arcs)} 条弧线")
        return arcs

    except Exception as e:
        logger.error(f"计算斐波那契弧线失败: {e}")
        return {}


# 高级峰值检测（使用scipy）
def find_fibonacci_peaks_and_valleys(
    price_data: pd.Series,
    min_distance: int = 5,
    prominence_ratio: float = 0.02
) -> Dict[str, Any]:
    """
    使用scipy进行高级峰值和谷值检测

    Args:
        price_data: 价格数据序列
        min_distance: 峰值间最小距离
        prominence_ratio: 突出度比例（相对于价格范围）

    Returns:
        峰值和谷值信息
    """
    try:
        if not SCIPY_AVAILABLE:
            logger.warning("scipy不可用，使用简化的峰值检测")
            return _find_peaks_simple(price_data, min_distance)

        prices = price_data.values
        price_range = np.max(prices) - np.min(prices)
        min_prominence = price_range * prominence_ratio

        # 找峰值
        peaks, peak_properties = find_peaks(
            prices,
            distance=min_distance,
            prominence=min_prominence
        )

        # 找谷值（反转数据）
        valleys, valley_properties = find_peaks(
            -prices,
            distance=min_distance,
            prominence=min_prominence
        )

        # 获取峰值和谷值的价格和时间
        peak_data = []
        for idx in peaks:
            peak_data.append({
                'index': idx,
                'price': prices[idx],
                'time': price_data.index[idx] if hasattr(price_data.index, '__getitem__') else idx,
                'prominence': peak_properties['prominences'][np.where(peaks == idx)[0][0]]
            })

        valley_data = []
        for idx in valleys:
            valley_data.append({
                'index': idx,
                'price': prices[idx],
                'time': price_data.index[idx] if hasattr(price_data.index, '__getitem__') else idx,
                'prominence': valley_properties['prominences'][np.where(valleys == idx)[0][0]]
            })

        # 按突出度排序
        peak_data.sort(key=lambda x: x['prominence'], reverse=True)
        valley_data.sort(key=lambda x: x['prominence'], reverse=True)

        logger.debug(f"检测到 {len(peak_data)} 个峰值和 {len(valley_data)} 个谷值")

        return {
            'peaks': peak_data,
            'valleys': valley_data,
            'peak_count': len(peak_data),
            'valley_count': len(valley_data),
            'highest_peak': peak_data[0] if peak_data else None,
            'lowest_valley': min(valley_data, key=lambda x: x['price']) if valley_data else None
        }

    except Exception as e:
        logger.error(f"高级峰值检测失败: {e}")
        return _find_peaks_simple(price_data, min_distance)


def _find_peaks_simple(price_data: pd.Series, min_distance: int = 5) -> Dict[str, Any]:
    """
    简化的峰值检测（不依赖scipy）

    Args:
        price_data: 价格数据序列
        min_distance: 峰值间最小距离

    Returns:
        峰值和谷值信息
    """
    try:
        prices = price_data.values
        peaks = []
        valleys = []

        # 简单的局部极值检测
        for i in range(min_distance, len(prices) - min_distance):
            # 检查是否为峰值
            is_peak = all(prices[i] >= prices[j] for j in range(i - min_distance, i + min_distance + 1))
            if is_peak and prices[i] > prices[i-1] and prices[i] > prices[i+1]:
                peaks.append({
                    'index': i,
                    'price': prices[i],
                    'time': price_data.index[i] if hasattr(price_data.index, '__getitem__') else i,
                    'prominence': 0.0  # 简化版本不计算突出度
                })

            # 检查是否为谷值
            is_valley = all(prices[i] <= prices[j] for j in range(i - min_distance, i + min_distance + 1))
            if is_valley and prices[i] < prices[i-1] and prices[i] < prices[i+1]:
                valleys.append({
                    'index': i,
                    'price': prices[i],
                    'time': price_data.index[i] if hasattr(price_data.index, '__getitem__') else i,
                    'prominence': 0.0
                })

        logger.debug(f"简化检测到 {len(peaks)} 个峰值和 {len(valleys)} 个谷值")

        return {
            'peaks': peaks,
            'valleys': valleys,
            'peak_count': len(peaks),
            'valley_count': len(valleys),
            'highest_peak': max(peaks, key=lambda x: x['price']) if peaks else None,
            'lowest_valley': min(valleys, key=lambda x: x['price']) if valleys else None
        }

    except Exception as e:
        logger.error(f"简化峰值检测失败: {e}")
        return {'peaks': [], 'valleys': [], 'peak_count': 0, 'valley_count': 0, 'highest_peak': None, 'lowest_valley': None}


# 综合分析函数
def comprehensive_fibonacci_analysis(
    price_data: pd.Series,
    start_price: Optional[float] = None,
    end_price: Optional[float] = None,
    current_price: Optional[float] = None,
    analysis_period: int = 100
) -> Dict[str, Any]:
    """
    综合斐波那契分析

    整合多种斐波那契工具进行全面分析，包括：
    - 回调位分析
    - 扩展位分析
    - 支撑阻力位识别
    - 峰值谷值检测
    - 价格位置分析

    Args:
        price_data: 价格数据序列
        start_price: 分析起始价格，如果为None则自动检测
        end_price: 分析结束价格，如果为None则自动检测
        current_price: 当前价格，如果为None则使用最新价格
        analysis_period: 分析周期

    Returns:
        综合分析结果
    """
    try:
        # 获取分析数据
        if len(price_data) > analysis_period:
            analysis_data = price_data.tail(analysis_period)
        else:
            analysis_data = price_data

        if current_price is None:
            current_price = float(analysis_data.iloc[-1])

        # 峰值谷值检测
        peaks_valleys = find_fibonacci_peaks_and_valleys(analysis_data)

        # 自动确定起始和结束价格
        if start_price is None or end_price is None:
            if peaks_valleys['valley_count'] > 0 and peaks_valleys['peak_count'] > 0:
                # 使用最低谷值和最高峰值
                start_price = peaks_valleys['lowest_valley']['price']
                end_price = peaks_valleys['highest_peak']['price']
            else:
                # 使用数据范围
                start_price = float(analysis_data.min())
                end_price = float(analysis_data.max())

        # 计算回调位
        retracement_levels = calculate_fibonacci_retracement(
            start_price=start_price,
            end_price=end_price,
            include_full_range=True
        )

        # 计算扩展位
        extension_levels = calculate_fibonacci_extension(
            start_price=start_price,
            end_price=end_price
        )

        # 合并所有斐波那契水平
        all_levels = {**retracement_levels, **extension_levels}

        # 支撑阻力分析
        support_resistance = identify_support_resistance(
            current_price=current_price,
            fibonacci_levels=all_levels,
            threshold_percent=2.0,
            max_levels=5
        )

        # 价格位置分析
        position_analysis = analyze_price_position(
            current_price=current_price,
            fibonacci_levels=retracement_levels,
            tolerance_percent=1.0
        )

        # 计算关键统计信息
        price_range = end_price - start_price
        current_position_ratio = (current_price - start_price) / price_range if price_range != 0 else 0

        # 趋势分析
        trend_direction = "上升" if end_price > start_price else "下降"
        trend_strength = abs(price_range) / start_price * 100 if start_price != 0 else 0

        analysis_result = {
            'basic_info': {
                'start_price': start_price,
                'end_price': end_price,
                'current_price': current_price,
                'price_range': price_range,
                'current_position_ratio': current_position_ratio,
                'trend_direction': trend_direction,
                'trend_strength': trend_strength,
                'analysis_period': len(analysis_data)
            },
            'retracement_levels': retracement_levels,
            'extension_levels': extension_levels,
            'support_resistance': support_resistance,
            'position_analysis': position_analysis,
            'peaks_valleys': peaks_valleys,
            'key_levels': {
                'nearest_support': support_resistance.get('nearest_support'),
                'nearest_resistance': support_resistance.get('nearest_resistance'),
                'key_retracement': position_analysis.get('nearest_level')
            }
        }

        logger.info(f"综合斐波那契分析完成: 趋势={trend_direction}, 强度={trend_strength:.2f}%")
        return analysis_result

    except Exception as e:
        logger.error(f"综合斐波那契分析失败: {e}")
        return {}


# 信号生成函数
def generate_fibonacci_signals(
    comprehensive_analysis: Dict[str, Any],
    signal_sensitivity: str = 'medium',
    min_signal_strength: float = 0.6
) -> Dict[str, Any]:
    """
    基于综合斐波那契分析生成交易信号

    Args:
        comprehensive_analysis: 综合分析结果
        signal_sensitivity: 信号敏感度 ('low', 'medium', 'high')
        min_signal_strength: 最小信号强度

    Returns:
        交易信号和建议
    """
    try:
        if not comprehensive_analysis:
            return {'signals': [], 'overall_signal': 'HOLD', 'signal_strength': 0.0}

        basic_info = comprehensive_analysis.get('basic_info', {})
        support_resistance = comprehensive_analysis.get('support_resistance', {})
        position_analysis = comprehensive_analysis.get('position_analysis', {})

        current_price = basic_info.get('current_price', 0)
        current_position_ratio = basic_info.get('current_position_ratio', 0.5)
        trend_direction = basic_info.get('trend_direction', '未知')

        signals = []
        signal_strength = 0.0

        # 设置敏感度参数
        sensitivity_params = {
            'low': {'distance_threshold': 0.5, 'strength_multiplier': 0.8},
            'medium': {'distance_threshold': 1.0, 'strength_multiplier': 1.0},
            'high': {'distance_threshold': 2.0, 'strength_multiplier': 1.2}
        }

        params = sensitivity_params.get(signal_sensitivity, sensitivity_params['medium'])

        # 支撑位信号
        if support_resistance.get('has_nearby_support'):
            nearest_support = support_resistance.get('nearest_support')
            if nearest_support and nearest_support['distance_percent'] <= params['distance_threshold']:
                strength = (1 - nearest_support['distance_percent'] / params['distance_threshold']) * params['strength_multiplier']
                signals.append({
                    'type': 'SUPPORT',
                    'action': 'BUY',
                    'price': nearest_support['price'],
                    'strength': strength,
                    'reason': f"接近斐波那契支撑位 {nearest_support['level']:.3f} ({nearest_support['price']:.4f})"
                })
                signal_strength += strength * 0.4

        # 阻力位信号
        if support_resistance.get('has_nearby_resistance'):
            nearest_resistance = support_resistance.get('nearest_resistance')
            if nearest_resistance and nearest_resistance['distance_percent'] <= params['distance_threshold']:
                strength = (1 - nearest_resistance['distance_percent'] / params['distance_threshold']) * params['strength_multiplier']
                signals.append({
                    'type': 'RESISTANCE',
                    'action': 'SELL',
                    'price': nearest_resistance['price'],
                    'strength': strength,
                    'reason': f"接近斐波那契阻力位 {nearest_resistance['level']:.3f} ({nearest_resistance['price']:.4f})"
                })
                signal_strength += strength * 0.4

        # 位置信号
        if position_analysis.get('is_near_level'):
            nearest_level = position_analysis.get('nearest_level')
            if nearest_level:
                level_value = nearest_level['level']
                if level_value <= 0.382:  # 强支撑区域
                    signals.append({
                        'type': 'STRONG_SUPPORT',
                        'action': 'BUY',
                        'price': nearest_level['price'],
                        'strength': 0.8 * params['strength_multiplier'],
                        'reason': f"位于强支撑区域 {level_value:.3f}"
                    })
                    signal_strength += 0.3
                elif level_value >= 0.618:  # 强阻力区域
                    signals.append({
                        'type': 'STRONG_RESISTANCE',
                        'action': 'SELL',
                        'price': nearest_level['price'],
                        'strength': 0.8 * params['strength_multiplier'],
                        'reason': f"位于强阻力区域 {level_value:.3f}"
                    })
                    signal_strength += 0.3

        # 趋势信号
        if trend_direction == "上升" and current_position_ratio < 0.618:
            signals.append({
                'type': 'TREND_FOLLOW',
                'action': 'BUY',
                'price': current_price,
                'strength': 0.6 * params['strength_multiplier'],
                'reason': "上升趋势中，价格位于合理买入区域"
            })
            signal_strength += 0.2
        elif trend_direction == "下降" and current_position_ratio > 0.382:
            signals.append({
                'type': 'TREND_FOLLOW',
                'action': 'SELL',
                'price': current_price,
                'strength': 0.6 * params['strength_multiplier'],
                'reason': "下降趋势中，价格位于合理卖出区域"
            })
            signal_strength += 0.2

        # 确定总体信号
        buy_signals = [s for s in signals if s['action'] == 'BUY']
        sell_signals = [s for s in signals if s['action'] == 'SELL']

        buy_strength = sum(s['strength'] for s in buy_signals)
        sell_strength = sum(s['strength'] for s in sell_signals)

        if buy_strength > sell_strength and buy_strength >= min_signal_strength:
            overall_signal = 'BUY'
        elif sell_strength > buy_strength and sell_strength >= min_signal_strength:
            overall_signal = 'SELL'
        else:
            overall_signal = 'HOLD'

        # 限制信号强度在0-1之间
        signal_strength = min(signal_strength, 1.0)

        result = {
            'signals': signals,
            'overall_signal': overall_signal,
            'signal_strength': signal_strength,
            'buy_strength': buy_strength,
            'sell_strength': sell_strength,
            'signal_count': len(signals),
            'sensitivity': signal_sensitivity
        }

        logger.info(f"生成斐波那契信号: {overall_signal}, 强度={signal_strength:.2f}, 信号数={len(signals)}")
        return result

    except Exception as e:
        logger.error(f"生成斐波那契信号失败: {e}")
        return {'signals': [], 'overall_signal': 'HOLD', 'signal_strength': 0.0}


# 工具函数：格式化分析结果
def format_analysis_report(
    comprehensive_analysis: Dict[str, Any],
    signals: Dict[str, Any]
) -> str:
    """
    格式化分析报告为可读文本

    Args:
        comprehensive_analysis: 综合分析结果
        signals: 信号分析结果

    Returns:
        格式化的分析报告
    """
    try:
        if not comprehensive_analysis:
            return "分析数据不足，无法生成报告"

        basic_info = comprehensive_analysis.get('basic_info', {})
        support_resistance = comprehensive_analysis.get('support_resistance', {})

        report_lines = [
            "=" * 50,
            "斐波那契技术分析报告",
            "=" * 50,
            "",
            "基本信息:",
            f"  当前价格: {basic_info.get('current_price', 0):.4f}",
            f"  价格区间: {basic_info.get('start_price', 0):.4f} - {basic_info.get('end_price', 0):.4f}",
            f"  趋势方向: {basic_info.get('trend_direction', '未知')}",
            f"  趋势强度: {basic_info.get('trend_strength', 0):.2f}%",
            f"  当前位置: {basic_info.get('current_position_ratio', 0):.1%}",
            "",
            "支撑阻力分析:",
        ]

        # 支撑位信息
        if support_resistance.get('has_nearby_support'):
            nearest_support = support_resistance.get('nearest_support')
            report_lines.append(f"  最近支撑位: {nearest_support['price']:.4f} (距离 {nearest_support['distance_percent']:.2f}%)")
        else:
            report_lines.append("  无近期支撑位")

        # 阻力位信息
        if support_resistance.get('has_nearby_resistance'):
            nearest_resistance = support_resistance.get('nearest_resistance')
            report_lines.append(f"  最近阻力位: {nearest_resistance['price']:.4f} (距离 {nearest_resistance['distance_percent']:.2f}%)")
        else:
            report_lines.append("  无近期阻力位")

        # 信号分析
        report_lines.extend([
            "",
            "交易信号分析:",
            f"  总体信号: {signals.get('overall_signal', 'HOLD')}",
            f"  信号强度: {signals.get('signal_strength', 0):.2f}",
            f"  信号数量: {signals.get('signal_count', 0)}",
            ""
        ])

        # 具体信号
        for i, signal in enumerate(signals.get('signals', []), 1):
            report_lines.append(f"  信号 {i}: {signal['action']} - {signal['reason']} (强度: {signal['strength']:.2f})")

        report_lines.extend([
            "",
            "=" * 50
        ])

        return "\n".join(report_lines)

    except Exception as e:
        logger.error(f"格式化分析报告失败: {e}")
        return "报告生成失败"
