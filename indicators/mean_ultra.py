"""
平均值分析通用工具模块

提供基于平均值的通用技术分析工具。
主要功能:

1. ema - 计算多周期的ema值
2. vwap - 计算基于周期的时间加权平均
3. 支撑阻力策略 - 识别关键支撑和阻力位
4. 时间区策略 - 基于斐波那契时间区的时间分析
5. 扇形线策略 - 基于斐波那契扇形线的趋势分析
6. 弧线策略 - 基于斐波那契弧线的价格运动分析
7. 综合分析 - 斐波那契分析通用函数
8. 位置分析 - 价格位置分析辅助函数
9. 信号生成 - 交易建议生成函数

Author: Xzh
Date: 2025-06-18
"""

import talib as ta
import pandas as pd
import numpy as np

from utils.logger import get_logger

logger = get_logger("mean_ultra")

# 计算多周期ema
def calculate_multi_period_ema(close_prices: pd.Series, periods: list, include_source: bool = True) -> pd.DataFrame:
    """
    增强版多周期EMA计算函数

    参数:
    data: 价格数据（可以是Series或numpy数组）
    periods: EMA周期列表
    include_source: 是否包含原始数据

    返回:
    包含原始数据和多个EMA的DataFrame
    """
    # 数据预处理
    if isinstance(close_prices, pd.Series):
        index = close_prices.index
        close_prices_array = close_prices.values
    else:
        close_prices_array = np.array(close_prices)
        index = range(len(close_prices_array))

    # 确保数据类型为float64（TA-Lib要求）
    close_prices_array = np.asarray(close_prices_array, dtype=np.float64)

    # 检查数据有效性
    if len(close_prices_array) == 0:
        raise ValueError("输入数据为空")

    # 检查输入数据的NaN值 - 输入数据不应该包含NaN
    if np.any(np.isnan(close_prices_array)):
        nan_count = np.sum(np.isnan(close_prices_array))
        logger.error(f"输入数据中包含 {nan_count} 个NaN值，这是严重的数据质量问题")
        raise ValueError(f"数据质量错误: 输入数据包含 {nan_count} 个NaN值，请检查数据源的数据质量")

    # 定义periods
    if periods is None:
        periods = [12, 62, 144, 169, 377, 576, 676]

    # 创建结果DataFrame
    result = pd.DataFrame(index=index)

    # 添加原始数据
    if include_source:
        result['close'] = close_prices_array

    # 计算各个周期的EMA
    for period in periods:
        try:
            # 确保有足够的数据点计算EMA
            if len(close_prices_array) < period:
                logger.warning(f"数据长度 {len(close_prices_array)} 小于EMA周期 {period}，跳过此周期")
                result[f'EMA_{period}'] = np.nan
                continue

            ema_values = ta.EMA(close_prices_array, timeperiod=period)
            result[f'EMA_{period}'] = ema_values

        except Exception as e:
            logger.error(f"计算EMA_{period}失败: {e}")
            result[f'EMA_{period}'] = np.nan

    return result

# 批量计算多个股票的多周期EMA
def batch_calculate_ema(stock_data_dict, periods):
    """
    批量计算多个股票的多周期EMA

    参数:
    stock_data_dict: {股票代码: 价格数据} 的字典
    periods: EMA周期列表

    返回:
    {股票代码: EMA结果DataFrame} 的字典
    """
    results = {}

    for stock_code, prices in stock_data_dict.items():
        try:
            ema_data = calculate_multi_period_ema(prices, periods, include_source=False)
            results[stock_code] = ema_data
        except Exception as e:
            print(f"计算 {stock_code} 的EMA时出错: {e}")
            continue

    return results

# 计算时间加权平均指数
def vwap(data: pd.DataFrame, period: int = 9, is_TWAP: bool = False) -> pd.Series:
    """
    计算周期VWAP或者是TWAP

    参数:
    data: 包含High, Low, Close, Volume列的DataFrame

    返回:
    周期滚动VWAP序列或者是TWAP
    """
    # 计算典型价格 (high + low + close) / 3
    typical_price = (data['high'] + data['low'] + data['close']) / 3

    # 计算典型价格与成交量的乘积
    tp_volume = typical_price * data['volume']

    if is_TWAP:
        # 计算TWAP
        twap = tp_volume.cumsum() / data['volume'].cumsum()
        return twap
    else:
        # 计算9期滚动求和
        rolling_tp_volume = tp_volume.rolling(window=period).sum()
        rolling_volume = data['volume'].rolling(window=period).sum()

        # 计算VWAP：避免除零错误
        vwap = rolling_tp_volume / rolling_volume

        return vwap

