-- 创建股票策略信号表
-- 用于存储盘后策略选股产生的信号
-- 
-- 作者: QuantFM Team
-- 创建时间: 2025-01-05

-- 创建stock_signals表
CREATE TABLE IF NOT EXISTS stock_signals (
    stock_code VARCHAR(10) NOT NULL,
    strategy_name VARCHAR(100) NOT NULL,
    signal_date DATE NOT NULL,
    stock_name VARCHAR(50) NOT NULL,
    signal_strength DECIMAL(5,4) DEFAULT 0.0,
    signal_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 复合主键：股票代码 + 策略名称 + 信号日期
    PRIMARY KEY (stock_code, strategy_name, signal_date)
);

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_signals_stock_date 
    ON stock_signals (stock_code, signal_date DESC);

CREATE INDEX IF NOT EXISTS idx_signals_strategy_date 
    ON stock_signals (strategy_name, signal_date DESC);

CREATE INDEX IF NOT EXISTS idx_signals_date 
    ON stock_signals (signal_date DESC);

CREATE INDEX IF NOT EXISTS idx_signals_strength 
    ON stock_signals (signal_strength DESC, signal_date DESC);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_stock_signals_updated_at 
    BEFORE UPDATE ON stock_signals 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 添加表注释
COMMENT ON TABLE stock_signals IS '股票策略信号表，存储盘后策略选股产生的信号';
COMMENT ON COLUMN stock_signals.stock_code IS '股票代码';
COMMENT ON COLUMN stock_signals.strategy_name IS '策略名称';
COMMENT ON COLUMN stock_signals.signal_date IS '信号日期';
COMMENT ON COLUMN stock_signals.stock_name IS '股票名称';
COMMENT ON COLUMN stock_signals.signal_strength IS '信号强度 (0-1)';
COMMENT ON COLUMN stock_signals.signal_data IS '策略相关数据 (JSON格式)';
COMMENT ON COLUMN stock_signals.created_at IS '创建时间';
COMMENT ON COLUMN stock_signals.updated_at IS '更新时间';

-- 验证表创建
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE tablename = 'stock_signals';

-- 验证索引创建
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'stock_signals'
ORDER BY indexname;
