# Design Document

## Overview

双通道斐波那契突破策略是一个基于通道突破的量化交易策略。该策略通过分析股票价格在两个不同通道之间的突破行为，识别具有潜在上涨动能的股票。策略采用正向遍历的方式，从历史数据开始扫描每一个完整的突破周期，确保信号的完整性和准确性。

## Architecture

### 系统架构图

```mermaid
graph TB
    A[after_market_schedule.py] --> A1[获取股票列表]
    A --> A2[获取K线数据]
    A --> A3[数据预处理 - 转换为DataFrame]
    A --> A4[调用indicators计算基础指标]
    A --> A5[调用execute_strategies]
    
    A4 --> I1[indicators/channel_indicators.py]
    A4 --> I2[indicators/position_indicators.py]
    A4 --> I3[indicators/talib_wrapper.py]
    
    I1 --> I1A[计算通道上下轨]
    I2 --> I2A[计算价格与通道位置关系]
    I3 --> I3A[调用talib计算EMA等]
    
    A5 --> B[DualChannelFibonacciStrategy]
    A5 --> B1[其他策略...]
    
    B --> B1[正向遍历K线数据]
    B --> B2[识别潜在起点]
    B --> B3[检测通道突破序列]
    B --> B4[验证突破条件]
    B --> B5[提取关键点]
    
    B --> G[返回Signal对象]
    A5 --> H[收集所有策略信号]
    H --> J[批量保存到stock_signals表]
```

### 集成方式

1. **after_market_schedule.py负责**：
   - 获取股票列表和K线数据
   - 数据预处理：将Dict格式转换为pandas DataFrame
   - 调用indicators模块计算所有基础指标
   - 将预处理后的数据传递给各个策略
   - 收集和保存策略信号

2. **indicators模块负责**：
   - 提供标准化的技术指标计算函数
   - 包装talib库的常用指标
   - 实现自定义指标（如通道计算、位置关系等）

3. **DualChannelFibonacciStrategy负责**：
   - 接收预处理后的数据（包含所有指标）
   - 实现策略逻辑：正向遍历识别突破模式
   - 返回符合条件的Signal对象

### 模块设计

#### 1. 数据预处理（在after_market_schedule.py中）
- K线数据格式转换（List[Dict] -> pandas DataFrame）
- 调用indicators模块计算所有技术指标
- 数据验证和异常处理

#### 2. 基础指标模块（indicators/）
- **channel_indicators.py**: 通道相关指标
- **position_indicators.py**: 价格位置关系指标  
- **talib_wrapper.py**: talib库包装函数

#### 3. 策略主模块 (DualChannelFibonacciStrategy)
- 实现标准策略接口
- 正向遍历K线数据，识别完整突破周期
- 管理策略参数和配置
- 返回符合条件的Signal对象

## Components and Interfaces

### 1. 主策略类接口

该策略需要集成到现有的`execute_strategies`方法中，与现有策略保持一致的接口：

```python
class DualChannelFibonacciStrategy:
    def __init__(self):
        """初始化策略，加载配置参数"""
        
    def analyze(self, stock_code: str, stock_name: str, kline_data: List[Dict]) -> Optional[Signal]:
        """
        分析单只股票，返回信号对象
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称  
            kline_data: K线数据列表，每个元素包含trade_time, open, high, low, close, volume, amount
            
        Returns:
            Signal对象或None（如果不满足条件）
        """
        
    def get_strategy_name(self) -> str:
        """返回策略名称"""
        return "双通道斐波那契突破"
```

### 2. 集成到after_market_schedule.py

在`execute_strategies`方法中添加该策略：

```python
def execute_strategies(self, stock_code: str, stock_name: str, kline_data: List[Dict]) -> List[Signal]:
    """执行所有策略，返回命中的信号"""
    signals = []

    try:
        # 双通道斐波那契策略
        dual_channel_strategy = DualChannelFibonacciStrategy()
        dual_channel_signal = dual_channel_strategy.analyze(stock_code, stock_name, kline_data)
        if dual_channel_signal:
            signals.append(dual_channel_signal)

    except Exception as e:
        self.logger.error(f"执行策略失败 {stock_code}: {e}")

    return signals
```

### 2. 配置类

```python
@dataclass
class StrategyConfig:
    # 通道参数
    ema_short_1: int = 144      # 通道1短期EMA
    ema_long_1: int = 169       # 通道1长期EMA
    ema_short_2: int = 576      # 通道2短期EMA
    ema_long_2: int = 676       # 通道2长期EMA
    
    # 时间窗参数
    min_days: int = 2           # 最小突破天数
    max_days: int = 62          # 最大突破天数
    pre_check_days: int = 120   # 历史结构验证天数
    pivot_window: int = 10      # 关键点提取窗口
    
    # 成交量参数
    volume_window: int = 20     # 成交量均值计算窗口
    volume_ratio: float = 1.2   # 突破时成交量比率
    
    # 容错参数
    max_intrusion_days: int = 1 # 最大允许影线穿透次数
```

### 3. 信号数据结构

该策略将使用现有的Signal数据类，并充分利用其字段来存储双通道策略的相关信息：

```python
# 使用现有的Signal数据类
signal = Signal(
    stock_code=stock_code,
    stock_name=stock_name,
    strategy_name="双通道斐波那契突破",
    signal_date=date.today(),
    signal_strength=calculated_strength,  # 基于突破强度计算
    
    # 利用现有字段存储策略特定信息
    latest_volume=int(latest_volume),
    avg_volume=int(avg_volume_20d),
    volume_ratio=latest_volume / avg_volume_20d,
    latest_close=float(latest_close),
    max_high_20d=float(target_high_price),  # 复用该字段存储目标高点
    breakout_ratio=(latest_close - start_low_price) / start_low_price,  # 突破幅度
    
    # 在signal_note中存储策略特定的详细信息
    signal_note=f"通道1突破:{break_t1_date},通道2突破:{break_t2_date},"
               f"起始低点:{start_low_price:.2f}({start_low_date}),"
               f"目标高点:{target_high_price:.2f}({target_high_date}),"
               f"EMA144:{ema_144:.2f},EMA169:{ema_169:.2f},"
               f"EMA576:{ema_576:.2f},EMA676:{ema_676:.2f}"
)
```

### 4. 策略特定数据模型

为了内部计算和验证，定义策略特定的数据模型：

```python
@dataclass
class DualChannelResult:
    """双通道策略内部结果模型"""
    is_valid: bool
    break_t1_date: date
    break_t2_date: date
    start_low_date: date
    start_low_price: float
    target_high_date: date
    target_high_price: float
    signal_strength: float
    ema_values: Dict[str, float]  # 存储各EMA值
    volume_info: Dict[str, float]  # 存储成交量信息
    validation_details: str = ""
```

## Data Models

### 1. K线数据模型

```python
@dataclass
class KlineData:
    trade_time: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    amount: float
```

### 2. 通道数据模型

```python
@dataclass
class ChannelData:
    date: datetime
    ema_144: float      # 通道1上轨
    ema_169: float      # 通道1下轨
    ema_576: float      # 通道2上轨
    ema_676: float      # 通道2下轨
    volume_ma: float    # 成交量移动平均
```

### 3. 突破检测结果模型

```python
@dataclass
class BreakoutResult:
    is_valid: bool
    break_t1_index: int = -1
    break_t2_index: int = -1
    start_low_index: int = -1
    target_high_index: int = -1
    intrusion_count: int = 0
    validation_errors: List[str] = field(default_factory=list)
```

## Error Handling

### 1. 数据异常处理

```python
class DataValidationError(Exception):
    """数据验证异常"""
    pass

class InsufficientDataError(Exception):
    """数据不足异常"""
    pass

class CalculationError(Exception):
    """计算异常"""
    pass
```

### 2. 异常处理策略

- **数据不足**: 当K线数据少于最小要求时，记录警告并跳过该股票
- **计算错误**: 当EMA计算失败时，记录错误并返回空信号列表
- **验证失败**: 当信号验证失败时，记录详细的失败原因
- **系统异常**: 捕获所有未预期异常，记录完整错误信息并继续处理下一只股票

### 3. 日志记录

```python
# 使用结构化日志记录
logger.info(f"开始分析股票: {stock_code}")
logger.debug(f"K线数据长度: {len(kline_data)}")
logger.warning(f"数据不足，跳过股票: {stock_code}")
logger.error(f"计算EMA失败: {stock_code}, 错误: {str(e)}")
```

## Testing Strategy

### 1. 单元测试

#### 测试覆盖范围
- EMA计算准确性测试
- 通道突破检测逻辑测试
- 信号验证规则测试
- 关键点提取算法测试
- 边界条件测试

#### 测试数据准备
```python
def create_test_kline_data():
    """创建测试用K线数据"""
    # 构造包含明确突破模式的测试数据
    # 包含边界情况和异常情况
```

### 2. 集成测试

#### 测试场景
- 完整策略流程测试
- 与数据库交互测试
- 多股票并发处理测试
- 异常情况恢复测试

### 3. 性能测试

#### 性能指标
- 单只股票处理时间 < 100ms
- 1000只股票批量处理时间 < 5分钟
- 内存使用峰值 < 1GB
- 数据库连接数 < 20

### 4. 回测验证

#### 历史数据验证
- 使用历史数据验证策略逻辑
- 统计信号准确率和收益率
- 分析假信号产生原因
- 优化策略参数

## Implementation Approach

### 1. 正向遍历算法

策略采用正向遍历的方式，扫描整个历史数据，识别每一个完整的突破周期：

```python
def detect_signals_forward(self, df: pd.DataFrame) -> List[DualChannelResult]:
    """正向遍历检测信号"""
    signals = []
    i = 0
    
    while i < len(df):
        # 1. 寻找潜在起点（股价低于通道1下轨）
        if df.iloc[i]['close'] >= df.iloc[i]['channel1_lower']:
            i += 1
            continue
            
        start_index = i
        
        # 2. 寻找通道1突破点
        t1_break_index = self._find_channel1_breakout_forward(df, start_index)
        if t1_break_index == -1:
            i += 1
            continue
            
        # 3. 寻找通道2突破点
        t2_break_index = self._find_channel2_breakout_forward(df, t1_break_index)
        if t2_break_index == -1:
            i = t1_break_index + 1
            continue
            
        # 4. 验证所有条件
        if self._validate_breakout_sequence(df, start_index, t1_break_index, t2_break_index):
            # 5. 提取关键点并创建信号
            signal = self._create_signal(df, start_index, t1_break_index, t2_break_index)
            signals.append(signal)
            
        # 跳过当前周期，继续寻找下一个
        i = t2_break_index + 1
        
    return signals
```

### 2. 基础指标计算接口

在indicators模块中定义标准化的指标计算接口：

```python
# indicators/channel_indicators.py
def calculate_dual_channels(df: pd.DataFrame, 
                          channel1_params: Dict, 
                          channel2_params: Dict) -> pd.DataFrame:
    """计算双通道指标"""
    
# indicators/position_indicators.py  
def calculate_price_position(df: pd.DataFrame) -> pd.DataFrame:
    """计算价格与通道的位置关系"""
    
# indicators/talib_wrapper.py
def calculate_ema_series(df: pd.DataFrame, periods: List[int]) -> pd.DataFrame:
    """批量计算多个周期的EMA"""
```

### 2. 性能优化策略

#### 数据预处理优化
- 使用numpy数组进行向量化计算
- 预计算所有EMA值，避免重复计算
- 使用滑动窗口算法优化成交量均值计算

#### 内存优化
- 及时释放不需要的中间数据
- 使用生成器处理大量股票数据
- 限制同时处理的股票数量

#### 并发优化
- 支持多线程并行处理
- 合理设置线程池大小
- 避免数据库连接竞争

### 3. 可扩展性设计

#### 参数配置化
- 所有策略参数通过配置文件管理
- 支持运行时参数调整
- 提供参数验证机制

#### 模块化设计
- 各功能模块独立，便于单独测试和维护
- 清晰的接口定义，便于扩展新功能
- 支持策略组合和链式调用

#### 监控和调试
- 提供详细的执行统计信息
- 支持调试模式输出中间结果
- 集成性能监控和报警机制