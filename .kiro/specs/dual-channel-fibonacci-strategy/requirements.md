# Requirements Document

## Introduction

双通道斐波那契突破策略是一个用于股票筛选的量化交易策略。该策略通过分析股票的历史日K线数据，识别满足特定条件的通道突破信号。策略基于两个EMA通道（通道1: EMA144/169，通道2: EMA576/676），寻找从通道1下方开始，依次突破通道1和通道2的完整周期，并记录每个有效的突破信号。

## Requirements

### Requirement 1

**User Story:** 作为量化交易系统用户，我希望能够对股票历史数据进行双通道突破分析，以便识别潜在的交易机会。

#### Acceptance Criteria

1. WHEN 系统接收到股票代码 THEN 系统 SHALL 获取该股票的完整历史日K线数据
2. WHEN 获取K线数据后 THEN 系统 SHALL 计算通道1（EMA144上轨，EMA169下轨）和通道2（EMA576上轨，EMA676下轨）
3. WHEN 计算完成后 THEN 系统 SHALL 返回包含开高低收价格、成交量和通道数据的完整数据集

### Requirement 2

**User Story:** 作为策略分析师，我希望系统能够识别完整的突破周期，以便确保信号的有效性。

#### Acceptance Criteria

1. WHEN 股票收盘价低于通道1下轨 THEN 系统 SHALL 标记为潜在起点
2. WHEN 从潜在起点开始扫描 THEN 系统 SHALL 寻找第一次突破通道1上轨的K线
3. WHEN 找到通道1突破点 THEN 系统 SHALL 验证该突破满足成交量条件（成交量大于前N日均量）
4. WHEN 通道1突破后 THEN 系统 SHALL 继续寻找第一次突破通道2上轨的K线
5. WHEN 找到通道2突破点 THEN 系统 SHALL 验证该突破也满足成交量条件

### Requirement 3

**User Story:** 作为风险控制人员，我希望系统能够验证突破的连续性和稳定性，以便过滤掉虚假突破。

#### Acceptance Criteria

1. WHEN 通道1突破后到通道2突破前 THEN 系统 SHALL 验证收盘价始终高于通道1上轨
2. WHEN 检查连续性时 THEN 系统 SHALL 允许最多1次影线式跌破（最低价跌破通道1下轨但收盘价仍在上轨之上）
3. WHEN 通道1突破到通道2突破之间 THEN 系统 SHALL 验证时间跨度在[min_days, max_days]范围内
4. WHEN 验证历史结构时 THEN 系统 SHALL 确保通道1上轨在前pre_check_days天内始终低于通道2下轨

### Requirement 4

**User Story:** 作为交易员，我希望系统能够准确记录每个有效信号的关键信息，以便后续分析和决策。

#### Acceptance Criteria

1. WHEN 所有条件都满足时 THEN 系统 SHALL 记录一条完整的信号记录
2. WHEN 记录信号时 THEN 系统 SHALL 包含股票代码、突破日期、起始低点、目标高点等关键字段
3. WHEN 提取关键点时 THEN 系统 SHALL 在通道1突破前pivot_window天内找到起始低点
4. WHEN 提取目标点时 THEN 系统 SHALL 在通道2突破后pivot_window天内找到目标高点

### Requirement 5

**User Story:** 作为系统管理员，我希望策略能够避免重复信号，以便保持信号的独特性和有效性。

#### Acceptance Criteria

1. WHEN 记录一条信号后 THEN 系统 SHALL 跳过当前周期直到股价重新跌回通道1下方
2. WHEN 股价重新跌回通道1下方 THEN 系统 SHALL 重新开始寻找下一个潜在起点
3. WHEN 处理多个信号时 THEN 系统 SHALL 确保每个信号代表一个独立的突破周期

### Requirement 6

**User Story:** 作为开发人员，我希望策略能够与现有的after_market_schedule.py进程集成，以便实现自动化股票筛选。

#### Acceptance Criteria

1. WHEN after_market_schedule.py调用策略时 THEN 系统 SHALL 提供标准化的接口
2. WHEN 策略执行完成时 THEN 系统 SHALL 返回符合数据库表结构的信号数据
3. WHEN 集成到现有流程时 THEN 系统 SHALL 支持批量处理多只股票
4. WHEN 处理异常情况时 THEN 系统 SHALL 提供适当的错误处理和日志记录