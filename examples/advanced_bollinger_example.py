#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高阶布林带因子使用示例

展示重构后的高阶布林带因子的完整功能。

重构成果：
1. 波动率建模：BBW历史分位数分析，压缩与扩张检测
2. 突破验证：成交量确认、趋势验证、假突破过滤
3. 贴轨效应：量化强势/弱势趋势识别
4. 中轨确认：支撑/阻力二次确认机制
5. 多维信号：8个基础因子 + 4个特征工程因子
6. Qlib支持：完整的参数优化和机器学习训练支持
7. 向后兼容：保持原有接口不变

作者: QuantFM Team
创建时间: 2025-08-23
"""

import sys
import os
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from factors import BollingerFactor, BollingerConfig, BollingerSqueezeReleaseFactor
from data.db_manager import get_db_manager

def get_test_data(stock_code='000001', days=500):
    """获取测试数据"""
    try:
        db_manager = get_db_manager()
        sql = '''
        SELECT trade_time, open, high, low, close, volume
        FROM stock_kline_day
        WHERE stock_code = %s
        ORDER BY trade_time DESC
        LIMIT %s
        '''
        
        results = db_manager.fetch_all(sql, (stock_code, days))
        df = pd.DataFrame(results)
        df = df.sort_values('trade_time').reset_index(drop=True)
        
        print(f"📊 获取到 {len(df)} 条数据")
        print(f"时间范围: {df['trade_time'].min().strftime('%Y-%m-%d')} 到 {df['trade_time'].max().strftime('%Y-%m-%d')}")
        
        return df
        
    except Exception as e:
        print(f"❌ 数据获取失败: {e}")
        return None

def demo_basic_usage():
    """演示基础使用方法"""
    print("🔧 基础使用方法演示")
    print("=" * 60)
    
    df = get_test_data()
    if df is None:
        return
    
    # 1. 使用默认配置
    print("\n📋 默认配置:")
    bb_factor = BollingerFactor(scenario="production")
    df_result = bb_factor.calculate(df.copy())
    
    if isinstance(df_result, pd.DataFrame):
        factor_names = bb_factor.get_factor_names()
        print(f"  生成因子数量: {len(factor_names)}")
        print(f"  因子名称: {factor_names[:5]}...")  # 显示前5个
        
        # 显示最新的因子值
        latest_values = {}
        for name in factor_names[:8]:  # 显示前8个因子的最新值
            latest_values[name] = df_result[name].iloc[-1]
        
        print(f"  最新因子值:")
        for name, value in latest_values.items():
            print(f"    {name}: {value:.4f}")
    
    # 2. 布尔模式
    print("\n🔘 布尔模式:")
    bb_bool = BollingerFactor(scenario="development")
    result_bool = bb_bool.calculate(df.copy())
    
    if hasattr(result_bool, 'metadata'):
        signals = result_bool.metadata.get('signals', {})
        active_signals = [k for k, v in signals.items() if v]
        print(f"  活跃信号: {active_signals}")
        print(f"  信号强度: {result_bool.signal_strength}")
        print(f"  因子值: {result_bool.factor_value:.4f}")

def demo_advanced_features():
    """演示高阶功能"""
    print("\n🚀 高阶功能演示")
    print("=" * 60)
    
    df = get_test_data(days=300)
    if df is None:
        return
    
    # 1. 波动率分析
    print("\n📊 波动率分析:")
    bb_factor = BollingerFactor(scenario="production")
    df_result = bb_factor.calculate(df.copy())
    
    if isinstance(df_result, pd.DataFrame):
        # 分析最近的波动率状态
        recent_data = df_result.tail(10)
        
        print(f"  最近BBW分位数: {recent_data['bbw_percentile'].iloc[-1]:.2f}")
        print(f"  压缩强度: {recent_data['bb_squeeze_strength'].iloc[-1]:.4f}")
        print(f"  扩张强度: {recent_data['bb_expansion_strength'].iloc[-1]:.4f}")
        
        # 统计波动率状态分布
        if 'bb_volatility_regime' in df_result.columns:
            regime_counts = df_result['bb_volatility_regime'].value_counts()
            print(f"  波动率状态分布: 低波动{regime_counts.get(0, 0)}天, 正常{regime_counts.get(1, 0)}天, 高波动{regime_counts.get(2, 0)}天")
    
    # 2. 突破分析
    print("\n🎯 突破分析:")
    if isinstance(df_result, pd.DataFrame):
        breakout_strength = df_result['bb_breakout_strength'].iloc[-10:]
        positive_breakouts = (breakout_strength > 0.1).sum()
        negative_breakouts = (breakout_strength < -0.1).sum()
        
        print(f"  最近10天向上突破: {positive_breakouts}次")
        print(f"  最近10天向下突破: {negative_breakouts}次")
        print(f"  当前突破强度: {breakout_strength.iloc[-1]:.4f}")
    
    # 3. 贴轨效应分析
    print("\n📈 贴轨效应分析:")
    if isinstance(df_result, pd.DataFrame):
        walking_strength = df_result['bb_walking_strength'].iloc[-10:]
        upper_walking = (walking_strength > 0.5).sum()
        lower_walking = (walking_strength < -0.5).sum()
        
        print(f"  最近10天上轨贴轨: {upper_walking}次")
        print(f"  最近10天下轨贴轨: {lower_walking}次")
        print(f"  当前贴轨强度: {walking_strength.iloc[-1]:.4f}")

def demo_custom_configuration():
    """演示自定义配置"""
    print("\n⚙️ 自定义配置演示")
    print("=" * 60)
    
    df = get_test_data(days=200)
    if df is None:
        return
    
    # 1. 配置对象方式
    print("\n📋 配置对象方式:")
    custom_config = BollingerConfig(
        period=15,
        std_dev=1.8,
        squeeze_threshold=0.15,
        expansion_threshold=0.85,
        output_mode="continuous",
        feature_engineering=True
    )
    
    bb_custom = BollingerFactor(config=custom_config)
    df_custom = bb_custom.calculate(df.copy())
    
    if isinstance(df_custom, pd.DataFrame):
        print(f"  自定义参数: 周期{custom_config.period}, 标准差{custom_config.std_dev}")
        print(f"  压缩阈值: {custom_config.squeeze_threshold * 100:.1f}%分位数")
        print(f"  生成因子: {len(bb_custom.get_factor_names())}个")
    
    # 2. 直接传参方式
    print("\n🔧 直接传参方式:")
    bb_direct = BollingerFactor(
        scenario="production",
        period=25,
        std_dev=2.2,
        breakout_volume_ratio=1.5,
        walking_days=5
    )
    
    df_direct = bb_direct.calculate(df.copy())
    print(f"  直接传参: 周期{bb_direct.config.period}, 成交量比率{bb_direct.config.breakout_volume_ratio}")

def demo_performance_comparison():
    """演示性能对比"""
    print("\n⚡ 性能对比演示")
    print("=" * 60)
    
    df = get_test_data(days=1000)  # 使用更多数据
    if df is None:
        return
    
    print(f"\n📊 处理{len(df)}条数据的性能:")
    
    # 测试不同配置的性能
    configs = {
        "基础配置": BollingerFactor(scenario="development"),
        "生产配置": BollingerFactor(scenario="production"),
        "Qlib配置": BollingerFactor(scenario="qlib_training")
    }
    
    for name, factor in configs.items():
        # 预热
        factor.calculate(df.head(100))
        
        # 性能测试
        times = []
        for i in range(3):
            start = time.perf_counter()
            result = factor.calculate(df.copy())
            end = time.perf_counter()
            times.append((end - start) * 1000)
        
        avg_time = sum(times) / len(times)
        factor_count = len(factor.get_factor_names())
        throughput = len(df) / avg_time * 1000
        
        print(f"  {name}: {avg_time:.2f}ms, {factor_count}个因子, {throughput:.0f}条/秒")

def demo_signal_analysis():
    """演示信号分析"""
    print("\n📊 信号分析演示")
    print("=" * 60)
    
    df = get_test_data(days=400)
    if df is None:
        return
    
    # 布尔模式信号分析
    print("\n🔘 布尔信号分析:")
    bb_bool = BollingerFactor(scenario="development")
    
    # 分析历史信号
    signal_history = []
    for i in range(len(df) - 50, len(df)):  # 分析最近50天
        subset = df.iloc[:i+1]
        if len(subset) >= 100:  # 确保有足够数据
            result = bb_bool.calculate(subset)
            if hasattr(result, 'metadata') and 'signals' in result.metadata:
                signals = result.metadata['signals']
                signal_history.append({
                    'date': subset['trade_time'].iloc[-1],
                    'price': subset['close'].iloc[-1],
                    **signals
                })
    
    if signal_history:
        signal_df = pd.DataFrame(signal_history)
        
        # 统计各种信号的出现次数
        signal_cols = [col for col in signal_df.columns if col.startswith('bb_')]
        signal_counts = {}
        for col in signal_cols:
            signal_counts[col] = signal_df[col].sum()
        
        print(f"  最近50天信号统计:")
        for signal, count in signal_counts.items():
            if count > 0:
                print(f"    {signal}: {count}次")

def demo_backward_compatibility():
    """演示向后兼容性"""
    print("\n🔄 向后兼容性演示")
    print("=" * 60)
    
    df = get_test_data(days=200)
    if df is None:
        return
    
    # 1. 原有BollingerSqueezeReleaseFactor接口
    print("\n📋 原有接口测试:")
    squeeze_factor = BollingerSqueezeReleaseFactor(
        period=20,
        std_dev=2.0,
        squeeze_threshold=0.1,
        lookback_days=10
    )
    
    result = squeeze_factor.calculate(df)
    if result:
        print(f"  因子名称: {result.factor_name}")
        print(f"  因子值: {result.factor_value:.4f}")
        print(f"  信号强度: {result.signal_strength}")
        
        if hasattr(result, 'metadata'):
            metadata = result.metadata
            print(f"  收缩状态: {metadata.get('is_squeezed', False)}")
            print(f"  释放状态: {metadata.get('is_releasing', False)}")
    
    print("\n✅ 向后兼容性验证:")
    print("  - 原有接口保持不变")
    print("  - 使用新的高阶算法")
    print("  - 提供更准确的信号")

def demo_qlib_integration():
    """演示Qlib集成"""
    print("\n🤖 Qlib集成演示")
    print("=" * 60)
    
    df = get_test_data(days=200)
    if df is None:
        return
    
    print("\n📋 参数空间定义:")
    param_space = BollingerFactor.get_param_space()
    print(f"  可优化参数: {len(param_space)}个")
    
    # 显示部分参数空间
    for param_name, param_def in list(param_space.items())[:5]:
        print(f"    {param_name}: {param_def['range']} (默认: {param_def['default']})")
    
    print("\n🔧 参数创建测试:")
    # 测试从参数创建因子
    sample_params = {
        'period': 15,
        'std_dev': 1.8,
        'squeeze_threshold': 0.15,
        'expansion_threshold': 0.85
    }
    
    factor_from_params = BollingerFactor.create_from_params(sample_params, scenario="qlib_training")
    df_result = factor_from_params.calculate(df.copy())
    
    if isinstance(df_result, pd.DataFrame):
        print(f"  参数创建成功: {len(factor_from_params.get_factor_names())}个因子")
        print(f"  配置参数: 周期{factor_from_params.config.period}, 标准差{factor_from_params.config.std_dev}")

def main():
    """主函数"""
    print("🚀 高阶布林带因子系统演示")
    print("=" * 60)
    
    try:
        # 1. 基础使用方法
        demo_basic_usage()
        
        # 2. 高阶功能
        demo_advanced_features()
        
        # 3. 自定义配置
        demo_custom_configuration()
        
        # 4. 性能对比
        demo_performance_comparison()
        
        # 5. 信号分析
        demo_signal_analysis()
        
        # 6. 向后兼容性
        demo_backward_compatibility()
        
        # 7. Qlib集成
        demo_qlib_integration()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成！")
        
        print("\n💡 高阶布林带因子特性总结:")
        print("✅ 波动率建模: BBW历史分位数分析，压缩与扩张检测")
        print("✅ 突破验证: 成交量确认、趋势验证、假突破过滤")
        print("✅ 贴轨效应: 量化强势/弱势趋势识别")
        print("✅ 中轨确认: 支撑/阻力二次确认机制")
        print("✅ 多维信号: 8个基础因子 + 4个特征工程因子")
        print("✅ Qlib支持: 完整的参数优化和ML训练功能")
        print("✅ 向后兼容: 保持原有接口不变")
        print("✅ 高性能: 向量化实现，显著提升计算效率")
        
        print("\n🔧 使用建议:")
        print("1. 生产环境使用continuous模式，获得更丰富的因子")
        print("2. 开发测试使用boolean模式，快速验证信号")
        print("3. 结合成交量和趋势指标提高信号质量")
        print("4. 利用BBW分位数识别波动率压缩机会")
        print("5. 通过贴轨效应判断趋势强度")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
