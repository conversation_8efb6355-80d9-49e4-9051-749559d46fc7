#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一背离因子使用示例

展示重构后的MACD、KDJ、RSI背离因子的统一使用方法。

重构成果：
1. 统一架构：所有背离因子使用相同的基类和配置系统
2. 统一配置：通过divergence_params.yaml统一管理所有参数
3. 向量化实现：显著提升计算性能
4. Qlib支持：完整的参数优化和机器学习训练支持
5. 多输出模式：布尔信号和连续数值两种模式
6. 向后兼容：保持原有接口不变

作者: QuantFM Team
创建时间: 2025-08-23
"""

import sys
import os
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from factors import (
    MACDDivergenceFactor, MACDDivergenceConfig,
    KDJDivergenceFactor, KDJDivergenceConfig,
    RSIDivergenceFactor, RSIDivergenceConfig,
    create_divergence_factor, compare_divergence_signals
)
from data.db_manager import get_db_manager

def get_test_data(stock_code='000001', days=300):
    """获取测试数据"""
    try:
        db_manager = get_db_manager()
        sql = '''
        SELECT trade_time, open, high, low, close, volume
        FROM stock_kline_day
        WHERE stock_code = %s
        ORDER BY trade_time DESC
        LIMIT %s
        '''
        
        results = db_manager.fetch_all(sql, (stock_code, days))
        df = pd.DataFrame(results)
        df = df.sort_values('trade_time').reset_index(drop=True)
        
        print(f"📊 获取到 {len(df)} 条数据")
        print(f"时间范围: {df['trade_time'].min().strftime('%Y-%m-%d')} 到 {df['trade_time'].max().strftime('%Y-%m-%d')}")
        
        return df
        
    except Exception as e:
        print(f"❌ 数据获取失败: {e}")
        return None

def demo_unified_usage():
    """演示统一使用方法"""
    print("🔄 统一使用方法演示")
    print("=" * 60)
    
    df = get_test_data()
    if df is None:
        return
    
    # 1. 使用工厂函数创建因子（推荐方式）
    print("\n🏭 工厂函数创建:")
    
    for indicator in ['macd', 'kdj', 'rsi']:
        # 布尔模式
        bool_factor = create_divergence_factor(indicator, scenario="development")
        df_bool = bool_factor.calculate(df.copy())
        
        # 连续模式
        cont_factor = create_divergence_factor(indicator, scenario="production")
        df_cont = cont_factor.calculate(df.copy())
        
        print(f"  {indicator.upper()}: 布尔模式{len(bool_factor.get_factor_names())}个因子, 连续模式{len(cont_factor.get_factor_names())}个因子")
    
    # 2. 直接创建因子类
    print("\n🔧 直接创建因子类:")
    
    # MACD背离因子
    macd_factor = MACDDivergenceFactor(scenario="development")
    df_macd = macd_factor.calculate(df.copy())
    macd_signals = sum([df_macd[col].sum() for col in df_macd.columns if col.startswith('macd') and col.endswith(('_low', '_high'))])
    print(f"  MACD: {macd_signals}个背离信号")
    
    # KDJ背离因子
    kdj_factor = KDJDivergenceFactor(scenario="development")
    df_kdj = kdj_factor.calculate(df.copy())
    kdj_signals = sum([df_kdj[col].sum() for col in df_kdj.columns if col.startswith('kdj') and col.endswith(('_low', '_high'))])
    print(f"  KDJ: {kdj_signals}个背离信号")
    
    # RSI背离因子
    rsi_factor = RSIDivergenceFactor(scenario="development")
    df_rsi = rsi_factor.calculate(df.copy())
    rsi_signals = sum([df_rsi[col].sum() for col in df_rsi.columns if col.startswith('rsi') and col.endswith(('_low', '_high'))])
    print(f"  RSI: {rsi_signals}个背离信号")

def demo_custom_configuration():
    """演示自定义配置"""
    print("\n⚙️ 自定义配置演示")
    print("=" * 60)
    
    df = get_test_data(days=200)
    if df is None:
        return
    
    # 1. 使用配置对象
    print("\n📋 配置对象方式:")
    
    # 自定义MACD配置
    macd_config = MACDDivergenceConfig(
        output_mode="boolean",
        fastperiod=8,
        slowperiod=20,
        signalperiod=5,
        min_price_change=0.005,
        normalize_output=False
    )
    macd_factor = MACDDivergenceFactor(config=macd_config)
    df_macd = macd_factor.calculate(df.copy())
    
    signals = sum([df_macd[col].sum() for col in df_macd.columns if col.startswith('macd') and col.endswith(('_low', '_high'))])
    print(f"  自定义MACD: {signals}个信号 (参数: {macd_config.fastperiod}/{macd_config.slowperiod}/{macd_config.signalperiod})")
    
    # 2. 直接传参方式
    print("\n🔧 直接传参方式:")
    
    kdj_factor = KDJDivergenceFactor(
        scenario="development",
        fastk_period=6,
        slowk_period=2,
        min_bars_between=3,
        min_price_change=0.005
    )
    df_kdj = kdj_factor.calculate(df.copy())
    
    signals = sum([df_kdj[col].sum() for col in df_kdj.columns if col.startswith('kdj') and col.endswith(('_low', '_high'))])
    print(f"  自定义KDJ: {signals}个信号 (K周期: {kdj_factor.config.fastk_period})")

def demo_performance_comparison():
    """演示性能对比"""
    print("\n⚡ 性能对比演示")
    print("=" * 60)
    
    df = get_test_data(days=1000)  # 使用更多数据
    if df is None:
        return
    
    print(f"\n📊 处理{len(df)}条数据的性能:")
    
    for indicator in ['macd', 'kdj', 'rsi']:
        factor = create_divergence_factor(indicator, scenario="production")
        
        # 预热
        factor.calculate(df.head(100))
        
        # 性能测试
        times = []
        for i in range(3):
            start = time.perf_counter()
            result = factor.calculate(df.copy())
            end = time.perf_counter()
            times.append((end - start) * 1000)
        
        avg_time = sum(times) / len(times)
        factor_count = len(factor.get_factor_names())
        throughput = len(df) / avg_time * 1000
        
        print(f"  {indicator.upper()}: {avg_time:.2f}ms, {factor_count}个因子, {throughput:.0f}条/秒")

def demo_signal_analysis():
    """演示信号分析"""
    print("\n📊 信号分析演示")
    print("=" * 60)
    
    df = get_test_data(days=400)
    if df is None:
        return
    
    # 使用信号对比功能
    print("\n📈 各指标背离信号统计:")
    comparison = compare_divergence_signals(df, scenario="development")
    
    for indicator, stats in comparison.items():
        if 'error' in stats:
            print(f"  {indicator.upper()}: ❌ {stats['error']}")
        else:
            buy = stats.get('buy_signals', 0)
            sell = stats.get('sell_signals', 0)
            total = stats.get('total_signals', 0)
            print(f"  {indicator.upper()}: 买入{buy}个, 卖出{sell}个, 总计{total}个")
    
    # 分析最近的信号
    print(f"\n🔍 最近的背离信号:")
    
    for indicator in ['macd', 'kdj', 'rsi']:
        try:
            factor = create_divergence_factor(indicator, scenario="development")
            df_result = factor.calculate(df.copy())
            
            # 找到最近的买入信号
            buy_col = f'{indicator}_upup_low'
            if buy_col in df_result.columns:
                recent_buy = df_result[df_result[buy_col] == True].tail(1)
                if len(recent_buy) > 0:
                    date = recent_buy.iloc[0]['trade_time'].strftime('%Y-%m-%d')
                    price = recent_buy.iloc[0]['close']
                    print(f"  {indicator.upper()}最近买入信号: {date} @ {price:.2f}")
                    
        except Exception as e:
            print(f"  {indicator.upper()}: 分析失败 - {e}")

def demo_qlib_integration():
    """演示Qlib集成"""
    print("\n🤖 Qlib集成演示")
    print("=" * 60)
    
    df = get_test_data(days=200)
    if df is None:
        return
    
    print("\n📋 参数空间定义:")
    
    for indicator in ['macd', 'kdj', 'rsi']:
        try:
            factor_class = {
                'macd': MACDDivergenceFactor,
                'kdj': KDJDivergenceFactor,
                'rsi': RSIDivergenceFactor
            }[indicator]
            
            param_space = factor_class.get_param_space()
            print(f"  {indicator.upper()}: {len(param_space)}个可优化参数")
            
            # 显示部分参数空间
            for param_name, param_def in list(param_space.items())[:3]:
                print(f"    {param_name}: {param_def['range']} (默认: {param_def['default']})")
            
        except Exception as e:
            print(f"  {indicator.upper()}: ❌ {e}")

def main():
    """主函数"""
    print("🚀 统一背离因子系统演示")
    print("=" * 60)
    
    try:
        # 1. 统一使用方法
        demo_unified_usage()
        
        # 2. 自定义配置
        demo_custom_configuration()
        
        # 3. 性能对比
        demo_performance_comparison()
        
        # 4. 信号分析
        demo_signal_analysis()
        
        # 5. Qlib集成
        demo_qlib_integration()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成！")
        
        print("\n💡 重构成果总结:")
        print("✅ 统一架构: MACD、KDJ、RSI使用相同的基类和接口")
        print("✅ 统一配置: 通过divergence_params.yaml统一管理")
        print("✅ 向量化实现: 显著提升计算性能")
        print("✅ Qlib支持: 完整的参数优化和ML训练功能")
        print("✅ 多输出模式: 布尔信号和连续数值两种模式")
        print("✅ 向后兼容: 保持原有MACD因子接口不变")
        
        print("\n🔧 使用建议:")
        print("1. 使用工厂函数create_divergence_factor()创建因子")
        print("2. 根据使用场景选择合适的scenario参数")
        print("3. 通过配置文件或直接传参自定义参数")
        print("4. 布尔模式适合策略信号，连续模式适合ML训练")
        print("5. 结合多个指标的背离信号提高准确性")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
