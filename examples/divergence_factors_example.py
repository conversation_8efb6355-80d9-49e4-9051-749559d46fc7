#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
背离因子综合示例

演示MACD、KDJ、RSI三种背离因子的使用方法和对比分析。

作者: QuantFM Team
创建时间: 2025-08-23
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from factors.divergence import (
    KDJDivergenceFactor, RSIDivergenceFactor,
    create_divergence_factor, compare_divergence_signals
)
from factors import MACDDivergenceFactor
from data.db_manager import get_db_manager

def get_test_data(stock_code='000001', days=300):
    """获取测试数据"""
    try:
        db_manager = get_db_manager()
        sql = '''
        SELECT trade_time, open, high, low, close, volume
        FROM stock_kline_day
        WHERE stock_code = %s
        ORDER BY trade_time DESC
        LIMIT %s
        '''
        
        results = db_manager.fetch_all(sql, (stock_code, days))
        df = pd.DataFrame(results)
        df = df.sort_values('trade_time').reset_index(drop=True)
        
        print(f"📊 获取到 {len(df)} 条数据")
        print(f"时间范围: {df['trade_time'].min().strftime('%Y-%m-%d')} 到 {df['trade_time'].max().strftime('%Y-%m-%d')}")
        
        return df
        
    except Exception as e:
        print(f"❌ 数据获取失败: {e}")
        return None

def test_individual_factors():
    """测试各个背离因子"""
    print("🔍 测试各个背离因子")
    print("=" * 60)
    
    df = get_test_data()
    if df is None:
        return
    
    # 测试MACD背离因子
    print("\n📈 MACD背离因子测试:")
    try:
        macd_factor = MACDDivergenceFactor(scenario="development")
        df_macd = macd_factor.calculate(df.copy())
        
        macd_signals = {
            'buy': df_macd['macd_upup_low'].sum(),
            'sell': df_macd['macd_downdown_high'].sum(),
            'uptrend': df_macd['macd_upup_high'].sum(),
            'downtrend': df_macd['macd_downdown_low'].sum()
        }
        
        print(f"  底背离(买入): {macd_signals['buy']}个")
        print(f"  顶背离(卖出): {macd_signals['sell']}个")
        print(f"  上涨延续: {macd_signals['uptrend']}个")
        print(f"  下跌延续: {macd_signals['downtrend']}个")
        
    except Exception as e:
        print(f"  ❌ MACD测试失败: {e}")
    
    # 测试KDJ背离因子
    print("\n📊 KDJ背离因子测试:")
    try:
        kdj_factor = KDJDivergenceFactor(scenario="development")
        df_kdj = kdj_factor.calculate(df.copy())
        
        kdj_signals = {
            'buy': df_kdj['kdj_upup_low'].sum(),
            'sell': df_kdj['kdj_downdown_high'].sum(),
            'uptrend': df_kdj['kdj_upup_high'].sum(),
            'downtrend': df_kdj['kdj_downdown_low'].sum()
        }
        
        print(f"  底背离(买入): {kdj_signals['buy']}个")
        print(f"  顶背离(卖出): {kdj_signals['sell']}个")
        print(f"  上涨延续: {kdj_signals['uptrend']}个")
        print(f"  下跌延续: {kdj_signals['downtrend']}个")
        
        # 显示KDJ当前状态
        kdj_status = kdj_factor.get_kdj_status(df_kdj)
        print(f"  当前KDJ状态: K={kdj_status.get('k_value', 0):.1f}, D={kdj_status.get('d_value', 0):.1f}")
        print(f"  超买超卖: {'超买' if kdj_status.get('overbought') else '超卖' if kdj_status.get('oversold') else '正常'}")
        
    except Exception as e:
        print(f"  ❌ KDJ测试失败: {e}")
    
    # 测试RSI背离因子
    print("\n📉 RSI背离因子测试:")
    try:
        rsi_factor = RSIDivergenceFactor(scenario="development")
        df_rsi = rsi_factor.calculate(df.copy())
        
        rsi_signals = {
            'buy': df_rsi['rsi_upup_low'].sum(),
            'sell': df_rsi['rsi_downdown_high'].sum(),
            'uptrend': df_rsi['rsi_upup_high'].sum(),
            'downtrend': df_rsi['rsi_downdown_low'].sum()
        }
        
        print(f"  底背离(买入): {rsi_signals['buy']}个")
        print(f"  顶背离(卖出): {rsi_signals['sell']}个")
        print(f"  上涨延续: {rsi_signals['uptrend']}个")
        print(f"  下跌延续: {rsi_signals['downtrend']}个")
        
        # 显示RSI当前状态
        rsi_status = rsi_factor.get_rsi_status(df_rsi)
        print(f"  当前RSI: {rsi_status.get('rsi_value', 0):.1f}")
        print(f"  状态: {'超买' if rsi_status.get('overbought') else '超卖' if rsi_status.get('oversold') else '正常'}")
        print(f"  风险等级: {rsi_status.get('risk_level', 'unknown')}")
        
    except Exception as e:
        print(f"  ❌ RSI测试失败: {e}")

def test_factory_functions():
    """测试工厂函数"""
    print("\n🏭 测试工厂函数")
    print("=" * 60)
    
    df = get_test_data(days=200)
    if df is None:
        return
    
    # 测试单个因子创建
    print("\n📋 单个因子创建测试:")
    for indicator in ['macd', 'kdj', 'rsi']:
        try:
            factor = create_divergence_factor(indicator, scenario="development")
            df_result = factor.calculate(df.copy())
            factor_names = factor.get_factor_names()
            
            print(f"  {indicator.upper()}: ✅ 创建成功，生成{len(factor_names)}个因子")
            
        except Exception as e:
            print(f"  {indicator.upper()}: ❌ 创建失败 - {e}")
    
    # 测试信号对比
    print("\n📊 背离信号对比:")
    try:
        comparison = compare_divergence_signals(df, scenario="development")
        
        for indicator, stats in comparison.items():
            if 'error' in stats:
                print(f"  {indicator.upper()}: ❌ {stats['error']}")
            else:
                buy = stats.get('buy_signals', 0)
                sell = stats.get('sell_signals', 0)
                total = stats.get('total_signals', 0)
                print(f"  {indicator.upper()}: 买入{buy}个, 卖出{sell}个, 总计{total}个")
                
    except Exception as e:
        print(f"  ❌ 信号对比失败: {e}")

def test_continuous_mode():
    """测试连续数值模式"""
    print("\n📈 连续数值模式测试")
    print("=" * 60)
    
    df = get_test_data(days=200)
    if df is None:
        return
    
    for indicator in ['kdj', 'rsi']:
        try:
            print(f"\n📊 {indicator.upper()}连续模式:")
            
            factor = create_divergence_factor(indicator, scenario="production")
            df_result = factor.calculate(df.copy())
            
            # 统计连续信号
            bullish_col = f'{indicator}_bullish_divergence'
            bearish_col = f'{indicator}_bearish_divergence'
            
            if bullish_col in df_result.columns:
                bullish_signals = (df_result[bullish_col] > 0).sum()
                bearish_signals = (df_result[bearish_col] > 0).sum()
                
                print(f"  看涨背离信号: {bullish_signals}个")
                print(f"  看跌背离信号: {bearish_signals}个")
                
                # 显示最强的信号
                max_bullish = df_result[bullish_col].max()
                max_bearish = df_result[bearish_col].max()
                
                print(f"  最强看涨信号: {max_bullish:.4f}")
                print(f"  最强看跌信号: {max_bearish:.4f}")
            
        except Exception as e:
            print(f"  ❌ {indicator.upper()}连续模式测试失败: {e}")

def analyze_signal_timing():
    """分析信号时机"""
    print("\n⏰ 信号时机分析")
    print("=" * 60)
    
    df = get_test_data(days=300)
    if df is None:
        return
    
    # 获取所有背离信号
    all_signals = []
    
    for indicator in ['macd', 'kdj', 'rsi']:
        try:
            factor = create_divergence_factor(indicator, scenario="development")
            df_result = factor.calculate(df.copy())
            
            # 收集买入信号
            buy_signals = df_result[df_result[f'{indicator}_upup_low'] == True]
            for _, signal in buy_signals.iterrows():
                all_signals.append({
                    'date': signal['trade_time'],
                    'price': signal['close'],
                    'indicator': indicator.upper(),
                    'type': '买入',
                    'signal': 'buy'
                })
            
            # 收集卖出信号
            sell_signals = df_result[df_result[f'{indicator}_downdown_high'] == True]
            for _, signal in sell_signals.iterrows():
                all_signals.append({
                    'date': signal['trade_time'],
                    'price': signal['close'],
                    'indicator': indicator.upper(),
                    'type': '卖出',
                    'signal': 'sell'
                })
                
        except Exception as e:
            print(f"❌ {indicator}信号收集失败: {e}")
    
    # 按时间排序
    all_signals.sort(key=lambda x: x['date'])
    
    print(f"\n📅 最近的背离信号 (共{len(all_signals)}个):")
    for signal in all_signals[-10:]:  # 显示最近10个信号
        date_str = signal['date'].strftime('%Y-%m-%d')
        print(f"  {date_str}: {signal['indicator']} {signal['type']} @ {signal['price']:.2f}")

def main():
    """主函数"""
    print("🚀 背离因子综合测试")
    print("=" * 60)
    
    try:
        # 1. 测试各个背离因子
        test_individual_factors()
        
        # 2. 测试工厂函数
        test_factory_functions()
        
        # 3. 测试连续数值模式
        test_continuous_mode()
        
        # 4. 分析信号时机
        analyze_signal_timing()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        
        print("\n💡 使用建议:")
        print("1. KDJ适合短期交易，对价格变化敏感")
        print("2. RSI适合中期分析，超买超卖信号明确")
        print("3. MACD适合趋势分析，背离信号较为可靠")
        print("4. 建议结合多个指标确认信号")
        print("5. 在不同市场环境下选择合适的指标")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
