#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
盘中选股监控进程使用示例

这个示例展示了如何独立运行盘中选股监控进程
通常情况下，该进程会通过main.py自动调度启动
"""

import sys
import time
import signal
from datetime import datetime

# 导入监控进程
from processes.intraday_stock_monitor import IntradayStockMonitor, create_intraday_signals_table

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n🛑 接收到信号 {signum}，正在停止监控进程...")
    global monitor
    if 'monitor' in globals() and monitor:
        monitor.stop()
    sys.exit(0)

def main():
    """主函数"""
    print("🚀 盘中选股监控进程示例")
    print("=" * 50)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 1. 创建数据库表
        print("📊 创建数据库表...")
        if not create_intraday_signals_table():
            print("❌ 数据库表创建失败")
            return 1
        
        # 2. 创建监控实例
        print("🔧 创建监控实例...")
        global monitor
        monitor = IntradayStockMonitor()
        
        # 3. 显示配置信息
        print("⚙️ 配置信息:")
        print(f"  线程数量: {monitor.thread_count}")
        print(f"  监控间隔: {monitor.monitoring_interval}秒")
        print(f"  价格阈值: {monitor.price_threshold*100}%")
        
        # 4. 获取股票列表
        stocks = monitor._get_active_stocks()
        print(f"📈 监控股票: {len(stocks)} 只")
        
        if not stocks:
            print("❌ 没有活跃股票，请检查stock_primary_signals表")
            return 1
        
        # 5. 显示监控的股票
        print("📋 监控股票列表:")
        for i, stock in enumerate(stocks[:10]):  # 只显示前10只
            print(f"  {i+1:2d}. {stock['stock_code']} - {stock['stock_name']}")
        
        if len(stocks) > 10:
            print(f"  ... 还有 {len(stocks) - 10} 只股票")
        
        # 6. 启动监控
        print(f"\n🎯 开始监控 ({datetime.now().strftime('%H:%M:%S')})")
        print("💡 提示: 按 Ctrl+C 停止监控")
        print("-" * 50)
        
        # 启动监控进程
        monitor.start()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断，正在停止...")
        if 'monitor' in locals() and monitor:
            monitor.stop()
    except Exception as e:
        print(f"❌ 运行异常: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("👋 监控进程已退出")
    return 0

if __name__ == "__main__":
    sys.exit(main())
