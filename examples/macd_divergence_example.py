#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MACD背离因子使用示例

演示MACD背离因子的基本使用方法和不同场景的应用。

作者: QuantFM Team
创建时间: 2025-08-23
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from factors import MACDDivergenceFactor, MACDDivergenceConfig

def generate_sample_data(length=500):
    """生成示例数据"""
    print("📈 生成示例股票数据...")

    np.random.seed(42)
    dates = pd.date_range(start='2023-01-01', periods=length, freq='D')

    # 生成带趋势的价格数据
    price = 100
    prices = []

    for i in range(length):
        # 添加趋势和随机波动
        trend = 0.001 * np.sin(i * 0.02)  # 长期趋势
        noise = np.random.normal(0, 0.02)  # 随机噪音
        price_change = trend + noise

        price *= (1 + price_change)
        prices.append(price)

    # 创建OHLCV数据
    df = pd.DataFrame({
        'trade_time': dates,
        'close': prices
    })

    # 生成开高低价
    df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0])
    df['high'] = df[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.01, len(df)))
    df['low'] = df[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.01, len(df)))
    df['volume'] = np.random.randint(1000000, 5000000, len(df))

    print(f"✅ 生成{len(df)}条数据")
    return df

def test_real_data():
    """测试真实数据（可选）"""
    try:
        from data.db_manager import get_db_manager

        # 获取数据库连接
        db_manager = get_db_manager()

        # 获取测试股票的历史数据
        test_stock = '600906'
        end_date = datetime.now()
        start_date = end_date - timedelta(days=4000)

        sql = """
        SELECT trade_time, open, high, low, close, volume
        FROM stock_kline_day
        WHERE stock_code = %s
        ORDER BY trade_time ASC
        """

        print(f"📊 尝试获取{test_stock}的历史数据...")
        results = db_manager.fetch_all(sql, (test_stock, start_date, end_date))

        if results:
            df = pd.DataFrame(results)
            print(f"✅ 获取到{len(df)}条真实数据")
            return df
        else:
            print("⚠️ 未找到真实数据")
            return None

    except Exception as e:
        print(f"⚠️ 真实数据获取失败: {e}")
        return None

def analyze_results(df_with_factors, factor):
    """分析结果"""
    factor_names = factor.get_factor_names()
    print(f"📊 生成因子数量: {len(factor_names)}")

    # 统计非零信号
    if factor.config.output_mode == "boolean":
        signals = df_with_factors[['macd_upup_low', 'macd_downdown_high']].sum().sum()
        print(f"📈 背离信号数量: {signals}")
    else:
        non_zero = (df_with_factors['macd_divergence_composite'] != 0).sum()
        print(f"📈 非零信号数量: {non_zero}")


def main():
    """主函数"""
    print("🚀 MACD背离因子使用示例")
    print("=" * 60)

    try:
        # 1. 尝试使用真实数据
        df = test_real_data()

        if df is None:
            # 2. 使用模拟数据
            print("📈 使用模拟数据进行测试...")
            df = generate_sample_data(300)

        # 3. 测试不同场景
        scenarios = ['development', 'production', 'qlib_training']

        for scenario in scenarios:
            print(f"\n🎭 测试场景: {scenario}")

            # 创建因子实例
            factor = MACDDivergenceFactor(scenario=scenario)

            # 计算因子
            df_with_factors = factor.calculate(df)

            # 分析结果
            analyze_results(df_with_factors, factor)

        print("\n🎉 示例运行完成！")
        print("\n💡 更多示例请查看文档: docs/macd_divergence_factor.md")

    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()