#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MACD背离因子实用示例

展示如何在实际交易中使用MACD背离信号
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from factors import MACDDivergenceFactor
from data.db_manager import get_db_manager

def analyze_stock_divergence(stock_code='600906', days=300):
    """分析股票的MACD背离信号"""
    print(f"🔍 分析股票 {stock_code} 的MACD背离信号")
    print("=" * 60)
    
    # 1. 获取数据
    db_manager = get_db_manager()
    sql = '''
    SELECT trade_time, open, high, low, close, volume
    FROM stock_kline_day
    WHERE stock_code = %s
    ORDER BY trade_time DESC
    LIMIT %s
    '''
    
    results = db_manager.fetch_all(sql, (stock_code, days))
    df = pd.DataFrame(results)
    df = df.sort_values('trade_time').reset_index(drop=True)
    
    print(f"📊 获取到 {len(df)} 条数据")
    print(f"时间范围: {df['trade_time'].min().strftime('%Y-%m-%d')} 到 {df['trade_time'].max().strftime('%Y-%m-%d')}")
    print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    # 2. 计算MACD背离因子
    factor = MACDDivergenceFactor(scenario="development")  # 使用布尔信号
    df_result = factor.calculate(df)
    
    # 3. 分析背离信号
    print(f"\n📈 背离信号统计:")
    
    # 底背离（买入信号）
    buy_signals = df_result[df_result['macd_upup_low'] == True]
    print(f"🟢 底背离信号: {len(buy_signals)}个")
    
    # 顶背离（卖出信号）  
    sell_signals = df_result[df_result['macd_downdown_high'] == True]
    print(f"🔴 顶背离信号: {len(sell_signals)}个")
    
    # 趋势延续信号
    uptrend_signals = df_result[df_result['macd_upup_high'] == True]
    downtrend_signals = df_result[df_result['macd_downdown_low'] == True]
    print(f"📈 上涨趋势延续: {len(uptrend_signals)}个")
    print(f"📉 下跌趋势延续: {len(downtrend_signals)}个")
    
    # 4. 显示最近的信号
    print(f"\n🎯 最近的背离信号:")
    
    if len(buy_signals) > 0:
        recent_buy = buy_signals.tail(3)
        print(f"🟢 最近的底背离信号:")
        for _, signal in recent_buy.iterrows():
            date = signal['trade_time'].strftime('%Y-%m-%d')
            price = signal['close']
            print(f"  {date}: 价格 {price:.2f} - 买入信号")
    
    if len(sell_signals) > 0:
        recent_sell = sell_signals.tail(3)
        print(f"🔴 最近的顶背离信号:")
        for _, signal in recent_sell.iterrows():
            date = signal['trade_time'].strftime('%Y-%m-%d')
            price = signal['close']
            print(f"  {date}: 价格 {price:.2f} - 卖出信号")
    
    # 5. 简单的回测分析
    print(f"\n📊 简单回测分析:")
    
    if len(buy_signals) > 0:
        # 计算买入信号后的收益
        buy_returns = []
        for _, signal in buy_signals.iterrows():
            signal_idx = signal.name
            if signal_idx < len(df_result) - 10:  # 确保有足够的后续数据
                entry_price = signal['close']
                # 看10天后的价格
                future_price = df_result.iloc[signal_idx + 10]['close']
                return_pct = (future_price - entry_price) / entry_price * 100
                buy_returns.append(return_pct)
        
        if buy_returns:
            avg_return = np.mean(buy_returns)
            win_rate = sum(1 for r in buy_returns if r > 0) / len(buy_returns) * 100
            print(f"🟢 底背离信号回测 (10天后):")
            print(f"  平均收益: {avg_return:.2f}%")
            print(f"  胜率: {win_rate:.1f}%")
    
    if len(sell_signals) > 0:
        # 计算卖出信号后的收益（做空）
        sell_returns = []
        for _, signal in sell_signals.iterrows():
            signal_idx = signal.name
            if signal_idx < len(df_result) - 10:
                entry_price = signal['close']
                future_price = df_result.iloc[signal_idx + 10]['close']
                return_pct = (entry_price - future_price) / entry_price * 100  # 做空收益
                sell_returns.append(return_pct)
        
        if sell_returns:
            avg_return = np.mean(sell_returns)
            win_rate = sum(1 for r in sell_returns if r > 0) / len(sell_returns) * 100
            print(f"🔴 顶背离信号回测 (10天后做空):")
            print(f"  平均收益: {avg_return:.2f}%")
            print(f"  胜率: {win_rate:.1f}%")
    
    # 6. 当前状态分析
    print(f"\n🔮 当前状态分析:")
    latest_data = df_result.tail(1).iloc[0]
    latest_date = latest_data['trade_time'].strftime('%Y-%m-%d')
    latest_price = latest_data['close']
    
    print(f"最新日期: {latest_date}")
    print(f"最新价格: {latest_price:.2f}")
    
    # 检查最近是否有信号
    recent_signals = df_result.tail(5)
    has_recent_signal = False
    
    for _, row in recent_signals.iterrows():
        date = row['trade_time'].strftime('%Y-%m-%d')
        if row['macd_upup_low']:
            print(f"🟢 {date}: 底背离信号 - 考虑买入")
            has_recent_signal = True
        elif row['macd_downdown_high']:
            print(f"🔴 {date}: 顶背离信号 - 考虑卖出")
            has_recent_signal = True
    
    if not has_recent_signal:
        print("📊 最近5天无背离信号，继续观察")
    
    return df_result

def compare_multiple_stocks():
    """比较多只股票的背离信号"""
    print(f"\n🔄 多股票背离信号对比")
    print("=" * 60)
    
    stocks = ['000001', '000002', '600000', '600036', '600519']
    
    for stock in stocks:
        try:
            print(f"\n📊 {stock}:")
            df_result = analyze_stock_divergence(stock, days=100)
            
            # 统计信号
            buy_count = df_result['macd_upup_low'].sum()
            sell_count = df_result['macd_downdown_high'].sum()
            
            print(f"  底背离: {buy_count}个, 顶背离: {sell_count}个")
            
        except Exception as e:
            print(f"  ❌ 分析失败: {e}")

def main():
    """主函数"""
    print("🚀 MACD背离因子实用分析")
    print("=" * 60)
    
    try:
        # 1. 分析单只股票
        df_result = analyze_stock_divergence('300067', days=300)
        
        # 2. 多股票对比（可选）
        # compare_multiple_stocks()
        
        print(f"\n💡 使用建议:")
        print("1. 底背离信号通常是买入机会")
        print("2. 顶背离信号通常是卖出机会")
        print("3. 背离信号需要结合其他指标确认")
        print("4. 建议在趋势明确时使用背离信号")
        print("5. 设置止损止盈，控制风险")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
