#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
因子基类

定义统一的因子接口和数据结构
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, Any, Optional, List
import pandas as pd
from datetime import datetime

from utils.logger import get_logger


@dataclass
class FactorResult:
    """因子计算结果"""
    factor_name: str                    # 因子名称
    factor_value: float                 # 因子值
    factor_type: str                    # 因子类型 (technical/fundamental/sentiment)
    confidence: float = 0.0             # 置信度 (0-1)
    signal_strength: str = "NONE"       # 信号强度 (STRONG/MEDIUM/WEAK/NONE)
    metadata: Dict[str, Any] = None     # 元数据
    calculation_time: datetime = None   # 计算时间
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.calculation_time is None:
            self.calculation_time = datetime.now()


class BaseFactor(ABC):
    """因子基类"""
    
    def __init__(self, factor_name: str, factor_type: str = "technical"):
        self.factor_name = factor_name
        self.factor_type = factor_type
        self.logger = get_logger(f'Factor_{factor_name}')
        
        # 因子参数（子类可以重写）
        self.min_data_length = 50
        self.required_columns = ['close']
        
        self.logger.debug(f"因子 {factor_name} 初始化完成")
    
    @abstractmethod
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """
        计算因子值
        
        Args:
            data: 包含OHLCV等数据的DataFrame
            **kwargs: 其他参数
            
        Returns:
            FactorResult对象或None
        """
        pass
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证输入数据"""
        try:
            if data is None or len(data) < self.min_data_length:
                self.logger.debug(f"数据长度不足: {len(data) if data is not None else 0} < {self.min_data_length}")
                return False
            
            # 检查必需列
            missing_columns = [col for col in self.required_columns if col not in data.columns]
            if missing_columns:
                self.logger.error(f"缺少必需列: {missing_columns}")
                return False
            
            # 检查数据有效性
            for col in self.required_columns:
                if data[col].isna().all():
                    self.logger.error(f"列 {col} 全部为空值")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            return False
    
    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """预处理数据（子类可以重写）"""
        try:
            # 基础预处理
            df = data.copy()
            
            # 确保数据类型正确
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 处理时间列
            if 'trade_time' in df.columns:
                df['trade_time'] = pd.to_datetime(df['trade_time'])
            
            # 排序
            if 'trade_time' in df.columns:
                df = df.sort_values('trade_time').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"数据预处理失败: {e}")
            return data
    
    def calculate_with_validation(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """带验证的因子计算"""
        try:
            # 数据验证
            if not self.validate_data(data):
                return None
            
            # 数据预处理
            processed_data = self.preprocess_data(data)
            
            # 计算因子
            result = self.calculate(processed_data, **kwargs)
            
            if result:
                self.logger.debug(f"因子 {self.factor_name} 计算成功: {result.factor_value}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"因子 {self.factor_name} 计算失败: {e}")
            return None
    
    def get_factor_description(self) -> str:
        """获取因子描述（子类可以重写）"""
        return f"因子名称: {self.factor_name}\n因子类型: {self.factor_type}"


class CompositeFactor(BaseFactor):
    """复合因子基类"""
    
    def __init__(self, factor_name: str, sub_factors: List[BaseFactor], 
                 weights: Optional[List[float]] = None):
        super().__init__(factor_name, "composite")
        self.sub_factors = sub_factors
        self.weights = weights or [1.0] * len(sub_factors)
        
        if len(self.weights) != len(self.sub_factors):
            raise ValueError("权重数量必须与子因子数量相等")
        
        # 归一化权重
        total_weight = sum(self.weights)
        self.weights = [w / total_weight for w in self.weights]
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算复合因子"""
        try:
            sub_results = []
            
            # 计算所有子因子
            for factor in self.sub_factors:
                result = factor.calculate_with_validation(data, **kwargs)
                if result is None:
                    self.logger.warning(f"子因子 {factor.factor_name} 计算失败")
                    return None
                sub_results.append(result)
            
            # 加权平均
            weighted_value = sum(
                result.factor_value * weight 
                for result, weight in zip(sub_results, self.weights)
            )
            
            # 计算平均置信度
            avg_confidence = sum(result.confidence for result in sub_results) / len(sub_results)
            
            # 确定信号强度
            if weighted_value > 0.7:
                signal_strength = "STRONG"
            elif weighted_value > 0.5:
                signal_strength = "MEDIUM"
            elif weighted_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"
            
            # 收集元数据
            metadata = {
                'sub_factors': [
                    {
                        'name': result.factor_name,
                        'value': result.factor_value,
                        'weight': weight
                    }
                    for result, weight in zip(sub_results, self.weights)
                ],
                'calculation_method': 'weighted_average'
            }
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=weighted_value,
                factor_type=self.factor_type,
                confidence=avg_confidence,
                signal_strength=signal_strength,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"复合因子 {self.factor_name} 计算失败: {e}")
            return None


class FactorManager:
    """因子管理器"""
    
    def __init__(self):
        self.logger = get_logger('FactorManager')
        self.factors: Dict[str, BaseFactor] = {}
    
    def register_factor(self, factor: BaseFactor):
        """注册因子"""
        self.factors[factor.factor_name] = factor
        self.logger.info(f"注册因子: {factor.factor_name}")
    
    def unregister_factor(self, factor_name: str):
        """注销因子"""
        if factor_name in self.factors:
            del self.factors[factor_name]
            self.logger.info(f"注销因子: {factor_name}")
    
    def calculate_factor(self, factor_name: str, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算指定因子"""
        if factor_name not in self.factors:
            self.logger.error(f"因子 {factor_name} 未注册")
            return None
        
        return self.factors[factor_name].calculate_with_validation(data, **kwargs)
    
    def calculate_all_factors(self, data: pd.DataFrame, **kwargs) -> Dict[str, FactorResult]:
        """计算所有注册的因子"""
        results = {}
        
        for factor_name, factor in self.factors.items():
            try:
                result = factor.calculate_with_validation(data, **kwargs)
                if result:
                    results[factor_name] = result
            except Exception as e:
                self.logger.error(f"计算因子 {factor_name} 失败: {e}")
        
        return results
    
    def get_factor_list(self) -> List[str]:
        """获取已注册的因子列表"""
        return list(self.factors.keys())
    
    def get_factor_info(self, factor_name: str) -> Optional[str]:
        """获取因子信息"""
        if factor_name in self.factors:
            return self.factors[factor_name].get_factor_description()
        return None
