#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RSI背离因子

基于RSI（相对强弱指数）与价格的背离分析，识别潜在的趋势反转信号。

RSI指标说明：
- RSI通过比较上涨日和下跌日的幅度来评估买卖双方的相对强弱
- 取值范围：0-100
- 超买区域：RSI > 70
- 超卖区域：RSI < 30

背离检测逻辑：
1. RSI顶背离：价格创新高，但RSI指标未创新高（看跌信号）
2. RSI底背离：价格创新低，但RSI指标未创新低（看涨信号）

作者: QuantFM Team
创建时间: 2025-08-23
"""

import pandas as pd
import numpy as np
import talib
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from .base_divergence import BaseDivergenceFactor, DivergenceConfig

@dataclass
class RSIDivergenceConfig(DivergenceConfig):
    """RSI背离因子配置类"""
    # RSI参数
    rsi_period: int = 14       # RSI周期
    
    # 背离检测参数
    rsi_threshold: float = 2.0 # RSI变化阈值
    overbought_level: float = 70.0   # 超买线
    oversold_level: float = 30.0     # 超卖线
    
    def get_param_space(self) -> Dict[str, Dict]:
        """获取参数空间（用于优化）"""
        base_space = {
            'rsi_period': {'type': 'int', 'range': [6, 21], 'default': self.rsi_period},
            'window': {'type': 'int', 'range': [3, 10], 'default': self.window},
            'min_bars_between': {'type': 'int', 'range': [5, 20], 'default': self.min_bars_between},
            'min_price_change': {'type': 'float', 'range': [0.01, 0.05], 'default': self.min_price_change},
            'rsi_threshold': {'type': 'float', 'range': [1.0, 5.0], 'default': self.rsi_threshold},
            'overbought_level': {'type': 'float', 'range': [65.0, 80.0], 'default': self.overbought_level},
            'oversold_level': {'type': 'float', 'range': [20.0, 35.0], 'default': self.oversold_level}
        }
        return base_space

class RSIDivergenceFactor(BaseDivergenceFactor):
    """
    RSI背离因子
    
    基于RSI相对强弱指数的背离分析
    """
    
    def __init__(self, config: RSIDivergenceConfig = None, scenario: str = "production", **kwargs):
        """
        初始化RSI背离因子

        Args:
            config: 因子配置对象
            scenario: 使用场景 ("development", "production", "qlib_training")
            **kwargs: 直接参数覆盖
        """
        # 配置初始化
        if config is None:
            base_config = DivergenceConfig.from_config_file(scenario=scenario, indicator_name="rsi")
            # 转换为RSI配置，保留所有基础配置
            config_dict = base_config.__dict__.copy()
            # 添加RSI特有的默认参数
            rsi_defaults = {
                'rsi_period': 14,
                'rsi_threshold': 2.0,
                'overbought_level': 70.0,
                'oversold_level': 30.0
            }
            config_dict.update(rsi_defaults)
            config = RSIDivergenceConfig(**config_dict)

        # 参数覆盖
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)

        super().__init__("RSI_Divergence", "rsi", config, scenario, **kwargs)

        # 因子缓存
        self._factor_cache = {} if self.config.enable_cache else None

        # 根据配置决定计算方法
        if self.config.output_mode == "boolean":
            self._calculate_method = self._calculate_boolean_factors
        else:
            self._calculate_method = self._calculate_continuous_factors

    def calculate(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算RSI背离因子

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            添加了背离因子的DataFrame
        """
        try:
            # 检查输入数据
            if df is None:
                self.logger.error("输入数据为None")
                return pd.DataFrame()

            if len(df) < 50:  # 最少需要50条数据
                self.logger.warning(f"数据长度不足")
                return self._add_empty_factors(df)

            df_result = df.copy()

            # 1. 计算RSI指标
            df_result = self._calculate_indicator(df_result)

            # 2. 根据配置选择计算方法
            df_result = self._calculate_method(df_result)

            # 3. 后处理
            if self.config.normalize_output and self.config.output_mode == "continuous":
                df_result = self._normalize_factors(df_result)

            return df_result

        except Exception as e:
            self.logger.error(f"计算RSI背离因子失败: {e}")
            return self._add_empty_factors(df)

    def _calculate_boolean_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算布尔信号因子"""
        # 初始化因子列
        df['rsi_upup_high'] = False      # 价格新高，RSI新高
        df['rsi_upup_low'] = False       # 价格新低，RSI新高（底背离）
        df['rsi_downdown_high'] = False  # 价格新高，RSI新低（顶背离）
        df['rsi_downdown_low'] = False   # 价格新低，RSI新低

        # 识别局部极值点
        price_highs, price_lows = self._find_local_extremes(df['close'])
        rsi_highs, rsi_lows = self._find_local_extremes(df[self._get_indicator_column()])

        # 检测背离
        self._detect_boolean_divergences(df, price_highs, price_lows, rsi_highs, rsi_lows, self._get_indicator_column())

        return df

    def _calculate_continuous_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算连续数值因子"""
        # 初始化因子列
        df['rsi_bullish_divergence'] = 0.0    # 看涨背离强度
        df['rsi_bearish_divergence'] = 0.0    # 看跌背离强度
        df['rsi_trend_strength'] = 0.0        # 趋势强度
        df['rsi_momentum_score'] = 0.0        # 动量评分

        # 计算背离强度
        self._calculate_divergence_strength(df)

        # 特征工程
        if self.config.feature_engineering:
            df = self._feature_engineering(df)

        # 平滑和衰减
        if self.config.smoothing_window > 1 or self.config.strength_decay < 1.0:
            df = self._apply_smoothing_and_decay(df)

        return df
    
    def _calculate_indicator(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算RSI指标"""
        try:
            # 计算RSI指标
            rsi = talib.RSI(df['close'].values, timeperiod=self.config.rsi_period)
            df['rsi'] = rsi
            
            # 计算RSI的移动平均（平滑RSI）
            df['rsi_ma'] = df['rsi'].rolling(3).mean()
            
            return df
            
        except Exception as e:
            self.logger.error(f"RSI指标计算失败: {e}")
            # 添加空列避免后续错误
            df['rsi'] = np.nan
            df['rsi_ma'] = np.nan
            return df
    
    def _get_indicator_column(self) -> str:
        """获取用于背离检测的指标列名"""
        return 'rsi'
    
    def _calculate_divergence_strength(self, df: pd.DataFrame):
        """计算RSI背离强度（向量化实现）"""
        # 向量化计算价格和RSI的变化率
        price_change = df['close'].pct_change().rolling(5).sum()
        rsi_change = df['rsi'].diff().rolling(5).sum()

        # RSI特有的背离强度计算（向量化）
        # 考虑RSI的超买超卖区域
        rsi_overbought = df['rsi'] > self.config.overbought_level
        rsi_oversold = df['rsi'] < self.config.oversold_level

        # 向量化的背离强度计算
        # 看涨背离：价格下跌但RSI上涨，特别是在超卖区域
        bullish_divergence = np.where(
            (price_change < 0) & (rsi_change > 0),
            abs(price_change) * abs(rsi_change) * (1 + rsi_oversold.astype(float) * 3),
            0
        )

        # 看跌背离：价格上涨但RSI下跌，特别是在超买区域
        bearish_divergence = np.where(
            (price_change > 0) & (rsi_change < 0),
            abs(price_change) * abs(rsi_change) * (1 + rsi_overbought.astype(float) * 3),
            0
        )

        df['rsi_bullish_divergence'] = bullish_divergence
        df['rsi_bearish_divergence'] = bearish_divergence

        # 趋势强度：基于RSI与中线50的关系（向量化）
        rsi_trend = (df['rsi'] - 50) / 50  # 标准化到[-1, 1]
        price_trend = df['close'].pct_change().rolling(10).sum()
        df['rsi_trend_strength'] = rsi_trend * np.sign(price_trend)
        df['rsi_trend_strength'] = df['rsi_trend_strength'].fillna(0)

        # 动量评分：RSI的变化率（向量化）
        df['rsi_momentum_score'] = df['rsi'].pct_change().rolling(3).mean().fillna(0)
    
    def _detect_boolean_divergences(self, df: pd.DataFrame, price_highs: List[int],
                                  price_lows: List[int], indicator_highs: List[int],
                                  indicator_lows: List[int], indicator_col: str):
        """检测RSI布尔背离信号（向量化优化）"""
        # 向量化的背离检测
        # 预先计算所有需要的数据
        prices = df['close'].values
        rsi_values = df[indicator_col].values

        # RSI顶背离检测（向量化）
        for i in range(len(price_highs) - 1):
            idx1, idx2 = price_highs[i], price_highs[i+1]

            if idx2 - idx1 < self.config.min_bars_between:
                continue

            price1, price2 = prices[idx1], prices[idx2]
            price_change = (price2 - price1) / price1

            if abs(price_change) >= self.config.min_price_change and price2 > price1:
                rsi1, rsi2 = rsi_values[idx1], rsi_values[idx2]
                rsi_change = abs(rsi2 - rsi1)

                if rsi_change >= self.config.rsi_threshold:
                    if rsi2 < rsi1:  # RSI创新低 - 顶背离
                        df.iloc[idx2, df.columns.get_loc('rsi_downdown_high')] = True
                    elif rsi2 > rsi1:  # RSI也创新高 - 趋势延续
                        df.iloc[idx2, df.columns.get_loc('rsi_upup_high')] = True

        # RSI底背离检测（向量化）
        for i in range(len(price_lows) - 1):
            idx1, idx2 = price_lows[i], price_lows[i+1]

            if idx2 - idx1 < self.config.min_bars_between:
                continue

            price1, price2 = prices[idx1], prices[idx2]
            price_change = (price2 - price1) / price1

            if abs(price_change) >= self.config.min_price_change and price2 < price1:
                rsi1, rsi2 = rsi_values[idx1], rsi_values[idx2]
                rsi_change = abs(rsi2 - rsi1)

                if rsi_change >= self.config.rsi_threshold:
                    if rsi2 > rsi1:  # RSI创新高 - 底背离
                        df.iloc[idx2, df.columns.get_loc('rsi_upup_low')] = True
                    elif rsi2 < rsi1:  # RSI也创新低 - 趋势延续
                        df.iloc[idx2, df.columns.get_loc('rsi_downdown_low')] = True

    def _feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """特征工程"""
        # 组合因子
        df['rsi_divergence_composite'] = (
            df['rsi_bullish_divergence'] - df['rsi_bearish_divergence']
        )

        # 信号确认度
        df['rsi_signal_confidence'] = (
            abs(df['rsi_bullish_divergence']) + abs(df['rsi_bearish_divergence'])
        ) * abs(df['rsi_trend_strength'])

        # 相对强度
        df['rsi_relative_strength'] = df['rsi_divergence_composite'].rolling(20).rank(pct=True)

        return df

    def _apply_smoothing_and_decay(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用平滑和衰减"""
        factor_cols = [col for col in df.columns if col.startswith('rsi_')]

        for col in factor_cols:
            if self.config.smoothing_window > 1:
                df[col] = df[col].rolling(self.config.smoothing_window, center=True).mean().fillna(df[col])

            if self.config.strength_decay < 1.0:
                df[col] = df[col] * (self.config.strength_decay ** np.arange(len(df)))

        return df

    def _normalize_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化因子输出"""
        factor_cols = [col for col in df.columns if col.startswith('rsi_')]

        for col in factor_cols:
            mean_val = df[col].rolling(252, min_periods=20).mean()
            std_val = df[col].rolling(252, min_periods=20).std()

            df[col + '_normalized'] = (df[col] - mean_val) / (std_val + 1e-8)
            df[col + '_normalized'] = df[col + '_normalized'].fillna(0)

        return df

    def _add_empty_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加空的因子列"""
        if df is None:
            return pd.DataFrame()

        df_result = df.copy()

        if self.config.output_mode == "boolean":
            df_result['rsi_upup_high'] = False
            df_result['rsi_upup_low'] = False
            df_result['rsi_downdown_high'] = False
            df_result['rsi_downdown_low'] = False
        else:
            factor_names = [
                'rsi_bullish_divergence', 'rsi_bearish_divergence',
                'rsi_trend_strength', 'rsi_momentum_score'
            ]

            if self.config.feature_engineering:
                factor_names.extend(['rsi_divergence_composite', 'rsi_signal_confidence', 'rsi_relative_strength'])

            for name in factor_names:
                df_result[name] = 0.0
                if self.config.normalize_output:
                    df_result[name + '_normalized'] = 0.0

        return df_result

    def get_factor_names(self) -> List[str]:
        """获取因子名称列表"""
        if self.config.output_mode == "boolean":
            return ['rsi_upup_high', 'rsi_upup_low', 'rsi_downdown_high', 'rsi_downdown_low']
        else:
            base_names = ['rsi_bullish_divergence', 'rsi_bearish_divergence',
                         'rsi_trend_strength', 'rsi_momentum_score']

            if self.config.feature_engineering:
                base_names.extend(['rsi_divergence_composite', 'rsi_signal_confidence', 'rsi_relative_strength'])

            if self.config.normalize_output:
                normalized_names = [name + '_normalized' for name in base_names]
                return base_names + normalized_names

            return base_names
    
    @classmethod
    def create_from_params(cls, params: Dict[str, Any], scenario: str = "production") -> 'RSIDivergenceFactor':
        """从参数字典创建因子实例（用于参数优化）"""
        base_config = DivergenceConfig.from_config_file(scenario=scenario, indicator_name="rsi_divergence")
        config_dict = base_config.__dict__.copy()
        config = RSIDivergenceConfig(**config_dict)
        
        # 更新参数
        for key, value in params.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        return cls(config)
    
    @classmethod
    def get_param_space(cls) -> Dict[str, Dict]:
        """获取参数空间定义（用于优化）"""
        config = RSIDivergenceConfig()
        return config.get_param_space()
    
    def get_signal_interpretation(self) -> Dict[str, str]:
        """获取信号解释"""
        if self.config.output_mode == "boolean":
            return {
                'rsi_upup_high': '价格新高且RSI新高 - 强势上涨趋势延续',
                'rsi_upup_low': 'RSI底背离 - 价格新低但RSI新高，看涨信号',
                'rsi_downdown_high': 'RSI顶背离 - 价格新高但RSI新低，看跌信号',
                'rsi_downdown_low': '价格新低且RSI新低 - 弱势下跌趋势延续'
            }
        else:
            return {
                'rsi_bullish_divergence': 'RSI看涨背离强度 - 数值越大看涨信号越强',
                'rsi_bearish_divergence': 'RSI看跌背离强度 - 数值越大看跌信号越强',
                'rsi_trend_strength': 'RSI趋势强度 - 基于RSI与中线50的关系',
                'rsi_momentum_score': 'RSI动量评分 - RSI变化率',
                'rsi_divergence_composite': 'RSI组合背离信号 - 综合看涨看跌背离',
                'rsi_signal_confidence': 'RSI信号确认度 - 背离信号的可信度',
                'rsi_relative_strength': 'RSI相对强度 - 在历史数据中的相对位置'
            }
    
    def get_rsi_status(self, df: pd.DataFrame) -> Dict[str, Any]:
        """获取RSI当前状态"""
        if len(df) == 0:
            return {}
        
        latest = df.iloc[-1]
        
        # RSI数值状态
        rsi_value = latest.get('rsi', np.nan)
        
        # 超买超卖状态
        overbought = rsi_value > self.config.overbought_level
        oversold = rsi_value < self.config.oversold_level
        neutral = self.config.oversold_level <= rsi_value <= self.config.overbought_level
        
        # RSI趋势
        if len(df) >= 5:
            rsi_trend = df['rsi'].tail(5).diff().mean()
            trend_direction = 'up' if rsi_trend > 0 else 'down' if rsi_trend < 0 else 'sideways'
        else:
            rsi_trend = 0
            trend_direction = 'unknown'
        
        # 背离风险评估
        risk_level = 'low'
        if overbought and trend_direction == 'down':
            risk_level = 'high'  # 超买区域RSI下降，顶背离风险
        elif oversold and trend_direction == 'up':
            risk_level = 'high'  # 超卖区域RSI上升，底背离风险
        elif overbought or oversold:
            risk_level = 'medium'
        
        return {
            'rsi_value': rsi_value,
            'overbought': overbought,
            'oversold': oversold,
            'neutral': neutral,
            'trend_direction': trend_direction,
            'rsi_trend': rsi_trend,
            'risk_level': risk_level,
            'signal_strength': abs(rsi_value - 50) / 50  # 信号强度（距离中线的程度）
        }
