#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MACD背离因子

基于MACD指标与价格的背离分析，识别潜在的趋势反转信号。
支持多种输出模式和使用场景，通过配置文件统一管理参数。

核心特性：
1. 配置文件驱动的参数管理
2. 支持布尔信号和连续数值两种输出模式
3. Qlib兼容性（可选）
4. 参数优化支持
5. 高性能向量化计算
6. 统一的背离因子架构

MACD指标说明：
- MACD线：快速EMA - 慢速EMA
- 信号线：MACD线的EMA
- 柱状图：MACD线 - 信号线

背离检测逻辑：
1. MACD顶背离：价格创新高，但MACD指标未创新高（看跌信号）
2. MACD底背离：价格创新低，但MACD指标未创新低（看涨信号）

作者: QuantFM Team
创建时间: 2025-08-23
"""

import pandas as pd
import numpy as np
import talib
import yaml
import os
from typing import Dict, List, Tuple, Optional, Union, Any
import logging
from dataclasses import dataclass
from .base_divergence import BaseDivergenceFactor, DivergenceConfig

@dataclass
class MACDDivergenceConfig(DivergenceConfig):
    """MACD背离因子配置类"""
    # MACD参数
    fastperiod: int = 12       # 快速EMA周期
    slowperiod: int = 26       # 慢速EMA周期
    signalperiod: int = 9      # 信号线周期

    # 背离检测参数
    use_macd_line: bool = True # 是否使用MACD线进行背离检测（否则使用信号线）
    macd_threshold: float = 0.001 # MACD变化阈值

    # 性能优化（继承自基类但可以覆盖）
    vectorized_compute: bool = True

    @classmethod
    def from_config_file(cls, config_path: str = None, scenario: str = "production") -> 'MACDDivergenceConfig':
        """从配置文件加载配置"""
        if config_path is None:
            # 默认配置文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(os.path.dirname(os.path.dirname(current_dir)),
                                     "config", "divergence_params.yaml")

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            # 获取MACD背离因子配置
            macd_config = config_data.get('macd_divergence', {})

            # 如果指定了场景，使用场景配置覆盖
            if scenario and scenario in config_data.get('scenarios', {}):
                scenario_config = config_data['scenarios'][scenario].get('macd_divergence', {})
                macd_config = cls._merge_configs(macd_config, scenario_config)

            # 展平嵌套配置
            flat_config = {}
            for section, params in macd_config.items():
                if section == 'param_space':
                    # 跳过参数空间定义，不作为实际参数
                    continue
                elif isinstance(params, dict):
                    flat_config.update(params)
                else:
                    flat_config[section] = params

            return cls(**flat_config)

        except Exception as e:
            logging.warning(f"配置文件加载失败，使用默认配置: {e}")
            return cls()

    @staticmethod
    def _merge_configs(base_config: dict, override_config: dict) -> dict:
        """合并配置"""
        merged = base_config.copy()
        for key, value in override_config.items():
            if isinstance(value, dict) and key in merged:
                merged[key].update(value)
            else:
                merged[key] = value
        return merged

    def get_param_space(self) -> Dict[str, Dict]:
        """获取参数空间（用于优化）"""
        return {
            'fastperiod': {'type': 'int', 'range': [8, 20], 'default': self.fastperiod},
            'slowperiod': {'type': 'int', 'range': [20, 35], 'default': self.slowperiod},
            'signalperiod': {'type': 'int', 'range': [5, 15], 'default': self.signalperiod},
            'window': {'type': 'int', 'range': [3, 10], 'default': self.window},
            'min_bars_between': {'type': 'int', 'range': [5, 20], 'default': self.min_bars_between},
            'min_price_change': {'type': 'float', 'range': [0.01, 0.05], 'default': self.min_price_change},
            'macd_threshold': {'type': 'float', 'range': [0.0001, 0.01], 'default': self.macd_threshold},
            'smoothing_window': {'type': 'int', 'range': [1, 5], 'default': self.smoothing_window},
            'strength_decay': {'type': 'float', 'range': [0.8, 0.99], 'default': self.strength_decay}
        }

class MACDDivergenceFactor(BaseDivergenceFactor):
    """
    MACD背离因子

    支持多种输出模式和使用场景的MACD背离因子实现。
    完全兼容原有功能，包括Qlib支持、参数优化等。
    """

    def __init__(self, config: MACDDivergenceConfig = None, scenario: str = "production", **kwargs):
        """
        初始化MACD背离因子

        Args:
            config: 因子配置对象
            scenario: 使用场景 ("development", "production", "qlib_training")
            **kwargs: 直接参数覆盖
        """
        # 配置初始化
        if config is None:
            config = MACDDivergenceConfig.from_config_file(scenario=scenario)

        # 参数覆盖
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)

        super().__init__("MACD_Divergence", "macd", config, scenario, **kwargs)

        # 因子缓存
        self._factor_cache = {} if self.config.enable_cache else None

        # 根据配置决定计算方法
        if self.config.output_mode == "boolean":
            self._calculate_method = self._calculate_boolean_factors
        else:
            self._calculate_method = self._calculate_continuous_factors

    def calculate(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算MACD背离因子

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            添加了背离因子的DataFrame
        """
        try:
            # 检查输入数据
            if df is None:
                self.logger.error("输入数据为None")
                return pd.DataFrame()

            if len(df) < self.config.slowperiod + self.config.signalperiod + self.config.window * 2:
                self.logger.warning(f"数据长度不足")
                return self._add_empty_factors(df)

            df_result = df.copy()

            # 1. 计算MACD指标
            df_result = self._calculate_indicator(df_result)

            # 2. 根据配置选择计算方法
            df_result = self._calculate_method(df_result)

            # 3. 后处理
            if self.config.normalize_output and self.config.output_mode == "continuous":
                df_result = self._normalize_factors(df_result)

            return df_result

        except Exception as e:
            self.logger.error(f"计算MACD背离因子失败: {e}")
            return self._add_empty_factors(df)

    def _calculate_boolean_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算布尔信号因子（策略信号模式）"""
        # 初始化因子列
        df['macd_upup_high'] = False      # 价格新高，MACD新高
        df['macd_upup_low'] = False       # 价格新低，MACD新高（底背离）
        df['macd_downdown_high'] = False  # 价格新高，MACD新低（顶背离）
        df['macd_downdown_low'] = False   # 价格新低，MACD新低

        # 识别局部极值点
        price_highs, price_lows = self._find_local_extremes(df['close'])
        macd_highs, macd_lows = self._find_local_extremes(df[self._get_indicator_column()])

        # 检测背离
        self._detect_boolean_divergences(df, price_highs, price_lows, macd_highs, macd_lows, self._get_indicator_column())

        return df

    def _calculate_continuous_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算连续数值因子（机器学习模式）"""
        # 初始化因子列
        df['macd_bullish_divergence'] = 0.0    # 看涨背离强度
        df['macd_bearish_divergence'] = 0.0    # 看跌背离强度
        df['macd_trend_strength'] = 0.0        # 趋势强度
        df['macd_momentum_score'] = 0.0        # 动量评分

        # 计算背离强度
        self._calculate_divergence_strength(df)

        # 特征工程
        if self.config.feature_engineering:
            df = self._feature_engineering(df)

        # 平滑和衰减
        if self.config.smoothing_window > 1 or self.config.strength_decay < 1.0:
            df = self._apply_smoothing_and_decay(df)

        return df

    def _feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """特征工程"""
        # 组合因子
        df['macd_divergence_composite'] = (
            df['macd_bullish_divergence'] - df['macd_bearish_divergence']
        )

        # 信号确认度
        df['macd_signal_confidence'] = (
            abs(df['macd_bullish_divergence']) + abs(df['macd_bearish_divergence'])
        ) * abs(df['macd_trend_strength'])

        # 相对强度
        df['macd_relative_strength'] = df['macd_divergence_composite'].rolling(20).rank(pct=True)

        return df

    def _apply_smoothing_and_decay(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用平滑和衰减"""
        factor_cols = [col for col in df.columns if col.startswith('macd_')]

        for col in factor_cols:
            if self.config.smoothing_window > 1:
                df[col] = df[col].rolling(self.config.smoothing_window, center=True).mean().fillna(df[col])

            if self.config.strength_decay < 1.0:
                df[col] = df[col] * (self.config.strength_decay ** np.arange(len(df)))

        return df

    def _normalize_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化因子输出"""
        factor_cols = [col for col in df.columns if col.startswith('macd_')]

        for col in factor_cols:
            mean_val = df[col].rolling(252, min_periods=20).mean()
            std_val = df[col].rolling(252, min_periods=20).std()

            df[col + '_normalized'] = (df[col] - mean_val) / (std_val + 1e-8)
            df[col + '_normalized'] = df[col + '_normalized'].fillna(0)

        return df

    def _add_empty_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加空的因子列"""
        if df is None:
            return pd.DataFrame()

        df_result = df.copy()

        if self.config.output_mode == "boolean":
            df_result['macd_upup_high'] = False
            df_result['macd_upup_low'] = False
            df_result['macd_downdown_high'] = False
            df_result['macd_downdown_low'] = False
        else:
            factor_names = [
                'macd_bullish_divergence', 'macd_bearish_divergence',
                'macd_trend_strength', 'macd_momentum_score'
            ]

            if self.config.feature_engineering:
                factor_names.extend(['macd_divergence_composite', 'macd_signal_confidence', 'macd_relative_strength'])

            for name in factor_names:
                df_result[name] = 0.0
                if self.config.normalize_output:
                    df_result[name + '_normalized'] = 0.0

        return df_result

    def get_factor_names(self) -> List[str]:
        """获取因子名称列表"""
        if self.config.output_mode == "boolean":
            return ['macd_upup_high', 'macd_upup_low', 'macd_downdown_high', 'macd_downdown_low']
        else:
            base_names = ['macd_bullish_divergence', 'macd_bearish_divergence',
                         'macd_trend_strength', 'macd_momentum_score']

            if self.config.feature_engineering:
                base_names.extend(['macd_divergence_composite', 'macd_signal_confidence', 'macd_relative_strength'])

            if self.config.normalize_output:
                normalized_names = [name + '_normalized' for name in base_names]
                return base_names + normalized_names

            return base_names
    
    def _calculate_indicator(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算MACD指标"""
        try:
            # 计算MACD指标
            macd, macdsignal, macdhist = talib.MACD(
                df['close'].values,
                fastperiod=self.config.fastperiod,
                slowperiod=self.config.slowperiod,
                signalperiod=self.config.signalperiod
            )
            
            df['macd'] = macd
            df['macdsignal'] = macdsignal
            df['macdhist'] = macdhist
            
            return df
            
        except Exception as e:
            self.logger.error(f"MACD指标计算失败: {e}")
            # 添加空列避免后续错误
            df['macd'] = np.nan
            df['macdsignal'] = np.nan
            df['macdhist'] = np.nan
            return df
    
    def _get_indicator_column(self) -> str:
        """获取用于背离检测的指标列名"""
        return 'macd' if self.config.use_macd_line else 'macdsignal'
    
    def _calculate_divergence_strength(self, df: pd.DataFrame):
        """计算MACD背离强度（向量化实现）"""
        indicator_col = self._get_indicator_column()
        
        # 向量化计算价格和MACD的变化率
        price_change = df['close'].pct_change().rolling(5).sum()
        macd_change = df[indicator_col].pct_change().rolling(5).sum()
        
        # MACD特有的背离强度计算
        # 考虑MACD的零轴位置
        macd_above_zero = df[indicator_col] > 0
        macd_below_zero = df[indicator_col] < 0
        
        # 向量化的背离强度计算
        # 看涨背离：价格下跌但MACD上涨，特别是在零轴下方
        bullish_divergence = np.where(
            (price_change < 0) & (macd_change > 0),
            abs(price_change - macd_change) * (1 + macd_below_zero.astype(float)),
            0
        )
        
        # 看跌背离：价格上涨但MACD下跌，特别是在零轴上方
        bearish_divergence = np.where(
            (price_change > 0) & (macd_change < 0),
            abs(price_change - macd_change) * (1 + macd_above_zero.astype(float)),
            0
        )
        
        df['macd_bullish_divergence'] = bullish_divergence
        df['macd_bearish_divergence'] = bearish_divergence
        
        # 趋势强度：MACD线与信号线的关系
        df['macd_trend_strength'] = (df['macd'] - df['macdsignal']) / (df['close'].rolling(20).std() + 1e-8)
        df['macd_trend_strength'] = df['macd_trend_strength'].replace([np.inf, -np.inf], 0).fillna(0)
        
        # 动量评分：MACD柱状图的变化率
        df['macd_momentum_score'] = df['macdhist'].pct_change().rolling(3).mean().fillna(0)
    
    def _detect_boolean_divergences(self, df: pd.DataFrame, price_highs: List[int], 
                                  price_lows: List[int], indicator_highs: List[int], 
                                  indicator_lows: List[int], indicator_col: str):
        """检测MACD布尔背离信号（向量化优化）"""
        # 向量化的背离检测
        # 预先计算所有需要的数据
        prices = df['close'].values
        macd_values = df[indicator_col].values
        
        # MACD顶背离检测（向量化）
        for i in range(len(price_highs) - 1):
            idx1, idx2 = price_highs[i], price_highs[i+1]
            
            if idx2 - idx1 < self.config.min_bars_between:
                continue
                
            price1, price2 = prices[idx1], prices[idx2]
            price_change = (price2 - price1) / price1
            
            if abs(price_change) >= self.config.min_price_change and price2 > price1:
                macd1, macd2 = macd_values[idx1], macd_values[idx2]
                macd_change = abs(macd2 - macd1)
                
                if macd_change >= self.config.macd_threshold:
                    if macd2 < macd1:  # MACD创新低 - 顶背离
                        df.iloc[idx2, df.columns.get_loc('macd_downdown_high')] = True
                    elif macd2 > macd1:  # MACD也创新高 - 趋势延续
                        df.iloc[idx2, df.columns.get_loc('macd_upup_high')] = True
        
        # MACD底背离检测（向量化）
        for i in range(len(price_lows) - 1):
            idx1, idx2 = price_lows[i], price_lows[i+1]
            
            if idx2 - idx1 < self.config.min_bars_between:
                continue
                
            price1, price2 = prices[idx1], prices[idx2]
            price_change = (price2 - price1) / price1
            
            if abs(price_change) >= self.config.min_price_change and price2 < price1:
                macd1, macd2 = macd_values[idx1], macd_values[idx2]
                macd_change = abs(macd2 - macd1)
                
                if macd_change >= self.config.macd_threshold:
                    if macd2 > macd1:  # MACD创新高 - 底背离
                        df.iloc[idx2, df.columns.get_loc('macd_upup_low')] = True
                    elif macd2 < macd1:  # MACD也创新低 - 趋势延续
                        df.iloc[idx2, df.columns.get_loc('macd_downdown_low')] = True
    
    @classmethod
    def create_from_params(cls, params: Dict[str, Any], scenario: str = "production") -> 'MACDDivergenceFactor':
        """从参数字典创建因子实例（用于参数优化）"""
        base_config = DivergenceConfig.from_config_file(scenario=scenario, indicator_name="macd")
        config_dict = base_config.__dict__.copy()
        
        # 添加MACD默认参数
        macd_defaults = {
            'fastperiod': 12,
            'slowperiod': 26,
            'signalperiod': 9,
            'use_macd_line': True,
            'macd_threshold': 0.001
        }
        config_dict.update(macd_defaults)
        config = MACDDivergenceConfig(**config_dict)
        
        # 更新参数
        for key, value in params.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        return cls(config)
    
    @classmethod
    def get_param_space(cls) -> Dict[str, Dict]:
        """获取参数空间定义（用于优化）"""
        config = MACDDivergenceConfig()
        return config.get_param_space()
    
    def get_signal_interpretation(self) -> Dict[str, str]:
        """获取信号解释"""
        if self.config.output_mode == "boolean":
            return {
                'macd_upup_high': '价格新高且MACD新高 - 强势上涨趋势延续',
                'macd_upup_low': 'MACD底背离 - 价格新低但MACD新高，看涨信号',
                'macd_downdown_high': 'MACD顶背离 - 价格新高但MACD新低，看跌信号',
                'macd_downdown_low': '价格新低且MACD新低 - 弱势下跌趋势延续'
            }
        else:
            return {
                'macd_bullish_divergence': 'MACD看涨背离强度 - 数值越大看涨信号越强',
                'macd_bearish_divergence': 'MACD看跌背离强度 - 数值越大看跌信号越强',
                'macd_trend_strength': 'MACD趋势强度 - 基于MACD线与信号线关系',
                'macd_momentum_score': 'MACD动量评分 - 基于柱状图变化',
                'macd_divergence_composite': 'MACD组合背离信号 - 综合看涨看跌背离',
                'macd_signal_confidence': 'MACD信号确认度 - 背离信号的可信度',
                'macd_relative_strength': 'MACD相对强度 - 在历史数据中的相对位置'
            }
    
    def get_macd_status(self, df: pd.DataFrame) -> Dict[str, Any]:
        """获取MACD当前状态"""
        if len(df) == 0:
            return {}
        
        latest = df.iloc[-1]
        
        # MACD数值状态
        macd_value = latest.get('macd', np.nan)
        signal_value = latest.get('macdsignal', np.nan)
        hist_value = latest.get('macdhist', np.nan)
        
        # 金叉死叉
        if len(df) >= 2:
            prev = df.iloc[-2]
            prev_macd = prev.get('macd', np.nan)
            prev_signal = prev.get('macdsignal', np.nan)
            
            golden_cross = (macd_value > signal_value) and (prev_macd <= prev_signal)  # 金叉
            death_cross = (macd_value < signal_value) and (prev_macd >= prev_signal)   # 死叉
        else:
            golden_cross = death_cross = False
        
        # 零轴状态
        above_zero = macd_value > 0
        signal_above_zero = signal_value > 0
        
        return {
            'macd_value': macd_value,
            'signal_value': signal_value,
            'hist_value': hist_value,
            'above_zero': above_zero,
            'signal_above_zero': signal_above_zero,
            'golden_cross': golden_cross,
            'death_cross': death_cross,
            'trend_direction': 'up' if macd_value > signal_value else 'down',
            'momentum_strength': abs(hist_value) if not np.isnan(hist_value) else 0
        }
