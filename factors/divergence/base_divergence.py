#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
背离因子基类

提供统一的背离检测框架，支持各种技术指标的背离分析。

核心功能：
1. 局部极值点识别
2. 背离模式检测
3. 信号强度计算
4. 多输出模式支持

作者: QuantFM Team
创建时间: 2025-08-23
"""

import pandas as pd
import numpy as np
import yaml
import os
from typing import Dict, List, Tuple, Optional, Union, Any
import logging
from dataclasses import dataclass
from abc import ABC, abstractmethod
from ..base_factor import BaseFactor

@dataclass
class DivergenceConfig:
    """背离因子配置基类"""
    # 背离检测参数
    window: int = 5
    min_bars_between: int = 10
    min_price_change: float = 0.02
    min_indicator_change: float = 0.01
    
    # 输出控制
    output_mode: str = "continuous"  # "boolean" | "continuous"
    normalize_output: bool = True
    feature_engineering: bool = True
    
    # 性能优化
    smoothing_window: int = 3
    strength_decay: float = 0.95
    enable_cache: bool = True
    
    # Qlib集成
    enable_qlib_mode: bool = False
    auto_optimization: bool = False
    ml_ready: bool = True
    
    @classmethod
    def from_config_file(cls, config_path: str = None, scenario: str = "production",
                        indicator_name: str = "default") -> 'DivergenceConfig':
        """从配置文件加载配置"""
        if config_path is None:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(os.path.dirname(os.path.dirname(current_dir)), 
                                     "config", "divergence_params.yaml")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 映射指标名称到配置名称
            config_name_map = {
                'kdj': 'kdj_divergence',
                'rsi': 'rsi_divergence',
                'macd': 'macd_divergence'
            }
            config_name = config_name_map.get(indicator_name, indicator_name)

            # 获取指标配置
            indicator_config = config_data.get(config_name, {})
            
            # 如果指定了场景，使用场景配置覆盖
            if scenario and scenario in config_data.get('scenarios', {}):
                scenario_config = config_data['scenarios'][scenario].get(config_name, {})
                indicator_config = cls._merge_configs(indicator_config, scenario_config)
            
            # 展平嵌套配置
            flat_config = {}
            for section, params in indicator_config.items():
                if section == 'param_space':
                    continue
                elif isinstance(params, dict):
                    flat_config.update(params)
                else:
                    flat_config[section] = params
            
            return cls(**flat_config)
            
        except Exception as e:
            logging.warning(f"配置文件加载失败，使用默认配置: {e}")
            return cls()
    
    @staticmethod
    def _merge_configs(base_config: dict, override_config: dict) -> dict:
        """合并配置"""
        merged = base_config.copy()
        for key, value in override_config.items():
            if isinstance(value, dict) and key in merged:
                merged[key].update(value)
            else:
                merged[key] = value
        return merged

class BaseDivergenceFactor(BaseFactor, ABC):
    """
    背离因子基类
    
    提供统一的背离检测框架
    """
    
    def __init__(self, name: str, indicator_name: str, config: DivergenceConfig = None, 
                 scenario: str = "production", **kwargs):
        """
        初始化背离因子
        
        Args:
            name: 因子名称
            indicator_name: 指标名称（用于配置加载）
            config: 因子配置对象
            scenario: 使用场景
            **kwargs: 直接参数覆盖
        """
        super().__init__(name, "technical")
        
        self.indicator_name = indicator_name
        
        # 配置初始化
        if config is None:
            self.config = DivergenceConfig.from_config_file(
                scenario=scenario, indicator_name=indicator_name
            )
        else:
            self.config = config
        
        # 参数覆盖
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        
        # 因子缓存
        self._factor_cache = {} if self.config.enable_cache else None
        
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 根据配置决定计算方法
        if self.config.output_mode == "boolean":
            self._calculate_method = self._calculate_boolean_factors
        else:
            self._calculate_method = self._calculate_continuous_factors
    
    @abstractmethod
    def _calculate_indicator(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标（子类实现）"""
        pass
    
    @abstractmethod
    def _get_indicator_column(self) -> str:
        """获取用于背离检测的指标列名（子类实现）"""
        pass
    
    def calculate(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算背离因子
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            添加了背离因子的DataFrame
        """
        try:
            # 检查输入数据
            if df is None:
                self.logger.error("输入数据为None")
                return pd.DataFrame()
            
            if len(df) < 50:  # 最少需要50条数据
                self.logger.warning(f"数据长度不足")
                return self._add_empty_factors(df)
            
            df_result = df.copy()
            
            # 1. 计算技术指标
            df_result = self._calculate_indicator(df_result)
            
            # 2. 根据配置选择计算方法
            df_result = self._calculate_method(df_result)
            
            # 3. 后处理
            if self.config.normalize_output and self.config.output_mode == "continuous":
                df_result = self._normalize_factors(df_result)
            
            return df_result
            
        except Exception as e:
            self.logger.error(f"计算{self.indicator_name}背离因子失败: {e}")
            return self._add_empty_factors(df)
    
    def _find_local_extremes(self, series: pd.Series) -> Tuple[List[int], List[int]]:
        """识别局部极值点"""
        highs = []
        lows = []
        
        for i in range(self.config.window, len(series) - self.config.window):
            window_data = series.iloc[i-self.config.window:i+self.config.window+1]
            
            if series.iloc[i] == window_data.max():
                if not highs or i - highs[-1] >= self.config.min_bars_between:
                    highs.append(i)
            
            if series.iloc[i] == window_data.min():
                if not lows or i - lows[-1] >= self.config.min_bars_between:
                    lows.append(i)
        
        return highs, lows
    
    def _calculate_boolean_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算布尔信号因子"""
        indicator_col = self._get_indicator_column()
        
        # 初始化因子列
        df[f'{self.indicator_name}_upup_high'] = False      # 价格新高，指标新高
        df[f'{self.indicator_name}_upup_low'] = False       # 价格新低，指标新高（底背离）
        df[f'{self.indicator_name}_downdown_high'] = False  # 价格新高，指标新低（顶背离）
        df[f'{self.indicator_name}_downdown_low'] = False   # 价格新低，指标新低
        
        # 识别局部极值点
        price_highs, price_lows = self._find_local_extremes(df['close'])
        indicator_highs, indicator_lows = self._find_local_extremes(df[indicator_col])
        
        # 检测背离
        self._detect_boolean_divergences(df, price_highs, price_lows, 
                                       indicator_highs, indicator_lows, indicator_col)
        
        return df
    
    def _calculate_continuous_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算连续数值因子"""
        # 初始化因子列
        df[f'{self.indicator_name}_bullish_divergence'] = 0.0    # 看涨背离强度
        df[f'{self.indicator_name}_bearish_divergence'] = 0.0    # 看跌背离强度
        df[f'{self.indicator_name}_trend_strength'] = 0.0        # 趋势强度
        df[f'{self.indicator_name}_momentum_score'] = 0.0        # 动量评分
        
        # 计算背离强度
        self._calculate_divergence_strength(df)
        
        # 特征工程
        if self.config.feature_engineering:
            df = self._feature_engineering(df)
        
        # 平滑和衰减
        if self.config.smoothing_window > 1 or self.config.strength_decay < 1.0:
            df = self._apply_smoothing_and_decay(df)
        
        return df
    
    def _detect_boolean_divergences(self, df: pd.DataFrame, price_highs: List[int], 
                                  price_lows: List[int], indicator_highs: List[int], 
                                  indicator_lows: List[int], indicator_col: str):
        """检测布尔背离信号"""
        # 顶背离检测
        for i in range(len(price_highs) - 1):
            idx1, idx2 = price_highs[i], price_highs[i+1]
            
            price1, price2 = df.iloc[idx1]['close'], df.iloc[idx2]['close']
            price_change = (price2 - price1) / price1
            
            if abs(price_change) >= self.config.min_price_change:
                indicator1, indicator2 = df.iloc[idx1][indicator_col], df.iloc[idx2][indicator_col]
                
                if price2 > price1:  # 价格创新高
                    if indicator2 < indicator1:  # 指标创新低 - 顶背离
                        df.iloc[idx2, df.columns.get_loc(f'{self.indicator_name}_downdown_high')] = True
                    elif indicator2 > indicator1:  # 指标也创新高 - 趋势延续
                        df.iloc[idx2, df.columns.get_loc(f'{self.indicator_name}_upup_high')] = True
        
        # 底背离检测
        for i in range(len(price_lows) - 1):
            idx1, idx2 = price_lows[i], price_lows[i+1]
            
            price1, price2 = df.iloc[idx1]['close'], df.iloc[idx2]['close']
            price_change = (price2 - price1) / price1
            
            if abs(price_change) >= self.config.min_price_change:
                indicator1, indicator2 = df.iloc[idx1][indicator_col], df.iloc[idx2][indicator_col]
                
                if price2 < price1:  # 价格创新低
                    if indicator2 > indicator1:  # 指标创新高 - 底背离
                        df.iloc[idx2, df.columns.get_loc(f'{self.indicator_name}_upup_low')] = True
                    elif indicator2 < indicator1:  # 指标也创新低 - 趋势延续
                        df.iloc[idx2, df.columns.get_loc(f'{self.indicator_name}_downdown_low')] = True

    def _calculate_divergence_strength(self, df: pd.DataFrame):
        """计算背离强度（连续数值模式）"""
        indicator_col = self._get_indicator_column()

        # 简化的背离强度计算
        price_change = df['close'].pct_change().rolling(5).sum()
        indicator_change = df[indicator_col].pct_change().rolling(5).sum()

        # 背离强度 = 价格变化与指标变化的差异
        divergence = price_change - indicator_change

        # 看涨背离：价格下跌但指标上涨
        df[f'{self.indicator_name}_bullish_divergence'] = np.where(
            (price_change < 0) & (indicator_change > 0),
            abs(divergence), 0
        )

        # 看跌背离：价格上涨但指标下跌
        df[f'{self.indicator_name}_bearish_divergence'] = np.where(
            (price_change > 0) & (indicator_change < 0),
            abs(divergence), 0
        )

        # 趋势强度
        df[f'{self.indicator_name}_trend_strength'] = price_change * indicator_change

        # 动量评分
        df[f'{self.indicator_name}_momentum_score'] = df[indicator_col].pct_change().rolling(3).mean()

    def _feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """特征工程"""
        # 组合因子
        df[f'{self.indicator_name}_divergence_composite'] = (
            df[f'{self.indicator_name}_bullish_divergence'] -
            df[f'{self.indicator_name}_bearish_divergence']
        )

        # 信号确认度
        df[f'{self.indicator_name}_signal_confidence'] = (
            abs(df[f'{self.indicator_name}_bullish_divergence']) +
            abs(df[f'{self.indicator_name}_bearish_divergence'])
        ) * abs(df[f'{self.indicator_name}_trend_strength'])

        # 相对强度
        df[f'{self.indicator_name}_relative_strength'] = (
            df[f'{self.indicator_name}_divergence_composite'].rolling(20).rank(pct=True)
        )

        return df

    def _apply_smoothing_and_decay(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用平滑和衰减"""
        factor_cols = [col for col in df.columns if col.startswith(f'{self.indicator_name}_')]

        for col in factor_cols:
            if self.config.smoothing_window > 1:
                df[col] = df[col].rolling(self.config.smoothing_window, center=True).mean().fillna(df[col])

            if self.config.strength_decay < 1.0:
                df[col] = df[col] * (self.config.strength_decay ** np.arange(len(df)))

        return df

    def _normalize_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化因子输出"""
        factor_cols = [col for col in df.columns if col.startswith(f'{self.indicator_name}_')]

        for col in factor_cols:
            mean_val = df[col].rolling(252, min_periods=20).mean()
            std_val = df[col].rolling(252, min_periods=20).std()

            df[col + '_normalized'] = (df[col] - mean_val) / (std_val + 1e-8)
            df[col + '_normalized'] = df[col + '_normalized'].fillna(0)

        return df

    def _add_empty_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加空的因子列"""
        if df is None:
            return pd.DataFrame()

        df_result = df.copy()

        if self.config.output_mode == "boolean":
            df_result[f'{self.indicator_name}_upup_high'] = False
            df_result[f'{self.indicator_name}_upup_low'] = False
            df_result[f'{self.indicator_name}_downdown_high'] = False
            df_result[f'{self.indicator_name}_downdown_low'] = False
        else:
            factor_names = [
                f'{self.indicator_name}_bullish_divergence',
                f'{self.indicator_name}_bearish_divergence',
                f'{self.indicator_name}_trend_strength',
                f'{self.indicator_name}_momentum_score'
            ]

            if self.config.feature_engineering:
                factor_names.extend([
                    f'{self.indicator_name}_divergence_composite',
                    f'{self.indicator_name}_signal_confidence',
                    f'{self.indicator_name}_relative_strength'
                ])

            for name in factor_names:
                df_result[name] = 0.0
                if self.config.normalize_output:
                    df_result[name + '_normalized'] = 0.0

        return df_result

    def get_factor_names(self) -> List[str]:
        """获取因子名称列表"""
        if self.config.output_mode == "boolean":
            return [
                f'{self.indicator_name}_upup_high',
                f'{self.indicator_name}_upup_low',
                f'{self.indicator_name}_downdown_high',
                f'{self.indicator_name}_downdown_low'
            ]
        else:
            base_names = [
                f'{self.indicator_name}_bullish_divergence',
                f'{self.indicator_name}_bearish_divergence',
                f'{self.indicator_name}_trend_strength',
                f'{self.indicator_name}_momentum_score'
            ]

            if self.config.feature_engineering:
                base_names.extend([
                    f'{self.indicator_name}_divergence_composite',
                    f'{self.indicator_name}_signal_confidence',
                    f'{self.indicator_name}_relative_strength'
                ])

            if self.config.normalize_output:
                normalized_names = [name + '_normalized' for name in base_names]
                return base_names + normalized_names

            return base_names
