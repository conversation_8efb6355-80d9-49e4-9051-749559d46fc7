#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
背离因子模块

提供统一的技术指标背离分析框架，包括：
- MACD背离因子
- KDJ背离因子  
- RSI背离因子

核心特性：
1. 统一的背离检测算法
2. 多种输出模式（布尔信号/连续数值）
3. 场景化配置管理
4. Qlib兼容性
5. 参数优化支持

作者: QuantFM Team
创建时间: 2025-08-23
"""

from .base_divergence import BaseDivergenceFactor, DivergenceConfig
from .macd_divergence import MACDDivergenceFactor, MACDDivergenceConfig
from .kdj_divergence import KDJDivergenceFactor, KDJDivergenceConfig
from .rsi_divergence import RSIDivergenceFactor, RSIDivergenceConfig

__all__ = [
    # 基类
    'BaseDivergenceFactor',
    'DivergenceConfig',
    
    # KDJ背离因子
    'KDJDivergenceFactor', 
    'KDJDivergenceConfig',
    
    # RSI背离因子
    'RSIDivergenceFactor',
    'RSIDivergenceConfig',
    
    # MACD背离因子
    'MACDDivergenceFactor',
    'MACDDivergenceConfig',
]

# 版本信息
__version__ = "1.0.0"
__author__ = "QuantFM Team"

# 支持的指标列表
SUPPORTED_INDICATORS = [
    'macd',
    'kdj', 
    'rsi'
]

# 工厂函数
def create_divergence_factor(indicator: str, scenario: str = "production", **kwargs):
    """
    创建背离因子的工厂函数
    
    Args:
        indicator: 指标名称 ('macd', 'kdj', 'rsi')
        scenario: 使用场景
        **kwargs: 其他参数
        
    Returns:
        对应的背离因子实例
        
    Raises:
        ValueError: 不支持的指标类型
    """
    indicator = indicator.lower()
    
    if indicator == 'macd':
        return MACDDivergenceFactor(scenario=scenario, **kwargs)
    elif indicator == 'kdj':
        return KDJDivergenceFactor(scenario=scenario, **kwargs)
    elif indicator == 'rsi':
        return RSIDivergenceFactor(scenario=scenario, **kwargs)
    else:
        raise ValueError(f"不支持的指标类型: {indicator}. 支持的指标: {SUPPORTED_INDICATORS}")

def get_all_divergence_factors(scenario: str = "production", **kwargs):
    """
    获取所有可用的背离因子
    
    Args:
        scenario: 使用场景
        **kwargs: 其他参数
        
    Returns:
        Dict[str, BaseDivergenceFactor]: 指标名称到因子实例的映射
    """
    factors = {}
    
    for indicator in SUPPORTED_INDICATORS:
        try:
            factors[indicator] = create_divergence_factor(indicator, scenario, **kwargs)
        except (ImportError, ValueError) as e:
            print(f"警告: 无法创建{indicator}背离因子: {e}")
    
    return factors

def compare_divergence_signals(df, indicators=None, scenario="development"):
    """
    比较多个指标的背离信号
    
    Args:
        df: 价格数据
        indicators: 要比较的指标列表，默认为所有支持的指标
        scenario: 使用场景
        
    Returns:
        Dict: 各指标的背离信号统计
    """
    if indicators is None:
        indicators = SUPPORTED_INDICATORS
    
    results = {}
    
    for indicator in indicators:
        try:
            factor = create_divergence_factor(indicator, scenario)
            df_result = factor.calculate(df.copy())
            
            if factor.config.output_mode == "boolean":
                # 统计布尔信号
                buy_signals = df_result[f'{indicator}_upup_low'].sum()
                sell_signals = df_result[f'{indicator}_downdown_high'].sum()
                results[indicator] = {
                    'buy_signals': buy_signals,
                    'sell_signals': sell_signals,
                    'total_signals': buy_signals + sell_signals
                }
            else:
                # 统计连续信号
                non_zero_bullish = (df_result[f'{indicator}_bullish_divergence'] > 0).sum()
                non_zero_bearish = (df_result[f'{indicator}_bearish_divergence'] > 0).sum()
                results[indicator] = {
                    'bullish_signals': non_zero_bullish,
                    'bearish_signals': non_zero_bearish,
                    'total_signals': non_zero_bullish + non_zero_bearish
                }
                
        except Exception as e:
            results[indicator] = {'error': str(e)}
    
    return results
