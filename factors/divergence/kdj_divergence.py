#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KDJ背离因子

基于KDJ（随机指标）与价格的背离分析，识别潜在的趋势反转信号。

KDJ指标说明：
- K线：快速确认线
- D线：慢速主线  
- J线：方向敏感线

背离检测逻辑：
1. KDJ顶背离：价格创新高，但KDJ指标未创新高（看跌信号）
2. KDJ底背离：价格创新低，但KDJ指标未创新低（看涨信号）

作者: QuantFM Team
创建时间: 2025-08-23
"""

import pandas as pd
import numpy as np
import talib
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from .base_divergence import BaseDivergenceFactor, DivergenceConfig

@dataclass
class KDJDivergenceConfig(DivergenceConfig):
    """KDJ背离因子配置类"""
    # KDJ参数
    fastk_period: int = 9      # K线周期
    slowk_period: int = 3      # K线平滑周期
    slowd_period: int = 3      # D线平滑周期
    
    # 背离检测参数
    use_k_line: bool = True    # 是否使用K线进行背离检测（否则使用D线）
    kdj_threshold: float = 0.5 # KDJ变化阈值
    
    def get_param_space(self) -> Dict[str, Dict]:
        """获取参数空间（用于优化）"""
        base_space = {
            'fastk_period': {'type': 'int', 'range': [5, 14], 'default': self.fastk_period},
            'slowk_period': {'type': 'int', 'range': [1, 5], 'default': self.slowk_period},
            'slowd_period': {'type': 'int', 'range': [1, 5], 'default': self.slowd_period},
            'window': {'type': 'int', 'range': [3, 10], 'default': self.window},
            'min_bars_between': {'type': 'int', 'range': [5, 20], 'default': self.min_bars_between},
            'min_price_change': {'type': 'float', 'range': [0.01, 0.05], 'default': self.min_price_change},
            'kdj_threshold': {'type': 'float', 'range': [0.1, 2.0], 'default': self.kdj_threshold}
        }
        return base_space

class KDJDivergenceFactor(BaseDivergenceFactor):
    """
    KDJ背离因子
    
    基于KDJ随机指标的背离分析
    """
    
    def __init__(self, config: KDJDivergenceConfig = None, scenario: str = "production", **kwargs):
        """
        初始化KDJ背离因子

        Args:
            config: 因子配置对象
            scenario: 使用场景 ("development", "production", "qlib_training")
            **kwargs: 直接参数覆盖
        """
        # 配置初始化
        if config is None:
            base_config = DivergenceConfig.from_config_file(scenario=scenario, indicator_name="kdj")
            # 转换为KDJ配置，保留所有基础配置
            config_dict = base_config.__dict__.copy()
            # 添加KDJ特有的默认参数
            kdj_defaults = {
                'fastk_period': 9,
                'slowk_period': 3,
                'slowd_period': 3,
                'use_k_line': True,
                'kdj_threshold': 0.5
            }
            config_dict.update(kdj_defaults)
            config = KDJDivergenceConfig(**config_dict)

        # 参数覆盖
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)

        super().__init__("KDJ_Divergence", "kdj", config, scenario, **kwargs)

        # 因子缓存
        self._factor_cache = {} if self.config.enable_cache else None

        # 根据配置决定计算方法
        if self.config.output_mode == "boolean":
            self._calculate_method = self._calculate_boolean_factors
        else:
            self._calculate_method = self._calculate_continuous_factors

    def calculate(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算KDJ背离因子

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            添加了背离因子的DataFrame
        """
        try:
            # 检查输入数据
            if df is None:
                self.logger.error("输入数据为None")
                return pd.DataFrame()

            if len(df) < 50:  # 最少需要50条数据
                self.logger.warning(f"数据长度不足")
                return self._add_empty_factors(df)

            df_result = df.copy()

            # 1. 计算KDJ指标
            df_result = self._calculate_indicator(df_result)

            # 2. 根据配置选择计算方法
            df_result = self._calculate_method(df_result)

            # 3. 后处理
            if self.config.normalize_output and self.config.output_mode == "continuous":
                df_result = self._normalize_factors(df_result)

            return df_result

        except Exception as e:
            self.logger.error(f"计算KDJ背离因子失败: {e}")
            return self._add_empty_factors(df)

    def _calculate_boolean_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算布尔信号因子"""
        # 初始化因子列
        df['kdj_upup_high'] = False      # 价格新高，KDJ新高
        df['kdj_upup_low'] = False       # 价格新低，KDJ新高（底背离）
        df['kdj_downdown_high'] = False  # 价格新高，KDJ新低（顶背离）
        df['kdj_downdown_low'] = False   # 价格新低，KDJ新低

        # 识别局部极值点
        price_highs, price_lows = self._find_local_extremes(df['close'])
        kdj_highs, kdj_lows = self._find_local_extremes(df[self._get_indicator_column()])

        # 检测背离
        self._detect_boolean_divergences(df, price_highs, price_lows, kdj_highs, kdj_lows, self._get_indicator_column())

        return df

    def _calculate_continuous_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算连续数值因子"""
        # 初始化因子列
        df['kdj_bullish_divergence'] = 0.0    # 看涨背离强度
        df['kdj_bearish_divergence'] = 0.0    # 看跌背离强度
        df['kdj_trend_strength'] = 0.0        # 趋势强度
        df['kdj_momentum_score'] = 0.0        # 动量评分

        # 计算背离强度
        self._calculate_divergence_strength(df)

        # 特征工程
        if self.config.feature_engineering:
            df = self._feature_engineering(df)

        # 平滑和衰减
        if self.config.smoothing_window > 1 or self.config.strength_decay < 1.0:
            df = self._apply_smoothing_and_decay(df)

        return df
    
    def _calculate_indicator(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算KDJ指标"""
        try:
            # 计算KDJ指标
            slowk, slowd = talib.STOCH(
                df['high'].values,
                df['low'].values, 
                df['close'].values,
                fastk_period=self.config.fastk_period,
                slowk_period=self.config.slowk_period,
                slowd_period=self.config.slowd_period
            )
            
            df['kdj_k'] = slowk
            df['kdj_d'] = slowd
            df['kdj_j'] = 3 * slowk - 2 * slowd  # J = 3K - 2D
            
            return df
            
        except Exception as e:
            self.logger.error(f"KDJ指标计算失败: {e}")
            # 添加空列避免后续错误
            df['kdj_k'] = np.nan
            df['kdj_d'] = np.nan
            df['kdj_j'] = np.nan
            return df
    
    def _get_indicator_column(self) -> str:
        """获取用于背离检测的指标列名"""
        return 'kdj_k' if self.config.use_k_line else 'kdj_d'
    
    def _calculate_divergence_strength(self, df: pd.DataFrame):
        """计算KDJ背离强度（向量化实现）"""
        indicator_col = self._get_indicator_column()

        # 向量化计算价格和KDJ的变化率
        price_change = df['close'].pct_change().rolling(5).sum()
        kdj_change = df[indicator_col].pct_change().rolling(5).sum()

        # KDJ特有的背离强度计算（向量化）
        # 考虑KDJ的超买超卖区域
        kdj_overbought = df[indicator_col] > 80
        kdj_oversold = df[indicator_col] < 20

        # 向量化的背离强度计算
        # 看涨背离：价格下跌但KDJ上涨，特别是在超卖区域
        bullish_divergence = np.where(
            (price_change < 0) & (kdj_change > 0),
            abs(price_change) * abs(kdj_change) * (1 + kdj_oversold.astype(float) * 2),
            0
        )

        # 看跌背离：价格上涨但KDJ下跌，特别是在超买区域
        bearish_divergence = np.where(
            (price_change > 0) & (kdj_change < 0),
            abs(price_change) * abs(kdj_change) * (1 + kdj_overbought.astype(float) * 2),
            0
        )

        df['kdj_bullish_divergence'] = bullish_divergence
        df['kdj_bearish_divergence'] = bearish_divergence

        # 趋势强度：结合K、D、J三线（向量化）
        k_d_diff = df['kdj_k'] - df['kdj_d']
        j_center = df['kdj_j'] - 50
        df['kdj_trend_strength'] = k_d_diff * np.sign(j_center) / 100  # 标准化

        # 动量评分：基于J线的变化（向量化）
        df['kdj_momentum_score'] = df['kdj_j'].pct_change().rolling(3).mean().fillna(0)
    
    def _detect_boolean_divergences(self, df: pd.DataFrame, price_highs: List[int],
                                  price_lows: List[int], indicator_highs: List[int],
                                  indicator_lows: List[int], indicator_col: str):
        """检测KDJ布尔背离信号（向量化优化）"""
        # 向量化的背离检测
        # 预先计算所有需要的数据
        prices = df['close'].values
        kdj_values = df[indicator_col].values

        # KDJ顶背离检测（向量化）
        for i in range(len(price_highs) - 1):
            idx1, idx2 = price_highs[i], price_highs[i+1]

            if idx2 - idx1 < self.config.min_bars_between:
                continue

            price1, price2 = prices[idx1], prices[idx2]
            price_change = (price2 - price1) / price1

            if abs(price_change) >= self.config.min_price_change and price2 > price1:
                kdj1, kdj2 = kdj_values[idx1], kdj_values[idx2]
                kdj_change = abs(kdj2 - kdj1)

                if kdj_change >= self.config.kdj_threshold:
                    if kdj2 < kdj1:  # KDJ创新低 - 顶背离
                        df.iloc[idx2, df.columns.get_loc('kdj_downdown_high')] = True
                    elif kdj2 > kdj1:  # KDJ也创新高 - 趋势延续
                        df.iloc[idx2, df.columns.get_loc('kdj_upup_high')] = True

        # KDJ底背离检测（向量化）
        for i in range(len(price_lows) - 1):
            idx1, idx2 = price_lows[i], price_lows[i+1]

            if idx2 - idx1 < self.config.min_bars_between:
                continue

            price1, price2 = prices[idx1], prices[idx2]
            price_change = (price2 - price1) / price1

            if abs(price_change) >= self.config.min_price_change and price2 < price1:
                kdj1, kdj2 = kdj_values[idx1], kdj_values[idx2]
                kdj_change = abs(kdj2 - kdj1)

                if kdj_change >= self.config.kdj_threshold:
                    if kdj2 > kdj1:  # KDJ创新高 - 底背离
                        df.iloc[idx2, df.columns.get_loc('kdj_upup_low')] = True
                    elif kdj2 < kdj1:  # KDJ也创新低 - 趋势延续
                        df.iloc[idx2, df.columns.get_loc('kdj_downdown_low')] = True

    def _feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """特征工程"""
        # 组合因子
        df['kdj_divergence_composite'] = (
            df['kdj_bullish_divergence'] - df['kdj_bearish_divergence']
        )

        # 信号确认度
        df['kdj_signal_confidence'] = (
            abs(df['kdj_bullish_divergence']) + abs(df['kdj_bearish_divergence'])
        ) * abs(df['kdj_trend_strength'])

        # 相对强度
        df['kdj_relative_strength'] = df['kdj_divergence_composite'].rolling(20).rank(pct=True)

        return df

    def _apply_smoothing_and_decay(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用平滑和衰减"""
        factor_cols = [col for col in df.columns if col.startswith('kdj_')]

        for col in factor_cols:
            if self.config.smoothing_window > 1:
                df[col] = df[col].rolling(self.config.smoothing_window, center=True).mean().fillna(df[col])

            if self.config.strength_decay < 1.0:
                df[col] = df[col] * (self.config.strength_decay ** np.arange(len(df)))

        return df

    def _normalize_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化因子输出"""
        factor_cols = [col for col in df.columns if col.startswith('kdj_')]

        for col in factor_cols:
            mean_val = df[col].rolling(252, min_periods=20).mean()
            std_val = df[col].rolling(252, min_periods=20).std()

            df[col + '_normalized'] = (df[col] - mean_val) / (std_val + 1e-8)
            df[col + '_normalized'] = df[col + '_normalized'].fillna(0)

        return df

    def _add_empty_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加空的因子列"""
        if df is None:
            return pd.DataFrame()

        df_result = df.copy()

        if self.config.output_mode == "boolean":
            df_result['kdj_upup_high'] = False
            df_result['kdj_upup_low'] = False
            df_result['kdj_downdown_high'] = False
            df_result['kdj_downdown_low'] = False
        else:
            factor_names = [
                'kdj_bullish_divergence', 'kdj_bearish_divergence',
                'kdj_trend_strength', 'kdj_momentum_score'
            ]

            if self.config.feature_engineering:
                factor_names.extend(['kdj_divergence_composite', 'kdj_signal_confidence', 'kdj_relative_strength'])

            for name in factor_names:
                df_result[name] = 0.0
                if self.config.normalize_output:
                    df_result[name + '_normalized'] = 0.0

        return df_result

    def get_factor_names(self) -> List[str]:
        """获取因子名称列表"""
        if self.config.output_mode == "boolean":
            return ['kdj_upup_high', 'kdj_upup_low', 'kdj_downdown_high', 'kdj_downdown_low']
        else:
            base_names = ['kdj_bullish_divergence', 'kdj_bearish_divergence',
                         'kdj_trend_strength', 'kdj_momentum_score']

            if self.config.feature_engineering:
                base_names.extend(['kdj_divergence_composite', 'kdj_signal_confidence', 'kdj_relative_strength'])

            if self.config.normalize_output:
                normalized_names = [name + '_normalized' for name in base_names]
                return base_names + normalized_names

            return base_names
    
    @classmethod
    def create_from_params(cls, params: Dict[str, Any], scenario: str = "production") -> 'KDJDivergenceFactor':
        """从参数字典创建因子实例（用于参数优化）"""
        base_config = DivergenceConfig.from_config_file(scenario=scenario, indicator_name="kdj_divergence")
        config_dict = base_config.__dict__.copy()
        config = KDJDivergenceConfig(**config_dict)
        
        # 更新参数
        for key, value in params.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        return cls(config)
    
    @classmethod
    def get_param_space(cls) -> Dict[str, Dict]:
        """获取参数空间定义（用于优化）"""
        config = KDJDivergenceConfig()
        return config.get_param_space()
    
    def get_signal_interpretation(self) -> Dict[str, str]:
        """获取信号解释"""
        if self.config.output_mode == "boolean":
            return {
                'kdj_upup_high': '价格新高且KDJ新高 - 强势上涨趋势延续',
                'kdj_upup_low': 'KDJ底背离 - 价格新低但KDJ新高，看涨信号',
                'kdj_downdown_high': 'KDJ顶背离 - 价格新高但KDJ新低，看跌信号',
                'kdj_downdown_low': '价格新低且KDJ新低 - 弱势下跌趋势延续'
            }
        else:
            return {
                'kdj_bullish_divergence': 'KDJ看涨背离强度 - 数值越大看涨信号越强',
                'kdj_bearish_divergence': 'KDJ看跌背离强度 - 数值越大看跌信号越强',
                'kdj_trend_strength': 'KDJ趋势强度 - 基于K、D、J三线关系',
                'kdj_momentum_score': 'KDJ动量评分 - 基于J线变化',
                'kdj_divergence_composite': 'KDJ组合背离信号 - 综合看涨看跌背离',
                'kdj_signal_confidence': 'KDJ信号确认度 - 背离信号的可信度',
                'kdj_relative_strength': 'KDJ相对强度 - 在历史数据中的相对位置'
            }
    
    def get_kdj_status(self, df: pd.DataFrame) -> Dict[str, Any]:
        """获取KDJ当前状态"""
        if len(df) == 0:
            return {}
        
        latest = df.iloc[-1]
        
        # KDJ数值状态
        k_value = latest.get('kdj_k', np.nan)
        d_value = latest.get('kdj_d', np.nan) 
        j_value = latest.get('kdj_j', np.nan)
        
        # 超买超卖状态
        overbought = k_value > 80 and d_value > 80
        oversold = k_value < 20 and d_value < 20
        
        # 金叉死叉
        if len(df) >= 2:
            prev = df.iloc[-2]
            prev_k = prev.get('kdj_k', np.nan)
            prev_d = prev.get('kdj_d', np.nan)
            
            golden_cross = (k_value > d_value) and (prev_k <= prev_d)  # 金叉
            death_cross = (k_value < d_value) and (prev_k >= prev_d)   # 死叉
        else:
            golden_cross = death_cross = False
        
        return {
            'k_value': k_value,
            'd_value': d_value,
            'j_value': j_value,
            'overbought': overbought,
            'oversold': oversold,
            'golden_cross': golden_cross,
            'death_cross': death_cross,
            'trend_direction': 'up' if k_value > d_value else 'down'
        }
