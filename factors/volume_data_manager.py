#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成交量数据管理器

专门负责成交量相关数据的获取、缓存和计算。

功能特点：
1. 历史数据查询
2. 实时数据获取
3. K线数据管理
4. 缓存优化
5. 数据预处理
6. 批量处理

作者: QuantFM Team
创建时间: 2025-08-08
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time as dt_time
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, deque
import threading
import time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger
from data.db_manager import get_db_manager
from utils.realtime_kline_calculator import RealTimeKlineCalculator


class VolumeDataManager:
    """成交量数据管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化成交量数据管理器
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.logger = get_logger("VolumeDataManager")
        
        # 数据库管理器
        self.db_manager = get_db_manager()
        
        # K线计算器
        self.kline_calculator = RealTimeKlineCalculator(self.logger)
        
        # 缓存配置
        self.enable_cache = config.get("enable_cache", True)
        self.cache_ttl = config.get("cache_ttl", 3600)
        self.max_cache_size = config.get("max_cache_size", 10000)
        
        # 多级缓存
        self.opening_avg_cache = {}  # 开盘期历史平均缓存
        self.intraday_avg_cache = {}  # 盘中期平均缓存
        self.realtime_volume_cache = {}  # 实时成交量缓存
        self.kline_cache = {}  # K线数据缓存
        
        # 缓存时间戳
        self.cache_timestamps = {}
        
        # 数据预加载
        self.preloaded_data = {}
        self.preload_enabled = config.get("preload_historical_data", True)
        
        # 线程锁
        self._cache_lock = threading.RLock()
        
        # 性能统计
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'db_queries': 0,
            'data_events_processed': 0,
            'preload_operations': 0
        }
        
        # 连续监控状态
        self.monitoring_active = False
        self.last_data_update = {}
        
        self.logger.info("成交量数据管理器初始化完成")
    
    def initialize(self, stock_list: List[str]):
        """
        初始化数据管理器
        
        Args:
            stock_list: 股票列表
        """
        self.logger.info(f"初始化成交量数据管理器，股票数量: {len(stock_list)}")
        
        try:
            if self.preload_enabled:
                # 预加载开盘期历史数据
                self._preload_opening_historical_data(stock_list)
                
                # 预加载当日已有数据
                self._preload_today_data(stock_list)
            
            self.logger.info("成交量数据管理器初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化成交量数据管理器失败: {e}")
            raise
    
    def start_continuous_monitoring(self):
        """启动连续监控模式"""
        self.monitoring_active = True
        self.logger.info("启动连续监控模式")
    
    def stop_continuous_monitoring(self):
        """停止连续监控模式"""
        self.monitoring_active = False
        self.logger.info("停止连续监控模式")
    
    def get_opening_historical_avg(self, stock_code: str, days: int = 10) -> float:
        """
        获取开盘期历史平均成交量
        
        Args:
            stock_code: 股票代码
            days: 历史天数
            
        Returns:
            开盘期历史平均成交量
        """
        try:
            # 检查缓存
            cache_key = f"opening_avg_{stock_code}_{days}"
            cached_value = self._get_from_cache(cache_key)
            if cached_value is not None:
                return cached_value
            
            # 查询数据库
            self.stats['db_queries'] += 1
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days)
            
            query = """
                SELECT AVG(volume_sum) as avg_volume
                FROM (
                    SELECT 
                        DATE(trade_time) as trade_date,
                        SUM(cur_vol) as volume_sum
                    FROM stock_tick_data 
                    WHERE stock_code = %s 
                      AND DATE(trade_time) BETWEEN %s AND %s
                      AND TIME(trade_time) BETWEEN '09:30:00' AND '09:45:00'
                    GROUP BY DATE(trade_time)
                ) daily_volumes
                WHERE volume_sum > 0
            """
            
            result = self.db_manager.fetch_all(query, (stock_code, start_date, end_date))
            
            if result and result[0] and result[0][0]:
                avg_volume = float(result[0][0])
                self._set_cache(cache_key, avg_volume)
                return avg_volume
            else:
                self.logger.debug(f"股票 {stock_code} 无开盘期历史数据")
                return 0.0
                
        except Exception as e:
            self.logger.error(f"获取开盘期历史平均成交量失败 {stock_code}: {e}")
            return 0.0
    
    def get_intraday_avg(self, stock_code: str, until_time: datetime = None) -> float:
        """
        获取盘中期历史平均成交量
        
        Args:
            stock_code: 股票代码
            until_time: 截止时间，默认为当前时间
            
        Returns:
            盘中期历史平均成交量
        """
        try:
            if until_time is None:
                until_time = datetime.now()
            
            # 检查缓存
            time_key = until_time.strftime("%H%M")
            cache_key = f"intraday_avg_{stock_code}_{time_key}"
            cached_value = self._get_from_cache(cache_key)
            if cached_value is not None:
                return cached_value
            
            # 查询当日已完成的5分钟K线平均成交量
            self.stats['db_queries'] += 1
            today = until_time.date()
            until_time_str = until_time.strftime('%H:%M:%S')
            
            query = """
                SELECT AVG(volume_sum) as avg_volume
                FROM (
                    SELECT 
                        time_bucket('5 minutes', trade_time) as bucket_time,
                        SUM(cur_vol) as volume_sum
                    FROM stock_tick_data 
                    WHERE stock_code = %s 
                      AND DATE(trade_time) = %s
                      AND TIME(trade_time) BETWEEN '09:45:00' AND %s
                    GROUP BY bucket_time
                    HAVING SUM(cur_vol) > 0
                ) bucket_volumes
            """
            
            result = self.db_manager.fetch_all(query, (stock_code, today, until_time_str))
            
            if result and result[0] and result[0][0]:
                avg_volume = float(result[0][0])
                self._set_cache(cache_key, avg_volume)
                return avg_volume
            else:
                # 如果当日没有数据，返回0
                return 0.0
                
        except Exception as e:
            self.logger.error(f"获取盘中期历史平均成交量失败 {stock_code}: {e}")
            return 0.0
    
    def get_current_volume(self, stock_code: str) -> float:
        """
        获取当前实时成交量
        
        Args:
            stock_code: 股票代码
            
        Returns:
            当前成交量
        """
        try:
            # 检查缓存
            cache_key = f"current_volume_{stock_code}"
            cached_value = self._get_from_cache(cache_key, ttl=30)  # 30秒缓存
            if cached_value is not None:
                return cached_value
            
            # 查询最新tick数据
            self.stats['db_queries'] += 1
            current_time = datetime.now()
            
            # 获取当前时间段的累计成交量
            if self._is_opening_period(current_time.time()):
                # 开盘期：从09:30开始累计
                start_time = current_time.replace(hour=9, minute=30, second=0, microsecond=0)
            else:
                # 盘中期：当前5分钟桶的累计
                minute = current_time.minute
                aligned_minute = (minute // 5) * 5
                start_time = current_time.replace(minute=aligned_minute, second=0, microsecond=0)
            
            query = """
                SELECT SUM(cur_vol) as total_volume
                FROM stock_tick_data 
                WHERE stock_code = %s 
                  AND trade_time >= %s 
                  AND trade_time <= %s
            """
            
            result = self.db_manager.fetch_all(query, (stock_code, start_time, current_time))
            
            if result and result[0] and result[0][0]:
                volume = float(result[0][0])
                self._set_cache(cache_key, volume, ttl=30)
                return volume
            else:
                return 0.0
                
        except Exception as e:
            self.logger.error(f"获取当前成交量失败 {stock_code}: {e}")
            return 0.0
    
    def get_current_kline(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        获取当前5分钟K线数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            K线数据字典
        """
        try:
            current_time = datetime.now()
            
            # 计算当前5分钟桶的时间
            minute = current_time.minute
            aligned_minute = (minute // 5) * 5
            bucket_start = current_time.replace(minute=aligned_minute, second=0, microsecond=0)
            bucket_end = bucket_start + timedelta(minutes=5)
            
            # 检查缓存
            cache_key = f"kline_{stock_code}_{bucket_start.strftime('%H%M')}"
            cached_value = self._get_from_cache(cache_key, ttl=60)  # 1分钟缓存
            if cached_value is not None:
                return cached_value
            
            # 查询tick数据并计算K线
            self.stats['db_queries'] += 1
            query = """
                SELECT trade_time, price, cur_vol, amount
                FROM stock_tick_data 
                WHERE stock_code = %s 
                  AND trade_time >= %s 
                  AND trade_time < %s
                ORDER BY trade_time
            """
            
            result = self.db_manager.fetch_all(query, (stock_code, bucket_start, bucket_end))
            
            if not result:
                return None
            
            # 计算K线数据
            df = pd.DataFrame(result, columns=['trade_time', 'price', 'volume', 'amount'])
            df['trade_time'] = pd.to_datetime(df['trade_time'])
            df['price'] = df['price'].astype(float)
            df['volume'] = df['volume'].astype(int)
            df['amount'] = df['amount'].astype(float)
            
            if len(df) == 0:
                return None
            
            kline_data = {
                'trade_time': bucket_start,
                'open': df['price'].iloc[0],
                'high': df['price'].max(),
                'low': df['price'].min(),
                'close': df['price'].iloc[-1],
                'volume': df['volume'].sum(),
                'amount': df['amount'].sum()
            }
            
            self._set_cache(cache_key, kline_data, ttl=60)
            return kline_data
            
        except Exception as e:
            self.logger.error(f"获取当前K线数据失败 {stock_code}: {e}")
            return None

    def batch_get_current_volumes(self, stock_codes: List[str]) -> Dict[str, float]:
        """
        批量获取当前成交量

        Args:
            stock_codes: 股票代码列表

        Returns:
            股票成交量字典
        """
        try:
            result_dict = {}
            current_time = datetime.now()

            # 确定时间范围
            if self._is_opening_period(current_time.time()):
                start_time = current_time.replace(hour=9, minute=30, second=0, microsecond=0)
            else:
                minute = current_time.minute
                aligned_minute = (minute // 5) * 5
                start_time = current_time.replace(minute=aligned_minute, second=0, microsecond=0)

            # 批量查询
            self.stats['db_queries'] += 1
            placeholders = ','.join(['%s'] * len(stock_codes))
            query = f"""
                SELECT stock_code, SUM(cur_vol) as total_volume
                FROM stock_tick_data
                WHERE stock_code IN ({placeholders})
                  AND trade_time >= %s
                  AND trade_time <= %s
                GROUP BY stock_code
            """

            params = stock_codes + [start_time, current_time]
            result = self.db_manager.fetch_all(query, params)

            # 处理结果
            for row in result:
                stock_code, volume = row
                result_dict[stock_code] = float(volume) if volume else 0.0

            # 补充没有数据的股票
            for stock_code in stock_codes:
                if stock_code not in result_dict:
                    result_dict[stock_code] = 0.0

            return result_dict

        except Exception as e:
            self.logger.error(f"批量获取当前成交量失败: {e}")
            return {code: 0.0 for code in stock_codes}

    def _preload_opening_historical_data(self, stock_list: List[str], days: int = 10):
        """预加载开盘期历史数据"""
        try:
            self.logger.info(f"开始预加载 {len(stock_list)} 只股票的开盘期历史数据")
            self.stats['preload_operations'] += 1

            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days)

            # 批量查询
            batch_size = 100
            for i in range(0, len(stock_list), batch_size):
                batch_stocks = stock_list[i:i + batch_size]
                self._batch_load_opening_data(batch_stocks, start_date, end_date, days)

            self.logger.info("开盘期历史数据预加载完成")

        except Exception as e:
            self.logger.error(f"预加载开盘期历史数据失败: {e}")

    def _batch_load_opening_data(self, stock_codes: List[str], start_date, end_date, days: int):
        """批量加载开盘期数据"""
        try:
            placeholders = ','.join(['%s'] * len(stock_codes))
            query = f"""
                SELECT
                    stock_code,
                    AVG(volume_sum) as avg_volume
                FROM (
                    SELECT
                        stock_code,
                        DATE(trade_time) as trade_date,
                        SUM(cur_vol) as volume_sum
                    FROM stock_tick_data
                    WHERE stock_code IN ({placeholders})
                      AND DATE(trade_time) BETWEEN %s AND %s
                      AND TIME(trade_time) BETWEEN '09:30:00' AND '09:45:00'
                    GROUP BY stock_code, DATE(trade_time)
                ) daily_volumes
                WHERE volume_sum > 0
                GROUP BY stock_code
            """

            params = stock_codes + [start_date, end_date]
            result = self.db_manager.fetch_all(query, params)

            # 更新缓存
            current_time = datetime.now()
            for row in result:
                stock_code, avg_volume = row
                cache_key = f"opening_avg_{stock_code}_{days}"
                self.opening_avg_cache[cache_key] = float(avg_volume)
                self.cache_timestamps[cache_key] = current_time.timestamp()

            self.logger.debug(f"批量加载了 {len(result)} 只股票的开盘期数据")

        except Exception as e:
            self.logger.error(f"批量加载开盘期数据失败: {e}")

    def _preload_today_data(self, stock_list: List[str]):
        """预加载当日数据"""
        try:
            self.logger.info("预加载当日数据")
            # 这里可以预加载当日已有的K线数据等
            # 暂时留空，后续可以根据需要实现
            pass

        except Exception as e:
            self.logger.error(f"预加载当日数据失败: {e}")

    def _is_opening_period(self, current_time) -> bool:
        """判断是否为开盘期"""
        opening_start = dt_time(9, 30, 0)
        opening_end = dt_time(9, 45, 0)
        return opening_start <= current_time < opening_end

    def _is_intraday_period(self, current_time) -> bool:
        """判断是否为盘中期"""
        intraday_start = dt_time(9, 45, 0)
        intraday_end = dt_time(15, 0, 0)
        return intraday_start <= current_time < intraday_end

    def _get_from_cache(self, cache_key: str, ttl: int = None) -> Optional[Any]:
        """从缓存获取数据"""
        if not self.enable_cache:
            self.stats['cache_misses'] += 1
            return None

        with self._cache_lock:
            # 检查各个缓存
            for cache_dict in [self.opening_avg_cache, self.intraday_avg_cache,
                             self.realtime_volume_cache, self.kline_cache]:
                if cache_key in cache_dict:
                    cache_time = self.cache_timestamps.get(cache_key, 0)
                    effective_ttl = ttl if ttl is not None else self.cache_ttl

                    if datetime.now().timestamp() - cache_time < effective_ttl:
                        self.stats['cache_hits'] += 1
                        return cache_dict[cache_key]
                    else:
                        # 缓存过期，删除
                        del cache_dict[cache_key]
                        if cache_key in self.cache_timestamps:
                            del self.cache_timestamps[cache_key]

        self.stats['cache_misses'] += 1
        return None

    def _set_cache(self, cache_key: str, data: Any, ttl: int = None):
        """设置缓存"""
        if not self.enable_cache:
            return

        with self._cache_lock:
            # 检查缓存大小限制
            total_cache_size = (len(self.opening_avg_cache) + len(self.intraday_avg_cache) +
                              len(self.realtime_volume_cache) + len(self.kline_cache))

            if total_cache_size >= self.max_cache_size:
                self._cleanup_cache()

            # 根据缓存键类型选择合适的缓存
            if 'opening_avg' in cache_key:
                self.opening_avg_cache[cache_key] = data
            elif 'intraday_avg' in cache_key:
                self.intraday_avg_cache[cache_key] = data
            elif 'current_volume' in cache_key:
                self.realtime_volume_cache[cache_key] = data
            elif 'kline' in cache_key:
                self.kline_cache[cache_key] = data

            self.cache_timestamps[cache_key] = datetime.now().timestamp()

    def _cleanup_cache(self):
        """清理过期缓存"""
        current_time = datetime.now().timestamp()
        expired_keys = []

        for cache_key, timestamp in self.cache_timestamps.items():
            if current_time - timestamp > self.cache_ttl:
                expired_keys.append(cache_key)

        for key in expired_keys:
            # 从所有缓存中删除过期键
            for cache_dict in [self.opening_avg_cache, self.intraday_avg_cache,
                             self.realtime_volume_cache, self.kline_cache]:
                cache_dict.pop(key, None)
            self.cache_timestamps.pop(key, None)

        if expired_keys:
            self.logger.debug(f"清理了 {len(expired_keys)} 个过期缓存项")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._cache_lock:
            total_cache_size = (len(self.opening_avg_cache) + len(self.intraday_avg_cache) +
                              len(self.realtime_volume_cache) + len(self.kline_cache))

            cache_hit_rate = 0.0
            if self.stats['cache_hits'] + self.stats['cache_misses'] > 0:
                cache_hit_rate = self.stats['cache_hits'] / (self.stats['cache_hits'] + self.stats['cache_misses'])

            return {
                'total_cache_size': total_cache_size,
                'opening_avg_cache_size': len(self.opening_avg_cache),
                'intraday_avg_cache_size': len(self.intraday_avg_cache),
                'realtime_volume_cache_size': len(self.realtime_volume_cache),
                'kline_cache_size': len(self.kline_cache),
                'cache_hit_rate': cache_hit_rate,
                'cache_hits': self.stats['cache_hits'],
                'cache_misses': self.stats['cache_misses'],
                'db_queries': self.stats['db_queries'],
                'data_events_processed': self.stats['data_events_processed'],
                'preload_operations': self.stats['preload_operations']
            }

    def clear_cache(self):
        """清空所有缓存"""
        with self._cache_lock:
            self.opening_avg_cache.clear()
            self.intraday_avg_cache.clear()
            self.realtime_volume_cache.clear()
            self.kline_cache.clear()
            self.cache_timestamps.clear()
            self.logger.info("已清空所有缓存")
