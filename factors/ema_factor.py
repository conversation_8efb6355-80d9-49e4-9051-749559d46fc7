#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
EMA因子

检测价格是否接近EMA均线，以及EMA的趋势状态
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime

from .base_factor import BaseFactor, FactorResult
from .common_utils import calculate_ema_indicators, detect_ema_turnaround


class EmaFactor(BaseFactor):
    """EMA因子"""
    
    def __init__(self, periods: List[int] = None, price_threshold: float = 0.005):
        super().__init__("ema_factor", "technical")
        
        self.periods = periods or [12, 62, 144, 169, 377, 576, 676]
        self.price_threshold = price_threshold
        self.required_columns = ['close']
        self.min_data_length = max(self.periods) if self.periods else 676
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算EMA因子"""
        try:
            current_price = float(data['close'].iloc[-1])
            
            # 计算EMA指标
            ema_data = calculate_ema_indicators(data, self.periods)
            
            if not ema_data:
                return None
            
            # 检测价格是否接近任何EMA
            near_emas = []
            
            for ema_name, ema_series in ema_data.items():
                if len(ema_series) > 0:
                    ema_value = float(ema_series.iloc[-1])
                    deviation = abs(current_price - ema_value) / ema_value
                    
                    if deviation <= self.price_threshold:
                        # 提取周期数
                        period = int(ema_name.split('_')[1])
                        
                        near_emas.append({
                            'name': ema_name,
                            'value': ema_value,
                            'period': period,
                            'deviation': deviation,
                            'distance_score': 1 - deviation / self.price_threshold
                        })
            
            if not near_emas:
                return FactorResult(
                    factor_name=self.factor_name,
                    factor_value=0.0,
                    factor_type=self.factor_type,
                    confidence=0.0,
                    signal_strength="NONE",
                    metadata={'ema_data': {k: float(v.iloc[-1]) for k, v in ema_data.items()}}
                )
            
            # 计算因子值
            # 短期EMA权重更高，因为更敏感
            period_weights = {
                12: 1.0, 62: 0.9, 144: 0.8, 169: 0.8,
                377: 0.7, 576: 0.6, 676: 0.5
            }
            
            weighted_scores = []
            for ema in near_emas:
                weight = period_weights.get(ema['period'], 0.5)
                weighted_score = ema['distance_score'] * weight
                weighted_scores.append(weighted_score)
            
            factor_value = max(weighted_scores) if weighted_scores else 0.0
            
            # 计算置信度
            confidence = min(len(near_emas) * 0.25 + factor_value * 0.75, 1.0)
            
            # 确定信号强度
            if factor_value > 0.8 and len(near_emas) >= 2:
                signal_strength = "STRONG"
            elif factor_value > 0.6:
                signal_strength = "MEDIUM"
            elif factor_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"
            
            metadata = {
                'ema_data': {k: float(v.iloc[-1]) for k, v in ema_data.items()},
                'near_emas': near_emas,
                'current_price': current_price,
                'periods': self.periods,
                'price_threshold': self.price_threshold
            }
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"计算EMA因子失败: {e}")
            return None
    
    def get_factor_description(self) -> str:
        return f"""
EMA因子

功能：检测价格是否接近EMA均线
EMA周期：{self.periods}
价格阈值：{self.price_threshold * 100:.1f}%

权重设置：
- 短期EMA权重更高（更敏感）
- EMA_12: 1.0, EMA_62: 0.9, EMA_144/169: 0.8
- EMA_377: 0.7, EMA_576: 0.6, EMA_676: 0.5

因子值：基于价格与EMA的接近程度和周期权重 (0-1)
信号强度：
- STRONG: 因子值>0.8且接近多条EMA
- MEDIUM: 因子值>0.6
- WEAK: 因子值>0.3
- NONE: 因子值≤0.3
"""


class EmaTurnaroundFactor(BaseFactor):
    """EMA拐头因子"""
    
    def __init__(self, target_period: int = 12, lookback_days: int = 5, 
                 price_threshold: float = 0.02):
        super().__init__("ema_turnaround_factor", "technical")
        
        self.target_period = target_period  # 目标EMA周期
        self.lookback_days = lookback_days  # 拐头检测回看天数
        self.price_threshold = price_threshold  # 价格接近阈值
        self.required_columns = ['close']
        self.min_data_length = max(target_period * 2, 100)
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Optional[FactorResult]:
        """计算EMA拐头因子"""
        try:
            current_price = float(data['close'].iloc[-1])
            
            # 计算目标EMA
            ema_data = calculate_ema_indicators(data, [self.target_period])
            ema_key = f'ema_{self.target_period}'
            
            if ema_key not in ema_data:
                return None
            
            ema_series = ema_data[ema_key]
            ema_value = float(ema_series.iloc[-1])
            
            # 检测EMA拐头
            turnaround_result = detect_ema_turnaround(ema_series, self.lookback_days)
            
            # 检测价格是否在EMA附近
            price_deviation = abs(current_price - ema_value) / ema_value
            near_ema = price_deviation <= self.price_threshold
            
            # 计算因子值
            if turnaround_result['confirmed'] and near_ema:
                # 综合拐头强度和价格接近程度
                turnaround_strength = turnaround_result['strength']
                price_proximity = 1 - price_deviation / self.price_threshold
                factor_value = turnaround_strength * 0.7 + price_proximity * 0.3
            elif turnaround_result['confirmed']:
                # 只有拐头，没有接近
                factor_value = turnaround_result['strength'] * 0.5
            elif near_ema:
                # 只是接近，没有拐头
                factor_value = (1 - price_deviation / self.price_threshold) * 0.3
            else:
                factor_value = 0.0
            
            # 计算置信度
            confidence = factor_value
            if turnaround_result['confirmed'] and near_ema:
                confidence = min(confidence + 0.2, 1.0)  # 双重确认加分
            
            # 确定信号强度
            if factor_value > 0.7 and turnaround_result['confirmed'] and near_ema:
                signal_strength = "STRONG"
            elif factor_value > 0.5:
                signal_strength = "MEDIUM"
            elif factor_value > 0.3:
                signal_strength = "WEAK"
            else:
                signal_strength = "NONE"
            
            metadata = {
                'ema_value': ema_value,
                'current_price': current_price,
                'price_deviation': price_deviation,
                'near_ema': near_ema,
                'turnaround_result': turnaround_result,
                'target_period': self.target_period,
                'lookback_days': self.lookback_days,
                'price_threshold': self.price_threshold
            }
            
            return FactorResult(
                factor_name=self.factor_name,
                factor_value=factor_value,
                factor_type=self.factor_type,
                confidence=confidence,
                signal_strength=signal_strength,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"计算EMA拐头因子失败: {e}")
            return None
    
    def get_factor_description(self) -> str:
        return f"""
EMA拐头因子

功能：检测EMA拐头向上且价格在EMA附近的情况
目标EMA：EMA_{self.target_period}
回看天数：{self.lookback_days}
价格阈值：{self.price_threshold * 100:.1f}%

检测条件：
1. EMA斜率转为向上
2. 连续上升天数≥2天
3. 价格在EMA附近震荡

因子值计算：
- 拐头+接近：拐头强度*0.7 + 接近程度*0.3
- 仅拐头：拐头强度*0.5
- 仅接近：接近程度*0.3

信号强度：
- STRONG: 因子值>0.7且双重确认
- MEDIUM: 因子值>0.5
- WEAK: 因子值>0.3
- NONE: 因子值≤0.3
"""
