#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通用因子计算函数

提供可复用的因子计算工具函数
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from utils.logger import get_logger

logger = get_logger('FactorUtils')


def calculate_ema_indicators(df: pd.DataFrame, periods: List[int] = None) -> Dict[str, pd.Series]:
    """
    计算EMA指标
    
    Args:
        df: 包含close列的DataFrame
        periods: EMA周期列表，默认为[12, 62, 144, 169, 377, 576, 676]
        
    Returns:
        EMA指标字典 {ema_period: Series}
    """
    try:
        if periods is None:
            periods = [12, 62, 144, 169, 377, 576, 676]
        
        ema_data = {}
        
        for period in periods:
            if len(df) >= period:
                ema = df['close'].ewm(span=period, adjust=False).mean()
                ema_data[f'ema_{period}'] = ema
        
        return ema_data
        
    except Exception as e:
        logger.error(f"计算EMA指标失败: {e}")
        return {}


def calculate_fibonacci_levels(df: pd.DataFrame, lookback_period: int = 252) -> Dict[str, float]:
    """
    计算斐波那契回撤和扩展位
    
    Args:
        df: 包含high、low列的DataFrame
        lookback_period: 回看周期，默认252天（一年）
        
    Returns:
        斐波那契位字典
    """
    try:
        # 使用指定周期的高低点
        recent_period = min(lookback_period, len(df))
        recent_df = df.iloc[-recent_period:]
        
        high_price = recent_df['high'].max()
        low_price = recent_df['low'].min()
        price_range = high_price - low_price
        
        if price_range <= 0:
            return {}
        
        fib_levels = {
            'fib_23_6': low_price + price_range * 0.236,
            'fib_38_2': low_price + price_range * 0.382,
            'fib_50_0': low_price + price_range * 0.5,
            'fib_61_8': low_price + price_range * 0.618,
            'fib_78_6': low_price + price_range * 0.786,
            'fib_127_2': low_price + price_range * 1.272,
            'fib_138_2': low_price + price_range * 1.382,
            'fib_161_8': low_price + price_range * 1.618,
        }
        
        return fib_levels
        
    except Exception as e:
        logger.error(f"计算斐波那契位失败: {e}")
        return {}


def detect_price_oscillation(prices: np.ndarray, level_value: float, 
                           threshold: float = 0.02) -> Dict[str, float]:
    """
    检测价格在某个水平位附近的震荡强度
    
    Args:
        prices: 价格数组
        level_value: 关键水平位价格
        threshold: 震荡阈值（百分比）
        
    Returns:
        震荡检测结果字典
    """
    try:
        if len(prices) < 3:
            return {'oscillation_score': 0.0, 'within_range_ratio': 0.0, 'direction_score': 0.0}
        
        # 计算价格相对于关键位的偏离度
        deviations = np.abs(prices - level_value) / level_value
        
        # 统计在阈值范围内的价格点数量
        within_range_count = np.sum(deviations <= threshold)
        within_range_ratio = within_range_count / len(prices)
        
        # 计算价格变化的方向性（震荡应该是上下波动）
        price_changes = np.diff(prices)
        if len(price_changes) > 1:
            direction_changes = np.sum(np.diff(np.sign(price_changes)) != 0)
            direction_score = min(direction_changes / max(len(prices) - 2, 1), 1.0)
        else:
            direction_score = 0.0
        
        # 综合评分：范围内比例 * 方向变化频率
        oscillation_score = within_range_ratio * 0.7 + direction_score * 0.3
        
        return {
            'oscillation_score': min(oscillation_score, 1.0),
            'within_range_ratio': within_range_ratio,
            'direction_score': direction_score,
            'within_range_count': within_range_count,
            'total_points': len(prices)
        }
        
    except Exception as e:
        logger.error(f"检测价格震荡失败: {e}")
        return {'oscillation_score': 0.0, 'within_range_ratio': 0.0, 'direction_score': 0.0}


def detect_ema_turnaround(ema_series: pd.Series, lookback_days: int = 5) -> Dict[str, Any]:
    """
    检测EMA拐头向上的情况
    
    Args:
        ema_series: EMA时间序列
        lookback_days: 回看天数
        
    Returns:
        拐头检测结果字典
    """
    try:
        if len(ema_series) < lookback_days + 2:
            return {
                'confirmed': False,
                'strength': 0.0,
                'slope_recent': 0.0,
                'slope_change': 0.0,
                'consecutive_up_days': 0
            }
        
        # 获取最近的EMA值
        recent_ema = ema_series.iloc[-lookback_days:].values
        
        # 计算最近几天的斜率
        x = np.arange(len(recent_ema))
        slope_recent = np.polyfit(x, recent_ema, 1)[0]
        
        # 计算前一段时间的斜率（用于对比）
        if len(ema_series) >= lookback_days * 2:
            previous_ema = ema_series.iloc[-(lookback_days*2):-lookback_days].values
            x_prev = np.arange(len(previous_ema))
            slope_previous = np.polyfit(x_prev, previous_ema, 1)[0]
            slope_change = slope_recent - slope_previous
        else:
            slope_previous = 0
            slope_change = slope_recent
        
        # 计算连续上升天数
        ema_diffs = np.diff(recent_ema)
        consecutive_up_days = 0
        for diff in reversed(ema_diffs):
            if diff > 0:
                consecutive_up_days += 1
            else:
                break
        
        # 计算拐头强度
        strength_factors = []
        
        # 斜率强度 (归一化到0-1)
        slope_strength = min(abs(slope_recent) / (recent_ema[-1] * 0.01), 1.0)
        strength_factors.append(slope_strength * 0.4)
        
        # 斜率改善强度
        if slope_change > 0:
            slope_change_strength = min(abs(slope_change) / (recent_ema[-1] * 0.01), 1.0)
            strength_factors.append(slope_change_strength * 0.3)
        
        # 连续上升天数强度
        consecutive_strength = min(consecutive_up_days / lookback_days, 1.0)
        strength_factors.append(consecutive_strength * 0.3)
        
        total_strength = sum(strength_factors)
        
        # 确认拐头向上
        upward_trend = slope_recent > 0
        slope_improvement = slope_change > 0
        confirmed = (
            upward_trend and 
            slope_improvement and 
            consecutive_up_days >= 2 and
            total_strength > 0.3
        )
        
        return {
            'confirmed': confirmed,
            'strength': total_strength,
            'slope_recent': slope_recent,
            'slope_change': slope_change,
            'consecutive_up_days': consecutive_up_days,
            'slope_strength': slope_strength,
            'slope_change_strength': slope_change_strength if slope_change > 0 else 0,
            'consecutive_strength': consecutive_strength,
            'upward_trend': upward_trend,
            'slope_improvement': slope_improvement
        }
        
    except Exception as e:
        logger.error(f"检测EMA拐头失败: {e}")
        return {
            'confirmed': False,
            'strength': 0.0,
            'slope_recent': 0.0,
            'slope_change': 0.0,
            'consecutive_up_days': 0
        }


def calculate_ema_turnaround_factors(df: pd.DataFrame, 
                                   stock_code: str = "",
                                   start_low_price: Optional[float] = None,
                                   ema_periods: List[int] = None,
                                   oscillation_threshold: float = 0.02,
                                   lookback_days: int = 5) -> Dict[str, Any]:
    """
    通用EMA拐头向上因子计算函数
    
    检测股价在关键技术位附近震荡，然后短周期EMA（特别是EMA_12）拐头向上的情况
    
    Args:
        df: K线数据DataFrame
        stock_code: 股票代码（用于日志）
        start_low_price: 起始低点价格（用于斐波那契计算）
        ema_periods: EMA周期列表
        oscillation_threshold: 震荡阈值
        lookback_days: EMA拐头检测回看天数
        
    Returns:
        EMA拐头向上相关因子字典
    """
    try:
        factors = {}
        
        if len(df) < 50:  # 数据不足
            return factors
        
        # 设置默认参数
        if ema_periods is None:
            ema_periods = [12, 62, 144, 169, 377, 576, 676]
        
        # 1. 计算所有需要的EMA
        ema_values = calculate_ema_indicators(df, ema_periods)
        
        # 2. 计算斐波那契位
        fib_levels = calculate_fibonacci_levels(df)
        if start_low_price and len(df) > 0:
            # 使用指定的起始低点重新计算斐波那契位
            current_high = df['high'].max()
            price_range = current_high - start_low_price
            
            if price_range > 0:
                fib_levels = {
                    'fib_23_6': start_low_price + price_range * 0.236,
                    'fib_38_2': start_low_price + price_range * 0.382,
                    'fib_50_0': start_low_price + price_range * 0.5,
                    'fib_61_8': start_low_price + price_range * 0.618,
                    'fib_78_6': start_low_price + price_range * 0.786,
                    'fib_127_2': start_low_price + price_range * 1.272,
                    'fib_138_2': start_low_price + price_range * 1.382,
                    'fib_161_8': start_low_price + price_range * 1.618,
                }
        
        # 3. 获取最新价格和最近几天的数据
        current_price = float(df['close'].iloc[-1])
        recent_days = min(10, len(df))
        recent_prices = df['close'].iloc[-recent_days:].values
        
        # 4. 检测是否在关键技术位附近震荡
        key_levels = []
        
        # 添加EMA位
        for ema_name, ema_series in ema_values.items():
            if len(ema_series) > 0:
                key_levels.append({
                    'name': ema_name,
                    'value': float(ema_series.iloc[-1]),
                    'type': 'ema'
                })
        
        # 添加斐波那契位
        for fib_name, fib_value in fib_levels.items():
            key_levels.append({
                'name': fib_name,
                'value': fib_value,
                'type': 'fibonacci'
            })
        
        # 5. 检测价格是否在关键位附近震荡
        near_key_levels = []
        
        for level in key_levels:
            level_value = level['value']
            if level_value > 0:
                # 检查当前价格是否在关键位附近
                price_deviation = abs(current_price - level_value) / level_value
                if price_deviation <= oscillation_threshold:
                    # 检查最近是否有震荡
                    oscillation_result = detect_price_oscillation(
                        recent_prices, level_value, oscillation_threshold
                    )
                    if oscillation_result['oscillation_score'] > 0.3:
                        near_key_levels.append({
                            **level,
                            'deviation': price_deviation,
                            **oscillation_result
                        })
        
        # 6. 检测EMA_12拐头向上
        if 'ema_12' in ema_values and len(ema_values['ema_12']) >= 5:
            ema12_turnaround = detect_ema_turnaround(
                ema_values['ema_12'], lookback_days
            )
            factors.update({
                'ema12_turnaround_strength': ema12_turnaround['strength'],
                'ema12_turnaround_confirmed': ema12_turnaround['confirmed'],
                'ema12_slope_recent': ema12_turnaround['slope_recent'],
                'ema12_slope_change': ema12_turnaround['slope_change'],
                'ema12_consecutive_up_days': ema12_turnaround['consecutive_up_days']
            })
        
        # 7. 综合评分：震荡 + 拐头向上
        if near_key_levels and 'ema12_turnaround_confirmed' in factors:
            # 找到最强的震荡位
            best_oscillation = max(near_key_levels, key=lambda x: x['oscillation_score'])
            
            # 计算综合因子
            factors['oscillation_turnaround_score'] = (
                best_oscillation['oscillation_score'] * 0.4 +  # 震荡强度权重40%
                factors['ema12_turnaround_strength'] * 0.6     # 拐头强度权重60%
            )
            
            factors.update({
                'best_oscillation_level': best_oscillation['name'],
                'best_oscillation_value': best_oscillation['value'],
                'best_oscillation_type': best_oscillation['type'],
                'price_near_key_level': True,
                'key_level_count': len(near_key_levels)
            })
            
            # 设置信号强度等级
            score = factors['oscillation_turnaround_score']
            if score > 0.7:
                factors['signal_strength_level'] = 'STRONG'
            elif score > 0.5:
                factors['signal_strength_level'] = 'MEDIUM'
            elif score > 0.3:
                factors['signal_strength_level'] = 'WEAK'
            else:
                factors['signal_strength_level'] = 'NONE'
        else:
            factors.update({
                'oscillation_turnaround_score': 0.0,
                'price_near_key_level': False,
                'key_level_count': len(near_key_levels),
                'signal_strength_level': 'NONE'
            })
        
        # 8. 记录调试信息
        if factors.get('oscillation_turnaround_score', 0) > 0.3 and stock_code:
            logger.debug(
                f"股票 {stock_code} EMA拐头因子: "
                f"综合评分={factors['oscillation_turnaround_score']:.3f}, "
                f"信号强度={factors['signal_strength_level']}, "
                f"关键位数量={factors['key_level_count']}"
            )
        
        return factors
        
    except Exception as e:
        logger.error(f"计算EMA拐头向上因子失败 {stock_code}: {e}")
        return {}
