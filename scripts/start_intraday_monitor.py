#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
盘中选股监控进程启动脚本

功能：
1. 环境检查
2. 配置验证
3. 依赖检查
4. 启动监控进程

使用方法：
python scripts/start_intraday_monitor.py [--test] [--dry-run]

参数：
--test: 运行测试模式
--dry-run: 干运行模式，只检查不启动
"""

import sys
import os
import argparse
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from processes.intraday_stock_monitor import IntradayStockMonitor, create_intraday_signals_table
from tests.test_intraday_monitor import run_all_tests
from utils.logger import get_logger
from config.config_manager import get_config
from data.db_manager import get_db_manager


def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    checks = []
    
    # 检查Python版本
    if sys.version_info >= (3, 8):
        print("✅ Python版本检查通过")
        checks.append(True)
    else:
        print(f"❌ Python版本过低: {sys.version_info}, 需要3.8+")
        checks.append(False)
    
    # 检查必要的模块
    required_modules = [
        'pandas', 'numpy', 'psycopg2', 'toml'
    ]
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ 模块 {module} 可用")
            checks.append(True)
        except ImportError:
            print(f"❌ 模块 {module} 未安装")
            checks.append(False)
    
    return all(checks)


def check_configuration():
    """检查配置文件"""
    print("🔍 检查配置文件...")
    
    try:
        config = get_config()
        
        # 检查基础配置
        if 'intraday_monitor' in config:
            print("✅ 盘中监控配置存在")
            
            monitor_config = config['intraday_monitor']
            required_keys = ['enabled', 'thread_count', 'monitoring_interval']
            
            for key in required_keys:
                if key in monitor_config:
                    print(f"✅ 配置项 {key} 存在")
                else:
                    print(f"❌ 配置项 {key} 缺失")
                    return False
        else:
            print("❌ 盘中监控配置缺失")
            return False
        
        # 检查数据库配置
        if 'database' in config:
            print("✅ 数据库配置存在")
        else:
            print("❌ 数据库配置缺失")
            return False
        
        # 检查飞书配置
        notification_config = config.get('notification', {})
        feishu_config = notification_config.get('feishu', {})
        
        if feishu_config.get('webhook1') and feishu_config.get('secret1'):
            print("✅ 飞书通知配置完整")
        else:
            print("⚠️ 飞书通知配置不完整，通知功能将被禁用")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False


def check_dependencies():
    """检查依赖服务"""
    print("🔍 检查依赖服务...")
    
    checks = []
    
    # 检查数据库连接
    try:
        db_manager = get_db_manager()
        result = db_manager.fetch_one("SELECT 1")
        if result:
            print("✅ 数据库连接正常")
            checks.append(True)
        else:
            print("❌ 数据库连接异常")
            checks.append(False)
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        checks.append(False)
    
    # 检查必要的数据表
    try:
        db_manager = get_db_manager()
        
        # 检查stock_primary_signals表
        result = db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM information_schema.tables "
            "WHERE table_name = 'stock_primary_signals'"
        )
        
        if result and result['count'] > 0:
            print("✅ stock_primary_signals表存在")
            checks.append(True)
        else:
            print("❌ stock_primary_signals表不存在")
            checks.append(False)
        
        # 检查K线数据表
        kline_tables = ['stock_kline_day', 'stock_kline_5min']
        for table in kline_tables:
            result = db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM information_schema.tables "
                "WHERE table_name = %s", (table,)
            )
            
            if result and result['count'] > 0:
                print(f"✅ {table}表存在")
                checks.append(True)
            else:
                print(f"❌ {table}表不存在")
                checks.append(False)
        
    except Exception as e:
        print(f"❌ 数据表检查失败: {e}")
        checks.append(False)
    
    return all(checks)


def check_trading_hours():
    """检查是否在交易时间"""
    now = datetime.now()
    current_time = now.time()
    
    # 交易时间：9:30-11:30, 13:00-15:00
    morning_start = datetime.strptime("09:30:00", "%H:%M:%S").time()
    morning_end = datetime.strptime("11:30:00", "%H:%M:%S").time()
    afternoon_start = datetime.strptime("13:00:00", "%H:%M:%S").time()
    afternoon_end = datetime.strptime("15:00:00", "%H:%M:%S").time()
    
    is_trading_time = (
        (morning_start <= current_time <= morning_end) or
        (afternoon_start <= current_time <= afternoon_end)
    )
    
    if is_trading_time:
        print(f"✅ 当前在交易时间内: {current_time}")
        return True
    else:
        print(f"⚠️ 当前不在交易时间内: {current_time}")
        print("  交易时间: 09:30-11:30, 13:00-15:00")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='盘中选股监控进程启动脚本')
    parser.add_argument('--test', action='store_true', help='运行测试模式')
    parser.add_argument('--dry-run', action='store_true', help='干运行模式，只检查不启动')
    parser.add_argument('--force', action='store_true', help='强制启动，忽略交易时间检查')
    
    args = parser.parse_args()
    
    print("🚀 盘中选股监控进程启动脚本")
    print("=" * 50)
    
    # 1. 环境检查
    if not check_environment():
        print("❌ 环境检查失败，程序退出")
        return 1
    
    # 2. 配置检查
    if not check_configuration():
        print("❌ 配置检查失败，程序退出")
        return 1
    
    # 3. 依赖检查
    if not check_dependencies():
        print("❌ 依赖检查失败，程序退出")
        return 1
    
    # 4. 交易时间检查
    if not args.force and not check_trading_hours():
        print("⚠️ 不在交易时间内，使用 --force 参数强制启动")
        if not args.dry_run:
            return 1
    
    # 5. 测试模式
    if args.test:
        print("\n🧪 运行测试模式...")
        if not run_all_tests():
            print("❌ 测试失败，程序退出")
            return 1
    
    # 6. 干运行模式
    if args.dry_run:
        print("\n✅ 干运行检查完成，所有检查通过")
        return 0
    
    # 7. 创建数据库表
    print("\n🔧 创建数据库表...")
    if not create_intraday_signals_table():
        print("❌ 数据库表创建失败，程序退出")
        return 1
    
    # 8. 启动监控进程
    print("\n🚀 启动盘中选股监控进程...")
    try:
        monitor = IntradayStockMonitor()
        monitor.start()
    except KeyboardInterrupt:
        print("\n🛑 接收到停止信号，正在关闭...")
        if 'monitor' in locals():
            monitor.stop()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1
    
    print("👋 程序已退出")
    return 0


if __name__ == "__main__":
    sys.exit(main())
