[Unit]
Description=XYStock Market Data System
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=xy
Group=xy
WorkingDirectory=/home/<USER>/Program/xystock
Environment=PATH=/home/<USER>/Program/myenv/venv/bin:/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/home/<USER>/Program/xystock
ExecStart=/home/<USER>/Program/myenv/venv/bin/python main.py
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# 防止多开 - 确保只有一个实例运行
PIDFile=/var/run/xystock/xystock.pid
ExecStartPre=/bin/mkdir -p /var/run/xystock
ExecStartPre=/bin/chown xy:xy /var/run/xystock

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 重启策略
Restart=always
RestartSec=10
StartLimitInterval=300
StartLimitBurst=5

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=xystock

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/Program/xystock/logs /var/run/xystock

[Install]
WantedBy=multi-user.target
