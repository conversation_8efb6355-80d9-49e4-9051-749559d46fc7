#!/bin/bash

# XYStock 系统服务安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
if [[ $EUID -eq 0 ]]; then
   log_error "请不要使用root用户运行此脚本"
   exit 1
fi

# 项目路径
PROJECT_DIR="/home/<USER>/Program/xystock"
SERVICE_FILE="$PROJECT_DIR/scripts/xystock.service"
SYSTEMD_DIR="/etc/systemd/system"

log_info "开始安装 XYStock 系统服务..."

# 检查项目目录
if [ ! -d "$PROJECT_DIR" ]; then
    log_error "项目目录不存在: $PROJECT_DIR"
    exit 1
fi

# 检查服务文件
if [ ! -f "$SERVICE_FILE" ]; then
    log_error "服务文件不存在: $SERVICE_FILE"
    exit 1
fi

# 检查虚拟环境
VENV_DIR="/home/<USER>/Program/myenv/venv"
if [ ! -d "$VENV_DIR" ]; then
    log_error "虚拟环境不存在: $VENV_DIR"
    exit 1
fi

# 检查main.py
if [ ! -f "$PROJECT_DIR/main.py" ]; then
    log_error "主程序文件不存在: $PROJECT_DIR/main.py"
    exit 1
fi

# 停止现有服务（如果存在）
if systemctl is-active --quiet xystock; then
    log_info "停止现有的 xystock 服务..."
    sudo systemctl stop xystock
fi

# 复制服务文件
log_info "安装服务文件..."
sudo cp "$SERVICE_FILE" "$SYSTEMD_DIR/"
sudo chown root:root "$SYSTEMD_DIR/xystock.service"
sudo chmod 644 "$SYSTEMD_DIR/xystock.service"

# 创建运行时目录
log_info "创建运行时目录..."
sudo mkdir -p /var/run/xystock
sudo chown xy:xy /var/run/xystock

# 重新加载systemd
log_info "重新加载 systemd..."
sudo systemctl daemon-reload

# 启用服务
log_info "启用 xystock 服务..."
sudo systemctl enable xystock

# 验证安装
log_info "验证服务安装..."
if systemctl is-enabled --quiet xystock; then
    log_info "✅ 服务已成功启用"
else
    log_error "❌ 服务启用失败"
    exit 1
fi

log_info "🎉 XYStock 系统服务安装完成！"
echo
log_info "常用命令："
echo "  启动服务: sudo systemctl start xystock"
echo "  停止服务: sudo systemctl stop xystock"
echo "  重启服务: sudo systemctl restart xystock"
echo "  查看状态: sudo systemctl status xystock"
echo "  查看日志: sudo journalctl -u xystock -f"
echo "  禁用服务: sudo systemctl disable xystock"
echo
log_warn "注意: 服务配置为自动重启，如需永久停止请使用 'sudo systemctl disable xystock'"
