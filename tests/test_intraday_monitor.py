#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
盘中选股监控进程测试脚本

测试功能：
1. 数据库连接测试
2. 数据库连接测试
3. 股票列表获取测试
4. 技术指标计算测试
5. 信号检测测试
6. 飞书通知测试

使用方法：
python tests/test_intraday_monitor.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from processes.intraday_stock_monitor import IntradayStockMonitor, create_intraday_signals_table
from utils.logger import get_logger
from config.config_manager import get_config
from data.db_manager import get_db_manager
import pandas as pd
import time


def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        db_manager = get_db_manager()
        
        # 测试基本查询
        result = db_manager.fetch_one("SELECT 1 as test")
        if result and result.get('test') == 1:
            print("✅ 数据库连接正常")
            return True
        else:
            print("❌ 数据库查询结果异常")
            return False
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


def test_stock_list_retrieval():
    """测试股票列表获取"""
    print("🔍 测试股票列表获取...")
    
    try:
        monitor = IntradayStockMonitor()
        stocks = monitor._get_active_stocks()
        
        if stocks:
            print(f"✅ 成功获取 {len(stocks)} 只活跃股票")
            
            # 显示前5只股票的信息
            for i, stock in enumerate(stocks[:5]):
                print(f"  {i+1}. {stock['stock_code']} - {stock['stock_name']}")
                fib_levels = stock['fibonacci_levels']
                fib_count = sum(1 for v in fib_levels.values() if v is not None)
                print(f"     斐波那契水平数: {fib_count}")
            
            return True
        else:
            print("❌ 未获取到活跃股票")
            return False
            
    except Exception as e:
        print(f"❌ 获取股票列表失败: {e}")
        return False





def test_technical_indicators():
    """测试技术指标计算"""
    print("🔍 测试技术指标计算...")
    
    try:
        monitor = IntradayStockMonitor()
        
        # 创建测试数据
        test_prices = pd.Series([10.0, 10.5, 11.0, 10.8, 11.2, 11.5, 11.3, 11.8, 12.0, 11.9])
        
        # 测试EMA计算
        ema_5 = monitor._calculate_ema(test_prices, 5)
        if ema_5:
            print(f"✅ EMA5计算成功: {ema_5:.3f}")
        else:
            print("❌ EMA计算失败")
            return False
        
        # 测试布林线计算
        bb_values = monitor._calculate_bollinger_bands(test_prices, period=5)
        if bb_values:
            print(f"✅ 布林线计算成功: 上轨={bb_values.get('bb_upper', 0):.3f}")
        else:
            print("❌ 布林线计算失败")
            return False
        
        # 测试价格接近度检测
        is_near = monitor._is_price_near_level(11.9, 12.0, 0.01)  # 1%阈值
        if is_near:
            print("✅ 价格接近度检测正常")
        else:
            print("❌ 价格接近度检测异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 技术指标计算测试失败: {e}")
        return False


def test_signal_detection():
    """测试信号检测"""
    print("🔍 测试信号检测...")
    
    try:
        monitor = IntradayStockMonitor()
        
        # 创建测试股票信息
        test_stock = {
            'stock_code': '000001',
            'stock_name': '平安银行',
            'fibonacci_levels': {
                'fib_23_6': 12.36,
                'fib_38_2': 12.82,
                'fib_50_0': 13.00,
                'fib_61_8': 13.18,
                'fib_78_6': 13.56
            }
        }
        
        # 测试斐波那契信号检测
        current_price = 12.37  # 接近fib_23_6
        fib_signals = monitor._detect_fibonacci_signals(test_stock, current_price)
        
        if fib_signals:
            print(f"✅ 检测到 {len(fib_signals)} 个斐波那契信号")
            for signal in fib_signals:
                print(f"  信号: {signal['indicator_name']} = {signal['indicator_value']}, "
                      f"偏离度: {signal['deviation']:.2%}")
        else:
            print("ℹ️ 未检测到斐波那契信号（正常情况）")
        
        return True
        
    except Exception as e:
        print(f"❌ 信号检测测试失败: {e}")
        return False


def test_feishu_notification():
    """测试飞书通知"""
    print("🔍 测试飞书通知...")
    
    try:
        monitor = IntradayStockMonitor()
        
        if monitor._init_feishu_notifier():
            print("✅ 飞书通知器初始化成功")
            
            # 创建测试信号
            test_signal = {
                'stock_code': '000001',
                'stock_name': '平安银行',
                'signal_type': 'fibonacci',
                'indicator_name': 'fib_50_0',
                'current_price': 13.02,
                'indicator_value': 13.00,
                'deviation': 0.0015,
                'signal_time': pd.Timestamp.now()
            }
            
            # 构建测试卡片
            card = monitor._build_signal_card(test_signal, ['斐波那契fib_61_8'])
            
            if card:
                print("✅ 信号卡片构建成功")
                print(f"  卡片标题: {card['header']['title']['content']}")
                
                # 可选：发送测试通知（取消注释以实际发送）
                # success = monitor._send_feishu_notification(card, '000001')
                # if success:
                #     print("✅ 飞书通知发送成功")
                # else:
                #     print("❌ 飞书通知发送失败")
                
                return True
            else:
                print("❌ 信号卡片构建失败")
                return False
        else:
            print("⚠️ 飞书通知器初始化失败（可能是配置问题）")
            return True  # 不影响其他测试
            
    except Exception as e:
        print(f"❌ 飞书通知测试失败: {e}")
        return False


def test_database_table_creation():
    """测试数据库表创建"""
    print("🔍 测试数据库表创建...")
    
    try:
        success = create_intraday_signals_table()
        if success:
            print("✅ 数据库表创建成功")
            return True
        else:
            print("❌ 数据库表创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库表创建异常: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始盘中选股监控进程测试...")
    print("=" * 50)
    
    tests = [
        ("数据库连接", test_database_connection),
        ("数据库表创建", test_database_table_creation),
        ("股票列表获取", test_stock_list_retrieval),

        ("技术指标计算", test_technical_indicators),
        ("信号检测", test_signal_detection),
        ("飞书通知", test_feishu_notification),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        try:
            if test_func():
                passed += 1
            time.sleep(1)  # 短暂延迟
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！盘中选股监控进程准备就绪")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置和环境")
        return False


if __name__ == "__main__":
    run_all_tests()
