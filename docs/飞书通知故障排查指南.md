# 飞书通知故障排查指南

## 🚨 常见问题快速诊断

### 问题1：签名验证失败 (错误代码19021)
```
错误信息: "sign match fail or timestamp is not within one hour from current time"
```

#### 🔍 诊断步骤
1. **检查签名算法**
   ```python
   # 正确的签名算法
   key = f'{timestamp}\n{secret}'
   msg = ""
   hmac_code = hmac.new(key.encode(), msg.encode(), hashlib.sha256).digest()
   sign = base64.b64encode(hmac_code).decode()
   ```

2. **检查时间同步**
   ```bash
   # 检查系统时间
   date
   # 同步时间（如果需要）
   sudo ntpdate -s time.nist.gov
   ```

3. **验证secret是否正确**
   ```python
   # 检查配置文件中的secret
   print(f"当前secret: {secret}")
   # 确保与飞书机器人设置中的secret一致
   ```

#### ✅ 解决方案
- 使用正确的签名算法（见上面代码）
- 确保系统时间准确
- 验证secret配置正确

### 问题2：Webhook地址无效 (错误代码19001)
```
错误信息: "param invalid: incoming webhook access token invalid"
```

#### 🔍 诊断步骤
1. **检查URL格式**
   ```python
   # 正确格式
   webhook_url = "https://open.feishu.cn/open-apis/bot/v2/hook/xxxxxxxxx"
   
   # 错误格式
   webhook_url = "https://open.feishu.cn/open-apis/bot/hook/xxxxxxxxx"  # 缺少v2
   ```

2. **验证机器人状态**
   - 检查机器人是否还在群中
   - 确认机器人未被删除或禁用

#### ✅ 解决方案
- 使用正确的V2格式URL
- 重新创建机器人（如果已被删除）

### 问题3：关键词校验失败 (错误代码19024)
```
错误信息: "Key Words Not Found"
```

#### 🔍 诊断步骤
1. **检查机器人设置**
   - 查看是否设置了关键词校验
   - 确认设置的关键词

2. **检查消息内容**
   ```python
   # 确保消息包含设置的关键词
   keyword = "QuantFM"  # 你设置的关键词
   message = f"{keyword} - 成交量激增信号"
   ```

#### ✅ 解决方案
- 在消息中包含设置的关键词
- 或者关闭关键词校验

### 问题4：IP校验失败 (错误代码19022)
```
错误信息: "Ip Not Allowed"
```

#### 🔍 诊断步骤
1. **检查当前IP**
   ```bash
   curl ifconfig.me
   ```

2. **检查机器人IP白名单设置**

#### ✅ 解决方案
- 将当前IP添加到白名单
- 或者关闭IP校验

## 🛠️ 调试工具

### 1. 签名验证工具
```python
def debug_signature(timestamp, secret):
    """调试签名生成过程"""
    print(f"=== 签名调试 ===")
    print(f"timestamp: {timestamp}")
    print(f"secret: {secret}")
    
    key = f'{timestamp}\n{secret}'
    print(f"key: {repr(key)}")
    
    msg = ""
    print(f"msg: {repr(msg)}")
    
    import hmac, hashlib, base64
    hmac_code = hmac.new(key.encode(), msg.encode(), hashlib.sha256).digest()
    print(f"hmac_code length: {len(hmac_code)}")
    
    sign = base64.b64encode(hmac_code).decode()
    print(f"final sign: {sign}")
    
    return sign

# 使用示例
timestamp = str(int(time.time()))
secret = "your_secret"
sign = debug_signature(timestamp, secret)
```

### 2. 网络连接测试
```python
def test_network_connectivity(webhook_url):
    """测试网络连接"""
    import requests
    from urllib.parse import urlparse
    
    parsed = urlparse(webhook_url)
    host = parsed.netloc
    
    print(f"=== 网络连接测试 ===")
    print(f"目标主机: {host}")
    
    try:
        # 测试DNS解析
        import socket
        ip = socket.gethostbyname(host.split(':')[0])
        print(f"DNS解析: {host} -> {ip}")
        
        # 测试HTTP连接
        response = requests.get(f"https://{host}", timeout=5)
        print(f"HTTP连接: 状态码 {response.status_code}")
        
        return True
    except Exception as e:
        print(f"连接失败: {e}")
        return False
```

### 3. 完整诊断脚本
```python
def full_diagnosis(webhook_url, secret):
    """完整的飞书通知诊断"""
    print("🔍 开始飞书通知完整诊断...")
    
    # 1. 基础配置检查
    print("\n1. 基础配置检查")
    print(f"   Webhook URL: {webhook_url}")
    print(f"   Secret: {secret[:10]}...{secret[-5:]}")
    print(f"   URL格式: {'✅ 正确' if '/v2/hook/' in webhook_url else '❌ 错误'}")
    
    # 2. 网络连接测试
    print("\n2. 网络连接测试")
    network_ok = test_network_connectivity(webhook_url)
    print(f"   网络连接: {'✅ 正常' if network_ok else '❌ 异常'}")
    
    # 3. 签名算法测试
    print("\n3. 签名算法测试")
    timestamp = str(int(time.time()))
    sign = debug_signature(timestamp, secret)
    
    # 4. 实际发送测试
    print("\n4. 实际发送测试")
    test_data = {
        "timestamp": timestamp,
        "sign": sign,
        "msg_type": "text",
        "content": {"text": "QuantFM诊断测试消息"}
    }
    
    try:
        import requests
        response = requests.post(
            webhook_url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10,
            proxies={'http': '', 'https': ''}
        )
        
        print(f"   HTTP状态码: {response.status_code}")
        result = response.json()
        print(f"   响应内容: {result}")
        
        if result.get('code') == 0:
            print("   ✅ 发送成功")
            return True
        else:
            print(f"   ❌ 发送失败: {result.get('msg', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"   ❌ 发送异常: {e}")
        return False

# 使用示例
webhook_url = "your_webhook_url"
secret = "your_secret"
full_diagnosis(webhook_url, secret)
```

## 📋 故障排查清单

### 基础检查 ✅
- [ ] Webhook URL格式正确（包含/v2/hook/）
- [ ] Secret配置正确
- [ ] 机器人仍在群中且未被删除
- [ ] 系统时间准确（与标准时间差小于1小时）

### 网络检查 ✅
- [ ] 可以访问open.feishu.cn
- [ ] 没有防火墙阻挡
- [ ] 代理设置正确（建议绕过代理）
- [ ] DNS解析正常

### 代码检查 ✅
- [ ] 使用正确的签名算法
- [ ] 时间戳格式正确（秒级字符串）
- [ ] 请求格式符合飞书API规范
- [ ] 错误处理完善

### 配置检查 ✅
- [ ] 关键词校验设置（如果启用）
- [ ] IP白名单设置（如果启用）
- [ ] 机器人权限设置
- [ ] 群聊权限设置

## 🔧 常用修复命令

### 同步系统时间
```bash
# Ubuntu/Debian
sudo ntpdate -s time.nist.gov

# CentOS/RHEL
sudo chrony sources -v

# 检查时区
timedatectl status
```

### 测试网络连接
```bash
# 测试DNS解析
nslookup open.feishu.cn

# 测试HTTP连接
curl -I https://open.feishu.cn

# 测试具体的webhook（不会发送消息）
curl -I your_webhook_url
```

### 查看系统日志
```bash
# 查看应用日志
tail -f logs/volume_surge_processor.log

# 查看系统网络日志
sudo journalctl -u networking -f
```

## 📞 获取帮助

### 日志分析
当遇到问题时，请提供以下信息：
1. 完整的错误日志
2. 使用的webhook URL格式（隐藏敏感部分）
3. 系统时间和时区信息
4. 网络环境描述

### 联系支持
- 📧 技术支持：<EMAIL>
- 📖 文档中心：docs.quantfm.com
- 🐛 问题反馈：github.com/quantfm/issues

---

**📅 更新时间**：2025-08-19  
**🔧 适用版本**：QuantFM v2.0+  
**📖 相关文档**：飞书通知功能详细说明.md, 飞书通知配置模板.md
