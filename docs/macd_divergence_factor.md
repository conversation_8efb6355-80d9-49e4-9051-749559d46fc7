# MACD背离因子使用说明

## 📋 概述

MACD背离因子是基于MACD指标与价格的背离分析，用于识别潜在的趋势反转信号的技术分析因子。该因子支持多种输出模式和使用场景，通过配置文件统一管理参数。

## 🎯 核心特性

### 1. 多输出模式支持
- **布尔信号模式** (`boolean`): 输出True/False信号，适用于策略信号生成
- **连续数值模式** (`continuous`): 输出连续数值，适用于机器学习训练

### 2. 配置文件驱动
- 所有参数通过 `config/factor_params.yaml` 统一管理
- 支持多场景配置：开发、生产、Qlib训练
- 参数优化空间预定义

### 3. 高性能计算
- 向量化计算优化
- 可选的缓存机制
- 平滑和衰减处理

### 4. Qlib兼容性
- 支持Qlib参数优化
- 机器学习训练就绪
- 标准化输出格式

## 🚀 快速开始

### 基础使用

```python
from factors import MACDDivergenceFactor

# 创建因子实例（使用默认生产配置）
factor = MACDDivergenceFactor()

# 计算因子
df_with_factors = factor.calculate(df)

# 查看生成的因子
print(factor.get_factor_names())
```

### 指定使用场景

```python
# 开发模式（布尔信号）
dev_factor = MACDDivergenceFactor(scenario="development")

# 生产模式（连续数值）
prod_factor = MACDDivergenceFactor(scenario="production")

# Qlib训练模式（ML优化）
qlib_factor = MACDDivergenceFactor(scenario="qlib_training")
```

### 自定义参数

```python
# 方式1：直接传参
factor = MACDDivergenceFactor(
    scenario="production",
    fastperiod=8,
    slowperiod=20,
    output_mode="boolean"
)

# 方式2：使用配置对象
from factors import MACDDivergenceConfig

config = MACDDivergenceConfig(
    fastperiod=8,
    slowperiod=20,
    output_mode="boolean",
    normalize_output=False
)
factor = MACDDivergenceFactor(config=config)
```

## 📊 输出因子说明

### 布尔信号模式 (`output_mode="boolean"`)

| 因子名称 | 含义 | 信号类型 |
|----------|------|----------|
| `macd_upup_high` | 价格新高且MACD新高 | 强势上涨趋势延续 |
| `macd_upup_low` | 价格新低但MACD新高 | **底背离** - 看涨信号 |
| `macd_downdown_high` | 价格新高但MACD新低 | **顶背离** - 看跌信号 |
| `macd_downdown_low` | 价格新低且MACD新低 | 弱势下跌趋势延续 |

### 连续数值模式 (`output_mode="continuous"`)

| 因子名称 | 含义 | 数值范围 |
|----------|------|----------|
| `macd_bullish_divergence` | 看涨背离强度 | 0.0+ |
| `macd_bearish_divergence` | 看跌背离强度 | 0.0+ |
| `macd_trend_strength` | 趋势强度 | -∞ ~ +∞ |
| `macd_momentum_score` | 动量评分 | -∞ ~ +∞ |
| `macd_divergence_composite` | 组合背离信号 | -∞ ~ +∞ |
| `macd_signal_confidence` | 信号确认度 | 0.0+ |
| `macd_relative_strength` | 相对强度 | 0.0 ~ 1.0 |

### 标准化因子 (`normalize_output=true`)

当启用标准化输出时，每个因子会额外生成一个 `_normalized` 后缀的标准化版本：
- `macd_trend_strength_normalized`
- `macd_divergence_composite_normalized`
- 等等...

## ⚙️ 配置参数详解

### MACD参数

```yaml
macd_params:
  fastperiod: 12        # MACD快线周期 (建议范围: 8-20)
  slowperiod: 26        # MACD慢线周期 (建议范围: 20-35)
  signalperiod: 9       # MACD信号线周期 (建议范围: 5-15)
```

### 背离检测参数

```yaml
divergence_params:
  window: 5             # 局部极值识别窗口 (建议范围: 3-10)
  min_bars_between: 10  # 两个极值点最小间隔 (建议范围: 5-20)
  min_price_change: 0.02 # 最小价格变化幅度 (建议范围: 0.01-0.05)
```

### 输出控制参数

```yaml
output_params:
  output_mode: "continuous"  # 输出模式: "boolean" | "continuous"
  normalize_output: true     # 是否标准化输出
  feature_engineering: true # 是否启用特征工程
```

### 性能优化参数

```yaml
performance_params:
  smoothing_window: 3       # 信号平滑窗口 (建议范围: 1-5)
  strength_decay: 0.95      # 信号强度衰减系数 (建议范围: 0.8-0.99)
  enable_cache: true        # 是否启用缓存
  vectorized_compute: true  # 是否使用向量化计算
```

## 🎭 使用场景

### 1. 策略信号生成

```python
# 使用布尔信号模式
factor = MACDDivergenceFactor(scenario="development")
df_signals = factor.calculate(df)

# 生成交易信号
buy_signals = df_signals['macd_upup_low']  # 底背离买入
sell_signals = df_signals['macd_downdown_high']  # 顶背离卖出

print(f"买入信号数量: {buy_signals.sum()}")
print(f"卖出信号数量: {sell_signals.sum()}")
```

### 2. 机器学习特征

```python
# 使用连续数值模式
factor = MACDDivergenceFactor(scenario="qlib_training")
df_features = factor.calculate(df)

# 获取ML特征
feature_names = factor.get_factor_names()
X = df_features[feature_names].fillna(0)

# 生成标签（例如：未来5期收益率）
y = df_features['close'].pct_change().shift(-5)

print(f"特征数量: {len(feature_names)}")
print(f"样本数量: {len(X)}")
```

### 3. 因子分析

```python
# 分析因子有效性
factor = MACDDivergenceFactor(scenario="production")
df_analysis = factor.calculate(df)

# 计算因子与未来收益的相关性
future_return = df_analysis['close'].pct_change().shift(-5)
correlations = {}

for factor_name in factor.get_factor_names():
    corr = df_analysis[factor_name].corr(future_return)
    correlations[factor_name] = corr

# 显示相关性排序
sorted_corr = sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True)
for name, corr in sorted_corr:
    print(f"{name}: {corr:.4f}")
```

### 4. 参数优化

```python
# 获取参数空间
param_space = MACDDivergenceFactor.get_param_space()
print("可优化参数:", list(param_space.keys()))

# 创建优化实例
best_params = {
    'fastperiod': 8,
    'slowperiod': 20,
    'signalperiod': 5,
    'window': 7
}

optimized_factor = MACDDivergenceFactor.create_from_params(
    best_params, 
    scenario="production"
)
```

## 🔧 高级用法

### 自定义配置文件

```python
# 使用自定义配置文件
factor = MACDDivergenceFactor()
config = factor.config
config.fastperiod = 10
config.output_mode = "boolean"

# 重新计算
df_custom = factor.calculate(df)
```

### 批量处理

```python
# 批量处理多只股票
stocks_data = {
    '000001': df1,
    '000002': df2,
    '600000': df3
}

factor = MACDDivergenceFactor(scenario="production")
results = {}

for stock_code, stock_df in stocks_data.items():
    try:
        results[stock_code] = factor.calculate(stock_df)
        print(f"✅ {stock_code}: 处理成功")
    except Exception as e:
        print(f"❌ {stock_code}: 处理失败 - {e}")
```

### 实时计算

```python
# 实时数据流处理
factor = MACDDivergenceFactor(
    scenario="production",
    enable_cache=True  # 启用缓存提高性能
)

def process_realtime_data(new_data):
    """处理实时数据"""
    # 合并新数据到历史数据
    updated_df = pd.concat([historical_df, new_data])
    
    # 只计算最新部分的因子
    latest_factors = factor.calculate(updated_df.tail(100))
    
    # 返回最新的信号
    return latest_factors.iloc[-1]
```

## 📈 性能优化建议

### 1. 数据量优化
- 对于大数据集，建议分批处理
- 使用 `enable_cache=True` 启用缓存
- 适当调整 `window` 和 `min_bars_between` 参数

### 2. 计算优化
- 生产环境使用 `vectorized_compute=True`
- 根据需要调整 `smoothing_window`
- 合理设置 `strength_decay` 参数

### 3. 内存优化
- 及时清理不需要的中间变量
- 使用 `normalize_output=False` 减少输出因子数量
- 关闭不需要的 `feature_engineering`

## ⚠️ 注意事项

### 1. 数据要求
- 最少需要 `slowperiod + signalperiod + window * 2` 条数据
- 数据必须包含 `close` 列
- 建议数据按时间正序排列

### 2. 参数设置
- `fastperiod` 必须小于 `slowperiod`
- `window` 不宜过大，避免错过短期信号
- `min_price_change` 根据标的波动性调整

### 3. 信号解释
- 布尔信号适合策略触发
- 连续数值适合ML训练
- 背离信号需要结合其他指标确认

## 🔍 故障排除

### 常见问题

**Q: 计算结果全为0或False？**
A: 检查数据长度是否足够，参数设置是否合理

**Q: 配置文件加载失败？**
A: 确认 `config/factor_params.yaml` 文件存在且格式正确

**Q: 性能较慢？**
A: 启用缓存和向量化计算，减少数据量

**Q: 信号过多或过少？**
A: 调整 `min_price_change` 和 `min_bars_between` 参数

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

factor = MACDDivergenceFactor(scenario="development")
# 查看详细日志信息
```

## 📚 相关文档

- [因子框架说明](./factor_framework.md)
- [配置文件格式](./config_format.md)
- [Qlib集成指南](./qlib_integration.md)
- [性能优化指南](./performance_optimization.md)

## 🤝 贡献

如有问题或建议，请提交Issue或Pull Request。

---

**作者**: QuantFM Team  
**创建时间**: 2025-08-23  
**版本**: 1.0.0
