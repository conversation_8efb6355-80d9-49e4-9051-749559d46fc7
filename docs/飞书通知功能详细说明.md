# 飞书通知功能详细说明

## 📋 概述

本文档详细说明了QuantFM系统中飞书通知功能的实现原理、关键技术点和使用方法。

## 🔧 核心技术要点

### 1. 签名算法（最关键）

#### ✅ 正确的签名算法
```python
def _generate_sign(self, timestamp: str) -> str:
    """
    飞书签名生成 - 正确算法
    
    关键发现：
    - 密钥(key) = timestamp + "\n" + secret
    - 消息(msg) = "" (空字符串)
    - 算法：hmac.new(key, msg, sha256) -> base64编码
    """
    # 步骤1：构建密钥
    key = f'{timestamp}\n{self.secret}'
    key_enc = key.encode('utf-8')
    
    # 步骤2：待签名消息（空字符串）
    msg = ""
    msg_enc = msg.encode('utf-8')
    
    # 步骤3：HMAC-SHA256加密
    hmac_code = hmac.new(key_enc, msg_enc, digestmod=hashlib.sha256).digest()
    
    # 步骤4：Base64编码
    sign = base64.b64encode(hmac_code).decode('utf-8')
    return sign
```

#### ❌ 错误的签名算法（之前的实现）
```python
# 错误：把secret作为密钥，把timestamp+secret作为消息
string_to_sign = timestamp + "\n" + secret
hmac_code = hmac.new(secret.encode('utf-8'), string_to_sign.encode('utf-8'), sha256)
```

### 2. 时间戳要求

#### 时间戳格式
- **格式**：秒级Unix时间戳（字符串格式）
- **生成**：`str(int(time.time()))`
- **要求**：与飞书服务器时间误差不超过1小时

#### 时间同步检查
```python
# 检查系统时间与服务器时间的差异
system_timestamp = int(time.time())
# 如果时间差超过3600秒（1小时），签名验证会失败
```

### 3. 请求格式

#### 带签名的请求格式
```python
data = {
    "timestamp": timestamp,      # 秒级时间戳（字符串）
    "sign": sign,               # Base64编码的签名
    "msg_type": "text",         # 消息类型
    "content": {
        "text": "消息内容"       # 实际消息内容
    }
}
```

#### HTTP请求配置
```python
response = requests.post(
    webhook_url,
    json=data,
    headers={'Content-Type': 'application/json'},
    timeout=10,
    proxies={'http': '', 'https': ''}  # 绕过代理
)
```

## 🔗 配置管理

### 配置文件结构（config/main.toml）
```toml
[notification.feishu]
enabled = true
webhook1 = "https://open.feishu.cn/open-apis/bot/v2/hook/xxx"
secret1 = "your_secret_key"
webhook2 = "https://open.feishu.cn/open-apis/bot/v2/hook/yyy"
secret2 = "your_secret_key2"
```

### 配置读取逻辑
```python
def _init_feishu_notifier(self) -> Optional[FeishuNotifier]:
    """从配置文件读取飞书配置"""
    config = get_config()
    feishu_config = config.get('notification', {}).get('feishu', {})
    
    # 优先使用webhook1和secret1
    webhook_url = feishu_config.get('webhook1', '')
    secret = feishu_config.get('secret1', '')
    
    if webhook_url and secret:
        return FeishuNotifier(webhook_url, secret, self.logger, use_signature=True)
    return None
```

## 🚀 核心类设计

### FeishuNotifier类结构
```python
class FeishuNotifier:
    def __init__(self, webhook_url: str, secret: str, logger, use_signature: bool = True):
        self.webhook_url = webhook_url
        self.secret = secret
        self.logger = logger
        self.use_signature = use_signature  # 控制是否使用签名
        self.enabled = bool(webhook_url and (secret if use_signature else True))
    
    def _generate_sign(self, timestamp: str) -> str:
        """生成签名"""
        
    def _send_text_message(self, text: str) -> bool:
        """发送文本消息"""
        
    def _send_message(self, message: Dict) -> bool:
        """发送卡片消息"""
        
    def test_connection(self) -> bool:
        """测试连接"""
```

## 📊 消息类型支持

### 1. 文本消息
```python
data = {
    "timestamp": timestamp,
    "sign": sign,
    "msg_type": "text",
    "content": {
        "text": "这是一条文本消息"
    }
}
```

### 2. 卡片消息
```python
data = {
    "timestamp": timestamp,
    "sign": sign,
    "msg_type": "interactive",
    "card": {
        "header": {
            "template": "blue",
            "title": {
                "content": "标题",
                "tag": "plain_text"
            }
        },
        "elements": [
            {
                "tag": "div",
                "text": {
                    "content": "内容",
                    "tag": "lark_md"
                }
            }
        ]
    }
}
```

## ⚠️ 常见错误和解决方案

### 错误代码对照表
| 错误代码 | 错误信息 | 原因 | 解决方案 |
|---------|---------|------|---------|
| 19021 | sign match fail or timestamp is not within one hour from current time | 签名错误或时间戳超时 | 检查签名算法和系统时间 |
| 19001 | param invalid: incoming webhook access token invalid | webhook地址无效 | 检查webhook URL |
| 19024 | Key Words Not Found | 关键词校验失败 | 消息中包含设置的关键词 |
| 19022 | Ip Not Allowed | IP校验失败 | 检查IP白名单设置 |

### 调试技巧
1. **启用详细日志**：查看签名生成过程
2. **时间同步检查**：确保系统时间正确
3. **分步测试**：先测试无签名，再测试有签名
4. **网络检查**：确保可以访问飞书API

## 🔍 故障排查流程

### 1. 基础检查
- [ ] webhook URL是否正确
- [ ] secret是否正确
- [ ] 网络是否可达

### 2. 签名验证
- [ ] 时间戳格式是否正确（秒级字符串）
- [ ] 签名算法是否使用正确的密钥和消息
- [ ] Base64编码是否正确

### 3. 时间同步
- [ ] 系统时间是否准确
- [ ] 与服务器时间差是否在1小时内

### 4. 网络配置
- [ ] 是否绕过了代理
- [ ] 请求头是否正确
- [ ] 超时设置是否合理

## 📈 性能优化

### 频率控制
```python
# 通知频率控制
self.notify_interval = 300  # 同一股票5分钟内只通知一次
self.max_notifications_per_minute = 20  # 每分钟最大通知数
```

### 错误重试
```python
# 实现指数退避重试机制
for attempt in range(max_retries):
    if self._send_message(message):
        return True
    time.sleep(2 ** attempt)  # 指数退避
```

## 🎯 最佳实践

1. **安全性**：
   - 不要在日志中输出完整的secret
   - 使用HTTPS请求
   - 定期更换secret

2. **可靠性**：
   - 实现重试机制
   - 添加超时控制
   - 监控发送成功率

3. **性能**：
   - 控制发送频率
   - 使用连接池
   - 异步发送（可选）

4. **维护性**：
   - 详细的日志记录
   - 清晰的错误处理
   - 完善的测试用例

## 📝 使用示例

### 基本使用
```python
# 初始化
notifier = FeishuNotifier(webhook_url, secret, logger)

# 发送文本消息
notifier._send_text_message("测试消息")

# 测试连接
if notifier.test_connection():
    print("连接正常")
```

### 在成交量监控中的使用
```python
# 发送成交量激增信号
def send_volume_surge_signal(self, signal_data):
    if self.feishu_notifier and self.feishu_notifier.enabled:
        message = self._build_signal_message(signal_data)
        return self.feishu_notifier._send_message(message)
    return False
```

## 🔬 深度技术分析

### 签名算法深度解析

#### 为什么之前的算法错误？
```python
# 错误理解：以为是对"timestamp + secret"进行签名
# 实际上：飞书的算法是用"timestamp + secret"作为密钥，对空字符串进行签名

# 错误实现
hmac.new(secret.encode(), (timestamp + "\n" + secret).encode(), sha256)

# 正确实现
hmac.new((timestamp + "\n" + secret).encode(), "".encode(), sha256)
```

#### 算法验证步骤
1. **时间戳生成**：`timestamp = str(int(time.time()))`
2. **密钥构建**：`key = timestamp + "\n" + secret`
3. **消息内容**：`msg = ""` (空字符串)
4. **HMAC计算**：`hmac.new(key.encode(), msg.encode(), sha256)`
5. **Base64编码**：`base64.b64encode(hmac_result).decode()`

### 网络层优化

#### 代理处理
```python
# 确保绕过代理，直接访问飞书API
proxies = {'http': '', 'https': ''}
response = requests.post(url, json=data, proxies=proxies)
```

#### 连接池优化
```python
# 可选：使用会话对象提高性能
session = requests.Session()
session.proxies = {'http': '', 'https': ''}
response = session.post(url, json=data)
```

### 错误处理机制

#### 分层错误处理
```python
def _send_with_retry(self, data, max_retries=3):
    """带重试的发送机制"""
    for attempt in range(max_retries):
        try:
            response = requests.post(self.webhook_url, json=data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    return True
                else:
                    self.logger.warning(f"飞书API返回错误: {result}")
            else:
                self.logger.error(f"HTTP错误: {response.status_code}")
        except requests.exceptions.Timeout:
            self.logger.warning(f"请求超时，重试 {attempt + 1}/{max_retries}")
        except requests.exceptions.ConnectionError:
            self.logger.warning(f"连接错误，重试 {attempt + 1}/{max_retries}")
        except Exception as e:
            self.logger.error(f"未知错误: {e}")

        if attempt < max_retries - 1:
            time.sleep(2 ** attempt)  # 指数退避

    return False
```

## 🧪 测试用例

### 单元测试示例
```python
import unittest
from unittest.mock import Mock, patch

class TestFeishuNotifier(unittest.TestCase):
    def setUp(self):
        self.webhook_url = "https://test.webhook.url"
        self.secret = "test_secret"
        self.logger = Mock()
        self.notifier = FeishuNotifier(self.webhook_url, self.secret, self.logger)

    def test_generate_sign(self):
        """测试签名生成"""
        timestamp = "1234567890"
        expected_key = "1234567890\ntest_secret"

        # 模拟预期的签名结果
        with patch('hmac.new') as mock_hmac:
            with patch('base64.b64encode') as mock_b64:
                mock_hmac.return_value.digest.return_value = b'test_digest'
                mock_b64.return_value.decode.return_value = 'test_signature'

                result = self.notifier._generate_sign(timestamp)

                # 验证调用参数
                mock_hmac.assert_called_once()
                args = mock_hmac.call_args[0]
                self.assertEqual(args[0], expected_key.encode('utf-8'))
                self.assertEqual(args[1], "".encode('utf-8'))

    @patch('requests.post')
    def test_send_text_message_success(self, mock_post):
        """测试文本消息发送成功"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'code': 0, 'msg': 'success'}
        mock_post.return_value = mock_response

        result = self.notifier._send_text_message("测试消息")

        self.assertTrue(result)
        mock_post.assert_called_once()
```

### 集成测试
```python
def test_integration():
    """集成测试 - 需要真实的webhook配置"""
    # 注意：这需要真实的webhook URL和secret
    webhook_url = "your_real_webhook_url"
    secret = "your_real_secret"

    notifier = FeishuNotifier(webhook_url, secret, logger)

    # 测试连接
    assert notifier.test_connection(), "连接测试失败"

    # 测试文本消息
    assert notifier._send_text_message("集成测试消息"), "文本消息发送失败"

    print("✅ 集成测试通过")
```

## 📊 监控和指标

### 关键指标
- **发送成功率**：成功发送的消息数 / 总发送尝试数
- **平均响应时间**：飞书API的平均响应时间
- **错误率分布**：各种错误代码的出现频率
- **重试次数**：平均重试次数

### 监控实现
```python
class FeishuMetrics:
    def __init__(self):
        self.total_attempts = 0
        self.successful_sends = 0
        self.error_counts = {}
        self.response_times = []

    def record_attempt(self):
        self.total_attempts += 1

    def record_success(self, response_time):
        self.successful_sends += 1
        self.response_times.append(response_time)

    def record_error(self, error_code):
        self.error_counts[error_code] = self.error_counts.get(error_code, 0) + 1

    def get_success_rate(self):
        if self.total_attempts == 0:
            return 0
        return self.successful_sends / self.total_attempts

    def get_average_response_time(self):
        if not self.response_times:
            return 0
        return sum(self.response_times) / len(self.response_times)
```

## 🔄 版本兼容性

### V1 vs V2 Webhook
```python
# V1 格式（已废弃）
webhook_v1 = "https://open.feishu.cn/open-apis/bot/hook/xxx"

# V2 格式（推荐使用）
webhook_v2 = "https://open.feishu.cn/open-apis/bot/v2/hook/xxx"
```

### 向后兼容处理
```python
def detect_webhook_version(webhook_url):
    """检测webhook版本"""
    if "/v2/hook/" in webhook_url:
        return "v2"
    elif "/hook/" in webhook_url:
        return "v1"
    else:
        return "unknown"

def adapt_request_format(webhook_url, data):
    """根据版本调整请求格式"""
    version = detect_webhook_version(webhook_url)
    if version == "v1":
        # V1可能需要不同的格式
        return adapt_v1_format(data)
    return data  # V2使用标准格式
```

---

**📅 文档更新时间**：2025-08-19
**✅ 状态**：已验证可用
**🔧 维护者**：QuantFM开发团队
**📖 版本**：v2.0 - 包含完整的技术实现细节
