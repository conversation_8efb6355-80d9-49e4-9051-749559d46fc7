# 飞书通知配置模板和示例

## 📋 配置文件模板

### config/main.toml 配置示例
```toml
[notification.feishu]
# 是否启用飞书通知
enabled = true

# 主要的飞书机器人配置
webhook1 = "https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-id-1"
secret1 = "your-secret-key-1"

# 备用的飞书机器人配置（可选）
webhook2 = "https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-id-2"
secret2 = "your-secret-key-2"

# 第三个飞书机器人配置（可选）
webhook3 = "https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-id-3"
secret3 = "your-secret-key-3"

# 通知频率控制
[notification.feishu.rate_limit]
# 同一股票的通知间隔（秒）
notify_interval = 300
# 每分钟最大通知数
max_notifications_per_minute = 20
# 是否启用频率限制
enable_rate_limit = true

# 消息格式配置
[notification.feishu.message]
# 默认消息模板
default_template = "stock_surge"
# 是否包含技术指标
include_technical_indicators = true
# 是否包含市场数据
include_market_data = true
```

## 🔧 完整的实现示例

### 1. 基础FeishuNotifier类
```python
import time
import hmac
import hashlib
import base64
import requests
from typing import Dict, Optional
from datetime import datetime

class FeishuNotifier:
    """飞书通知器 - 完整实现"""
    
    def __init__(self, webhook_url: str, secret: str, logger, use_signature: bool = True):
        self.webhook_url = webhook_url
        self.secret = secret
        self.logger = logger
        self.use_signature = use_signature
        
        # 频率控制
        self.last_notify_time = {}
        self.notify_interval = 300  # 5分钟
        self.max_notifications_per_minute = 20
        self.notification_count = 0
        self.last_minute_reset = time.time()
        
        # 启用状态
        if self.use_signature:
            self.enabled = bool(webhook_url and secret)
        else:
            self.enabled = bool(webhook_url)
    
    def _generate_sign(self, timestamp: str) -> str:
        """生成飞书签名"""
        try:
            # 构建密钥：timestamp + "\n" + secret
            key = f'{timestamp}\n{self.secret}'
            key_enc = key.encode('utf-8')
            
            # 待签名消息：空字符串
            msg = ""
            msg_enc = msg.encode('utf-8')
            
            # HMAC-SHA256加密
            hmac_code = hmac.new(key_enc, msg_enc, digestmod=hashlib.sha256).digest()
            
            # Base64编码
            sign = base64.b64encode(hmac_code).decode('utf-8')
            
            self.logger.debug(f"签名生成: timestamp={timestamp}, sign={sign[:20]}...")
            return sign
            
        except Exception as e:
            self.logger.error(f"签名生成失败: {e}")
            return ""
    
    def _check_rate_limit(self, stock_code: str = None) -> bool:
        """检查频率限制"""
        current_time = time.time()
        
        # 重置每分钟计数器
        if current_time - self.last_minute_reset > 60:
            self.notification_count = 0
            self.last_minute_reset = current_time
        
        # 检查每分钟限制
        if self.notification_count >= self.max_notifications_per_minute:
            self.logger.warning("达到每分钟最大通知数限制")
            return False
        
        # 检查单个股票的通知间隔
        if stock_code:
            last_time = self.last_notify_time.get(stock_code, 0)
            if current_time - last_time < self.notify_interval:
                self.logger.debug(f"股票 {stock_code} 通知间隔未到")
                return False
            self.last_notify_time[stock_code] = current_time
        
        self.notification_count += 1
        return True
    
    def _send_request(self, data: Dict) -> bool:
        """发送HTTP请求"""
        try:
            response = requests.post(
                self.webhook_url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10,
                proxies={'http': '', 'https': ''}  # 绕过代理
            )
            
            self.logger.info(f"飞书响应: {response.status_code} - {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    return True
                else:
                    self.logger.error(f"飞书API错误: {result}")
            else:
                self.logger.error(f"HTTP错误: {response.status_code}")
            
            return False
            
        except Exception as e:
            self.logger.error(f"发送请求异常: {e}")
            return False
    
    def send_text_message(self, text: str, stock_code: str = None) -> bool:
        """发送文本消息（公共接口）"""
        if not self.enabled:
            self.logger.warning("飞书通知器未启用")
            return False
        
        if not self._check_rate_limit(stock_code):
            return False
        
        try:
            if self.use_signature:
                timestamp = str(int(time.time()))
                sign = self._generate_sign(timestamp)
                
                if not sign:
                    self.logger.error("签名生成失败")
                    return False
                
                data = {
                    "timestamp": timestamp,
                    "sign": sign,
                    "msg_type": "text",
                    "content": {"text": text}
                }
            else:
                data = {
                    "msg_type": "text",
                    "content": {"text": text}
                }
            
            return self._send_request(data)
            
        except Exception as e:
            self.logger.error(f"发送文本消息异常: {e}")
            return False
    
    def send_card_message(self, card_data: Dict, stock_code: str = None) -> bool:
        """发送卡片消息（公共接口）"""
        if not self.enabled:
            self.logger.warning("飞书通知器未启用")
            return False
        
        if not self._check_rate_limit(stock_code):
            return False
        
        try:
            if self.use_signature:
                timestamp = str(int(time.time()))
                sign = self._generate_sign(timestamp)
                
                if not sign:
                    self.logger.error("签名生成失败")
                    return False
                
                data = {
                    "timestamp": timestamp,
                    "sign": sign,
                    "msg_type": "interactive",
                    "card": card_data
                }
            else:
                data = {
                    "msg_type": "interactive",
                    "card": card_data
                }
            
            return self._send_request(data)
            
        except Exception as e:
            self.logger.error(f"发送卡片消息异常: {e}")
            return False
    
    def test_connection(self) -> bool:
        """测试连接"""
        test_message = f"QuantFM连接测试 - {datetime.now().strftime('%H:%M:%S')}"
        return self.send_text_message(test_message)
```

### 2. 消息模板构建器
```python
class FeishuMessageBuilder:
    """飞书消息构建器"""
    
    @staticmethod
    def build_volume_surge_card(signal_data: Dict) -> Dict:
        """构建成交量激增卡片消息"""
        stock_code = signal_data.get('stock_code', '')
        stock_name = signal_data.get('stock_name', '')
        volume_ratio = signal_data.get('volume_ratio', 0)
        price_change = signal_data.get('price_change_percent', 0)
        current_price = signal_data.get('current_price', 0)
        
        # 确定颜色主题
        if price_change > 5:
            template = "red"
            emoji = "🔥"
        elif price_change > 0:
            template = "orange"
            emoji = "📈"
        elif price_change < -5:
            template = "green"
            emoji = "📉"
        else:
            template = "blue"
            emoji = "📊"
        
        card = {
            "header": {
                "template": template,
                "title": {
                    "content": f"{emoji} 成交量激增信号",
                    "tag": "plain_text"
                }
            },
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "content": f"**股票代码**: {stock_code}\n**股票名称**: {stock_name}",
                        "tag": "lark_md"
                    }
                },
                {
                    "tag": "div",
                    "text": {
                        "content": f"**当前价格**: ¥{current_price:.2f}\n**涨跌幅**: {price_change:+.2f}%",
                        "tag": "lark_md"
                    }
                },
                {
                    "tag": "div",
                    "text": {
                        "content": f"**成交量比**: {volume_ratio:.2f}倍\n**信号时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                        "tag": "lark_md"
                    }
                },
                {
                    "tag": "hr"
                },
                {
                    "tag": "note",
                    "elements": [
                        {
                            "tag": "plain_text",
                            "content": "QuantFM量化交易系统 - 成交量监控"
                        }
                    ]
                }
            ]
        }
        
        return card
    
    @staticmethod
    def build_system_status_card(status_data: Dict) -> Dict:
        """构建系统状态卡片消息"""
        return {
            "header": {
                "template": "blue",
                "title": {
                    "content": "🖥️ 系统状态报告",
                    "tag": "plain_text"
                }
            },
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "content": f"**运行状态**: {status_data.get('status', '未知')}\n**运行时间**: {status_data.get('uptime', '未知')}",
                        "tag": "lark_md"
                    }
                },
                {
                    "tag": "div",
                    "text": {
                        "content": f"**监控股票数**: {status_data.get('stock_count', 0)}\n**今日信号数**: {status_data.get('signal_count', 0)}",
                        "tag": "lark_md"
                    }
                }
            ]
        }
```

### 3. 使用示例
```python
# 初始化
webhook_url = "https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-id"
secret = "your-secret-key"
logger = get_logger('FeishuTest')

notifier = FeishuNotifier(webhook_url, secret, logger, use_signature=True)

# 发送文本消息
notifier.send_text_message("系统启动成功！")

# 发送成交量激增信号
signal_data = {
    'stock_code': '000001',
    'stock_name': '平安银行',
    'volume_ratio': 3.5,
    'price_change_percent': 2.8,
    'current_price': 12.45
}

card = FeishuMessageBuilder.build_volume_surge_card(signal_data)
notifier.send_card_message(card, stock_code='000001')

# 测试连接
if notifier.test_connection():
    print("飞书通知功能正常")
else:
    print("飞书通知功能异常")
```

## 🚀 快速部署指南

### 1. 创建飞书机器人
1. 进入飞书群聊
2. 点击群设置 → 群机器人 → 添加机器人
3. 选择"自定义机器人"
4. 设置机器人名称和头像
5. **重要**：启用"签名校验"
6. 复制Webhook URL和Secret

### 2. 配置系统
```bash
# 编辑配置文件
vim config/main.toml

# 填入获取的webhook和secret
[notification.feishu]
enabled = true
webhook1 = "你的webhook_url"
secret1 = "你的secret"
```

### 3. 测试配置
```python
# 运行测试脚本
python -c "
from processes.volume_surge_processor import VolumeSurgeProcessor
processor = VolumeSurgeProcessor()
if processor.feishu_notifier.test_connection():
    print('✅ 配置成功')
else:
    print('❌ 配置失败')
"
```

---

**📅 创建时间**：2025-08-19  
**🔧 适用版本**：QuantFM v2.0+  
**📖 配套文档**：飞书通知功能详细说明.md
