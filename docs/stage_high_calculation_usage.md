# 阶段高点计算功能使用说明

## 功能概述

在盘后策略进程中，新增了使用scipy计算阶段高点(stage_high_date)的功能。该功能会在更新技术指标时，重新计算从低点开始后的局部最高点，使用两边各5个数据点的窗口来识别真正的阶段性高点，并基于新的高点重新计算斐波那契各周期值。

## ✅ 已实现功能

1. **字段名修复**：正确使用 `trade_time` 字段而不是 `trade_date`
2. **scipy阶段高点计算**：使用 `scipy.signal.argrelextrema` 识别局部最高点
3. **斐波那契重新计算**：基于新的低点和高点重新计算所有斐波那契周期值
4. **数据库更新**：自动更新 `stage_high_date` 和 `stage_high_price` 字段
5. **集成测试通过**：所有功能已验证正常工作

## 技术实现

### 核心算法
- 使用 `scipy.signal.argrelextrema` 函数
- 设置 `order=5`，即两边各5个数据点的窗口
- 在从低点开始之后的数据中寻找局部最高点
- 选择第一个（最接近低点的）局部最高点作为阶段高点

### 计算流程
1. 根据 `start_low_date` 和 `start_low_price` 定位起始低点
2. 提取从低点之后的K线数据（使用 `trade_time` 字段）
3. 使用scipy找到局部最高点（两边各5个点的窗口）
4. 返回第一个局部最高点的日期和价格
5. 基于新的低点和高点重新计算斐波那契回撤位和扩展位
6. 更新到数据库的 `stage_high_date`、`stage_high_price` 和所有斐波那契字段

## 代码位置

### 主要文件
- `processes/after_market_schedule.py` - 主要实现
- `indicators/primary_signal_indicators.py` - 技术指标计算

### 关键方法
```python
def calculate_stage_high_with_scipy(self, df, start_low_price, start_low_date):
    """
    使用scipy计算从低点开始后的阶段高点
    
    Args:
        df: K线数据DataFrame
        start_low_price: 起始低点价格
        start_low_date: 起始低点日期
        
    Returns:
        包含stage_high_date和stage_high_price的字典
    """
```

## 使用场景

### 自动触发
- 在盘后策略执行时自动运行
- 对所有活跃信号的股票进行阶段高点重新计算
- 更新结果保存到 `stock_primary_signals` 表

### 手动触发
```bash
# 强制执行盘后策略（包含阶段高点计算）
python main.py --force-after-market
```

## 数据库字段

### 更新的字段
- `stage_high_date` - 阶段高点日期
- `stage_high_price` - 阶段高点价格

### SQL示例
```sql
-- 查看最新的阶段高点数据
SELECT 
    stock_code,
    stock_name,
    start_low_date,
    start_low_price,
    stage_high_date,
    stage_high_price,
    (stage_high_price - start_low_price) / start_low_price * 100 as gain_percent
FROM stock_primary_signals 
WHERE is_active = true 
ORDER BY stage_high_date DESC;
```

## 算法优势

### 相比简单最高点的优势
1. **局部性识别**：能识别真正的阶段性高点，而不是全局最高点
2. **噪音过滤**：两边各5个点的窗口有效过滤短期波动
3. **趋势敏感**：能更好地反映价格趋势的转折点

### 参数说明
- `order=5`：两边各5个数据点的窗口
- 可以根据需要调整，更大的order值会找到更显著的高点
- 更小的order值会更敏感，可能包含更多噪音

## 错误处理

### 常见情况
1. **数据不足**：如果低点后数据少于11个点，返回空值
2. **无局部最高点**：如果scipy未找到局部最高点，使用全局最高点
3. **日期匹配失败**：如果找不到精确的低点匹配，使用最接近的日期

### 日志输出
```
DEBUG: 找到阶段高点: 2025-02-01, 9.1591
DEBUG: 未找到局部最高点，使用全局最高点: 2025-02-15, 10.2345
WARNING: 数据中缺少日期信息，无法计算阶段高点
```

## 性能考虑

### 计算复杂度
- scipy.argrelextrema 的时间复杂度为 O(n)
- 对于典型的K线数据（几百个数据点），计算时间在毫秒级别

### 内存使用
- 只处理单只股票的数据，内存占用很小
- 使用numpy数组，内存效率高

## 监控和调试

### 启用调试日志
```python
import logging
logging.getLogger('after_market_scheduler').setLevel(logging.DEBUG)
```

### 验证结果
```python
# 检查计算结果的合理性
def validate_stage_high(start_low_price, stage_high_price, start_low_date, stage_high_date):
    # 高点应该高于低点
    assert stage_high_price > start_low_price
    # 高点日期应该晚于低点日期
    assert stage_high_date > start_low_date
    # 涨幅应该在合理范围内（例如不超过500%）
    gain = (stage_high_price - start_low_price) / start_low_price
    assert gain < 5.0
```

## 扩展功能

### 未来可能的改进
1. **多重高点识别**：识别多个阶段高点
2. **动态窗口大小**：根据股票波动性调整窗口大小
3. **高点强度评分**：为不同高点分配强度评分
4. **趋势确认**：结合成交量等指标确认高点有效性

---

**作者**: QuantFM Team  
**创建时间**: 2025-08-25  
**版本**: 1.0.0
