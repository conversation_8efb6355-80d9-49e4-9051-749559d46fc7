"""
信号失效管理模块

负责检查和管理主信号表中信号的失效逻辑。
主要功能：
- 检查信号是否满足失效条件
- 批量处理信号失效检查
- 更新信号状态为非活跃

失效条件：
近期50个交易日内有多于等于15个交易日收盘价低于TWAP则设置为False

Author: System
Date: 2025-08-18
"""

import sys
import os
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.db_manager import get_db_manager
from indicators.primary_signal_indicators import calculate_twap_from_start_date

logger = logging.getLogger(__name__)


class SignalDeactivationManager:
    """信号失效管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.db_manager = get_db_manager()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 失效检查参数
        self.check_period_days = 50  # 检查最近50个交易日
        self.min_below_twap_days = 15  # 至少15个交易日低于TWAP
        
    def check_and_deactivate_all_signals(self) -> Dict[str, int]:
        """
        检查并失效所有符合条件的信号
        
        Returns:
            统计信息字典
        """
        try:
            # 获取所有活跃信号
            active_signals = self._get_active_signals()
            
            if not active_signals:
                self.logger.info("没有活跃信号需要检查")
                return {'total': 0, 'checked': 0, 'deactivated': 0}
            
            self.logger.info(f"开始检查 {len(active_signals)} 个活跃信号")
            
            checked_count = 0
            deactivated_count = 0
            
            for signal in active_signals:
                try:
                    stock_code = signal['stock_code']
                    start_low_date = signal['start_low_date']
                    
                    # 检查是否需要失效
                    should_deactivate = self._should_deactivate_signal(stock_code, start_low_date)
                    checked_count += 1
                    
                    if should_deactivate:
                        success = self._deactivate_signal(stock_code, start_low_date)
                        if success:
                            deactivated_count += 1
                            self.logger.info(f"信号失效: {stock_code} (开始日期: {start_low_date})")
                        else:
                            self.logger.error(f"信号失效操作失败: {stock_code}")
                    
                except Exception as e:
                    self.logger.error(f"检查信号失败 {signal.get('stock_code', 'unknown')}: {e}")
                    continue
            
            result = {
                'total': len(active_signals),
                'checked': checked_count,
                'deactivated': deactivated_count
            }
            
            self.logger.info(f"信号失效检查完成: 总数={result['total']}, 检查={result['checked']}, 失效={result['deactivated']}")
            return result
            
        except Exception as e:
            self.logger.error(f"批量信号失效检查失败: {e}")
            return {'total': 0, 'checked': 0, 'deactivated': 0}
    
    def _get_active_signals(self) -> List[Dict[str, Any]]:
        """
        获取所有活跃信号

        Returns:
            活跃信号列表，每个信号包含股票代码、开始日期等信息
        """
        try:
            query_sql = """
                SELECT stock_code, start_low_date, signal_time, strategy_name
                FROM stock_primary_signals
                WHERE is_active = true
                ORDER BY stock_code, start_low_date
            """

            results = self.db_manager.fetch_all(query_sql)

            if not results:
                return []

            # 处理查询结果，兼容不同的返回格式
            signals = []
            for row in results:
                if isinstance(row, dict):
                    signals.append({
                        'stock_code': row['stock_code'],
                        'start_low_date': row['start_low_date'],
                        'signal_time': row['signal_time'],
                        'strategy_name': row['strategy_name']
                    })
                elif isinstance(row, (list, tuple)):
                    signals.append({
                        'stock_code': row[0],
                        'start_low_date': row[1],
                        'signal_time': row[2],
                        'strategy_name': row[3]
                    })

            return signals

        except Exception as e:
            self.logger.error(f"获取活跃信号失败: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return []
    
    def _should_deactivate_signal(self, stock_code: str, start_low_date: datetime) -> bool:
        """
        判断信号是否应该失效

        正确的业务逻辑：
        1. 获取从start_low_date到最新日期的所有K线数据
        2. 计算总交易日数量的三分之一作为检查窗口
        3. 在最新的三分之一交易日内，对每一天：
           - 计算从start_low_date到当天的TWAP值
           - 比较当天收盘价与当天TWAP值
        4. 如果检查窗口内有≥15个交易日的收盘价低于对应的TWAP，则信号失效

        Args:
            stock_code: 股票代码
            start_low_date: 信号开始日期（从stock_primary_signals表获取）

        Returns:
            是否应该失效
        """
        try:
            # 修复：获取从start_low_date到最新日期的完整K线数据
            # 而不是只获取最近50个交易日的数据
            kline_data = self._get_kline_data_from_start_date(stock_code, start_low_date)

            if not kline_data:
                self.logger.debug(f"股票 {stock_code} 从 {start_low_date} 开始没有K线数据，跳过失效检查")
                return False

            # 注意：这里不再计算固定的TWAP值
            # 而是在后面对每一天都计算对应的TWAP值

            # 修复：正确的失效检查逻辑
            # 1. 先筛选出从start_low_date开始的有效数据
            valid_kline_data = []
            start_date = start_low_date.date() if hasattr(start_low_date, 'date') else start_low_date

            for day_data in kline_data:
                trade_time = day_data['trade_time']
                if isinstance(trade_time, str):
                    trade_time = pd.to_datetime(trade_time).date()
                elif hasattr(trade_time, 'date'):
                    trade_time = trade_time.date()

                # 只包含start_low_date之后的数据（包含当天）
                if trade_time >= start_date:
                    valid_kline_data.append(day_data)

            if not valid_kline_data:
                self.logger.debug(f"股票 {stock_code} 没有从 {start_date} 开始的有效数据")
                return False

            # 2. 计算总交易日数量的三分之一作为检查窗口
            total_days = len(valid_kline_data)
            check_window_size = max(1, total_days // 3)  # 至少检查1天

            # 3. 使用向量化操作进行高效的TWAP比较
            # 将数据转换为DataFrame以便进行向量化操作
            df = pd.DataFrame(valid_kline_data)
            df['trade_time'] = pd.to_datetime(df['trade_time'])
            df = df.sort_values('trade_time').reset_index(drop=True)

            # 计算检查窗口的索引范围
            check_start_index = total_days - check_window_size

            # 向量化计算累积成交量和累积金额（用于TWAP计算）
            # TWAP = 累积成交金额 / 累积成交量
            df['cumulative_volume'] = df['volume'].cumsum()
            df['cumulative_amount'] = (df['close'] * df['volume']).cumsum()

            # 向量化计算每天的TWAP（从start_low_date到当天）
            # 使用pandas的向量化除法，自动处理除零情况
            df['daily_twap'] = df['cumulative_amount'] / df['cumulative_volume']

            # 处理成交量为0的边界情况（使用收盘价作为TWAP）
            zero_volume_mask = df['cumulative_volume'] == 0
            if zero_volume_mask.any():
                df.loc[zero_volume_mask, 'daily_twap'] = df.loc[zero_volume_mask, 'close']

            # 向量化比较：当天收盘价 < 当天TWAP
            df['below_twap'] = df['close'] < df['daily_twap']

            # 只统计检查窗口内的数据（最新的1/3交易日）
            check_window_df = df.iloc[check_start_index:].copy()

            # 向量化统计低于TWAP的天数
            below_twap_count = int(check_window_df['below_twap'].sum())

            # 调试信息：显示检查窗口的详细数据
            if self.logger.isEnabledFor(logging.DEBUG):
                self.logger.debug(f"股票 {stock_code} 检查窗口详细数据 (最新{check_window_size}天):")
                for idx, row in check_window_df.head(5).iterrows():  # 只显示前5天
                    self.logger.debug(
                        f"  {row['trade_time'].date()}: 收盘价{row['close']:.3f} "
                        f"{'<' if row['below_twap'] else '>='} TWAP{row['daily_twap']:.3f} "
                        f"{'✓' if row['below_twap'] else '✗'}"
                    )
                if len(check_window_df) > 5:
                    self.logger.debug(f"  ... 还有 {len(check_window_df) - 5} 天数据")

            # 4. 判断是否满足失效条件：在检查窗口内≥15个交易日低于对应的TWAP
            should_deactivate = below_twap_count >= self.min_below_twap_days

            self.logger.debug(
                f"股票 {stock_code} 失效检查: "
                f"开始日期={start_low_date}, "
                f"总交易日={total_days}, 检查窗口={check_window_size}, "
                f"窗口内低于对应TWAP天数={below_twap_count}, "
                f"失效阈值={self.min_below_twap_days}, 是否失效={should_deactivate}"
            )

            return should_deactivate

        except Exception as e:
            self.logger.error(f"判断信号失效失败 {stock_code}: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return False
    
    def _get_kline_data_from_start_date(self, stock_code: str, start_date: datetime) -> List[Dict[str, Any]]:
        """
        获取从指定开始日期到最新日期的K线数据

        这是修复后的方法，用于正确实现信号失效检查的业务逻辑：
        1. 从stock_primary_signals表的start_low_date开始获取数据
        2. 获取到最新交易日的所有K线数据
        3. 用于计算完整时间段的TWAP和统计低于TWAP的天数

        Args:
            stock_code: 股票代码
            start_date: 开始日期（来自stock_primary_signals.start_low_date）

        Returns:
            K线数据列表，按时间升序排列（便于后续处理）
        """
        try:
            # 转换开始日期格式
            if isinstance(start_date, datetime):
                start_date_str = start_date.date()
            else:
                start_date_str = start_date

            # 获取当前日期作为结束日期
            end_date = datetime.now().date()

            # 查询从start_date到最新日期的所有K线数据
            query_sql = """
                SELECT trade_time, open, high, low, close, volume
                FROM stock_kline_day
                WHERE stock_code = %s
                  AND DATE(trade_time) >= %s
                  AND DATE(trade_time) <= %s
                ORDER BY trade_time ASC
            """
            
            results = self.db_manager.fetch_all(query_sql, [stock_code, start_date_str, end_date])

            if results:
                # 将查询结果转换为标准格式
                # 确保数据类型正确，便于后续的TWAP计算和失效判断
                kline_data = []
                for row in results:
                    try:
                        # 兼容不同的数据库返回格式（字典或元组）
                        if isinstance(row, dict):
                            kline_data.append({
                                'trade_time': row['trade_time'],
                                'open': float(row['open']),
                                'high': float(row['high']),
                                'low': float(row['low']),
                                'close': float(row['close']),
                                'volume': float(row['volume'])
                            })
                        elif isinstance(row, (list, tuple)):
                            kline_data.append({
                                'trade_time': row[0],
                                'open': float(row[1]),
                                'high': float(row[2]),
                                'low': float(row[3]),
                                'close': float(row[4]),
                                'volume': float(row[5])
                            })
                        else:
                            self.logger.warning(f"未知的数据格式: {type(row)}")
                            continue

                    except (ValueError, TypeError, IndexError, KeyError) as e:
                        self.logger.warning(f"数据转换失败 {stock_code}: {e}, 跳过该条记录")
                        continue

                self.logger.debug(
                    f"股票 {stock_code} 从 {start_date_str} 到 {end_date} "
                    f"获取到 {len(kline_data)} 条有效K线数据"
                )
                return kline_data
            else:
                self.logger.debug(f"股票 {stock_code} 从 {start_date_str} 开始没有找到K线数据")
                return []

        except Exception as e:
            self.logger.error(f"获取从开始日期的K线数据失败 {stock_code}: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return []
    
    def _deactivate_signal(self, stock_code: str, start_low_date: datetime) -> bool:
        """
        失效指定信号
        
        Args:
            stock_code: 股票代码
            start_low_date: 信号开始日期
            
        Returns:
            操作是否成功
        """
        try:
            update_sql = """
                UPDATE stock_primary_signals 
                SET is_active = false, updated_at = NOW()
                WHERE stock_code = %s AND start_low_date = %s AND is_active = true
            """
            
            result = self.db_manager.execute_query(update_sql, [stock_code, start_low_date])
            
            if result:
                self.logger.debug(f"成功失效信号: {stock_code} - {start_low_date}")
                return True
            else:
                self.logger.warning(f"失效信号失败: {stock_code} - {start_low_date}")
                return False
                
        except Exception as e:
            self.logger.error(f"失效信号异常 {stock_code}: {e}")
            return False
    
    def check_single_signal(self, stock_code: str, start_low_date: datetime) -> bool:
        """
        检查单个信号是否需要失效
        
        Args:
            stock_code: 股票代码
            start_low_date: 信号开始日期
            
        Returns:
            是否已失效
        """
        try:
            should_deactivate = self._should_deactivate_signal(stock_code, start_low_date)
            
            if should_deactivate:
                return self._deactivate_signal(stock_code, start_low_date)
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查单个信号失败 {stock_code}: {e}")
            return False
    
    def get_deactivation_statistics(self) -> Dict[str, Any]:
        """
        获取失效统计信息

        Returns:
            统计信息字典，包含总信号数、活跃信号数等
        """
        try:
            # 总信号数
            total_query = "SELECT COUNT(*) FROM stock_primary_signals"
            total_result = self.db_manager.fetch_one(total_query)
            total_count = self._extract_count_from_result(total_result)

            # 活跃信号数
            active_query = "SELECT COUNT(*) FROM stock_primary_signals WHERE is_active = true"
            active_result = self.db_manager.fetch_one(active_query)
            active_count = self._extract_count_from_result(active_result)

            # 非活跃信号数
            inactive_count = total_count - active_count

            # 今日失效的信号数 (使用PostgreSQL语法)
            today_deactivated_query = """
                SELECT COUNT(*) FROM stock_primary_signals
                WHERE is_active = false AND DATE(updated_at) = CURRENT_DATE
            """
            today_result = self.db_manager.fetch_one(today_deactivated_query)
            today_deactivated = self._extract_count_from_result(today_result)

            return {
                'total_signals': total_count,
                'active_signals': active_count,
                'inactive_signals': inactive_count,
                'today_deactivated': today_deactivated,
                'active_ratio': round(active_count / total_count * 100, 2) if total_count > 0 else 0
            }

        except Exception as e:
            self.logger.error(f"获取失效统计信息失败: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return {
                'total_signals': 0,
                'active_signals': 0,
                'inactive_signals': 0,
                'today_deactivated': 0,
                'active_ratio': 0
            }

    def _extract_count_from_result(self, result) -> int:
        """
        从数据库查询结果中提取计数值

        Args:
            result: 数据库查询结果

        Returns:
            计数值
        """
        if not result:
            return 0

        if isinstance(result, dict):
            return result.get('count', 0)
        elif isinstance(result, (list, tuple)):
            return result[0] if len(result) > 0 else 0
        else:
            return int(result) if result else 0


# 全局实例
_signal_deactivation_manager = None

def get_signal_deactivation_manager() -> SignalDeactivationManager:
    """获取信号失效管理器实例"""
    global _signal_deactivation_manager
    if _signal_deactivation_manager is None:
        _signal_deactivation_manager = SignalDeactivationManager()
    return _signal_deactivation_manager


if __name__ == "__main__":
    # 简单测试
    print("信号失效管理器测试")
    
    try:
        manager = get_signal_deactivation_manager()
        print("✅ 信号失效管理器创建成功")
        
        # 获取统计信息
        stats = manager.get_deactivation_statistics()
        print(f"✅ 统计信息: {stats}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
