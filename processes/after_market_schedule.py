#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
盘后策略选股调度器

实现盘后策略执行，从stock_info表获取股票列表，
从stock_kline_day表获取K线数据，执行所有策略选股，
将命中的信号存储到stock_signals表中。

作者: QuantFM Team
创建时间: 2025-01-05
"""

import sys
import os
import logging
import time
import threading
from datetime import datetime, date
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入数据库管理器和配置
from data.db_manager import get_db_manager
from data.strategy_db_manager import get_strategy_db_manager
from data.models import Signal
from config.config_manager import get_config
from services.feishu_notifier import FeishuNotifier

# 导入策略模块
try:
    from strategies.trending.dual_channel_fibonacci import DualChannelFibonacciStrategy
    from indicators.primary_signal_indicators import calculate_all_primary_indicators
    from processes.signal_deactivation_manager import get_signal_deactivation_manager
    DUAL_CHANNEL_STRATEGY_AVAILABLE = True
except ImportError as e:
    DUAL_CHANNEL_STRATEGY_AVAILABLE = False
    print(f"警告: 双通道斐波那契策略导入失败: {e}")


class AfterMarketScheduler:
    """盘后策略选股调度器"""

    def __init__(self):
        self.logger = self._setup_logging()
        self.db_manager = None
        self.strategy_db_manager = None
        self.feishu_notifier = None
        self.signals = []  # 存储所有信号
        self.lock = threading.Lock()  # 线程锁

        # 配置参数
        self.max_workers = 4  # 线程池大小
        self.batch_size = 50  # 每批处理的股票数量

        # 初始化数据库连接
        if not self._init_database():
            raise Exception("数据库初始化失败")

        # 策略实例
        self.dual_channel_strategy = None
        self.signal_deactivation_manager = None
        self._init_strategies()

        self.logger.info("盘后策略选股调度器初始化完成")
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        from utils.logger import get_logger

        # 使用统一的日志管理器，避免重复配置
        logger = get_logger("after_market_scheduler", level="info")

        return logger
    
    def _init_database(self) -> bool:
        """初始化数据库连接"""
        try:
            self.db_manager = get_db_manager()
            self.strategy_db_manager = get_strategy_db_manager()

            # 测试连接
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()

            if result:
                self.logger.info("数据库连接初始化成功")
                return True
            else:
                raise Exception("数据库连接测试失败")

        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            return False

    def _init_feishu_notifier(self) -> bool:
        """
        初始化飞书通知器

        盘后调度进程使用专用的webhook3和secret3配置
        这样可以将盘后报告与其他通知分开，便于管理和监控
        """
        try:
            # 从配置文件获取飞书配置，使用标准的配置路径
            config = get_config()
            feishu_config = config.get('notification', {}).get('feishu', {})

            # 使用专用的webhook3和secret3配置
            webhook_url = feishu_config.get('webhook3', '')
            secret = feishu_config.get('secret3', '')

            if webhook_url and secret:
                self.feishu_notifier = FeishuNotifier(webhook_url, secret, self.logger, use_signature=True)
                self.logger.info("✅ 飞书通知器初始化成功 (使用webhook3)")
                return True
            else:
                self.logger.warning("⚠️ 飞书webhook3配置不完整，通知功能将被禁用")
                self.logger.warning(f"   webhook3: {'已配置' if webhook_url else '未配置'}")
                self.logger.warning(f"   secret3: {'已配置' if secret else '未配置'}")
                self.feishu_notifier = None
                return True  # 不影响主流程

        except Exception as e:
            self.logger.error(f"❌ 初始化飞书通知器异常: {e}")
            self.feishu_notifier = None
            return True  # 不影响主流程
    
    def _init_strategies(self) -> bool:
        """初始化策略实例"""
        try:
            # 初始化双通道斐波那契策略
            if DUAL_CHANNEL_STRATEGY_AVAILABLE:
                try:
                    # 从配置文件获取策略启用状态
                    config = get_config()
                    strategy_config = config.get('strategies', {})
                    dual_channel_enabled = strategy_config.get('dual_channel_fibonacci_enabled', True)
                    
                    if dual_channel_enabled:
                        self.dual_channel_strategy = DualChannelFibonacciStrategy()
                        self.signal_deactivation_manager = get_signal_deactivation_manager()
                        self.logger.info("双通道斐波那契策略初始化成功")
                    else:
                        self.logger.info("双通道斐波那契策略已禁用")
                        self.dual_channel_strategy = None
                        self.signal_deactivation_manager = None
                        
                except Exception as e:
                    self.logger.error(f"双通道斐波那契策略初始化失败: {e}")
                    self.dual_channel_strategy = None
                    # 策略初始化失败不影响主流程
            else:
                self.logger.warning("双通道斐波那契策略不可用，跳过初始化")
                self.dual_channel_strategy = None
            
            return True
            
        except Exception as e:
            self.logger.error(f"策略初始化过程发生异常: {e}")
            # 确保所有策略实例都设置为None
            self.dual_channel_strategy = None
            return True  # 不影响主流程

    def get_stock_list(self) -> List[Dict[str, str]]:
        """从stock_info表获取股票列表"""
        try:
            query = "SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code"
            result = self.db_manager.fetch_all(query)
            
            stocks = []
            for row in result:
                stocks.append({
                    'stock_code': row['stock_code'] if isinstance(row, dict) else row[0],
                    'stock_name': (row['stock_name'] if isinstance(row, dict) else row[1]) or f'股票{row["stock_code"] if isinstance(row, dict) else row[0]}'
                })
            
            self.logger.info(f"获取到 {len(stocks)} 只股票")
            return stocks
            
        except Exception as e:
            self.logger.error(f"获取股票列表失败: {e}")
            return []
    
    def get_stock_kline_data(self, stock_code: str) -> Optional[List[Dict]]:
        """获取股票的所有日K线数据"""
        try:
            query = """
            SELECT trade_time, open, high, low, close, volume, amount 
            FROM stock_kline_day 
            WHERE stock_code = %s 
            ORDER BY trade_time ASC
            """
            
            result = self.db_manager.fetch_all(query, (stock_code,))
            
            if result:
                self.logger.debug(f"股票 {stock_code} 获取到 {len(result)} 条K线数据")
                return result
            else:
                self.logger.warning(f"股票 {stock_code} 没有K线数据")
                return None
                
        except Exception as e:
            self.logger.error(f"获取股票 {stock_code} K线数据失败: {e}")
            return None
    
    def preprocess_kline_data(self, stock_code: str, kline_data: List[Dict]) -> Optional[pd.DataFrame]:
        """
        K线数据预处理函数
        将List[Dict]格式转换为pandas DataFrame，并添加数据验证和基础指标计算
        
        Args:
            stock_code: 股票代码
            kline_data: K线数据列表
            
        Returns:
            预处理后的DataFrame或None（如果数据无效）
        """
        try:
            if not kline_data:
                self.logger.warning(f"股票 {stock_code} K线数据为空")
                return None
            
            # 数据长度验证（降低要求，支持更多股票）
            if len(kline_data) < 700:  # 降低到700条，确保EMA_676能计算
                self.logger.warning(f"股票 {stock_code} K线数据不足700条，当前: {len(kline_data)}")
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(kline_data)
            
            # 数据格式验证
            required_columns = ['trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                self.logger.error(f"股票 {stock_code} 缺少必要字段: {missing_columns}")
                return None
            
            # 数据类型转换
            df['trade_time'] = pd.to_datetime(df['trade_time'])
            df['open'] = pd.to_numeric(df['open'], errors='coerce')
            df['high'] = pd.to_numeric(df['high'], errors='coerce')
            df['low'] = pd.to_numeric(df['low'], errors='coerce')
            df['close'] = pd.to_numeric(df['close'], errors='coerce')
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
            df['amount'] = pd.to_numeric(df['amount'], errors='coerce')
            
            # 检查是否有无效数据
            if df[['open', 'high', 'low', 'close', 'volume']].isnull().any().any():
                self.logger.warning(f"股票 {stock_code} 存在无效数据，将进行清理")
                # 删除包含NaN的行
                df = df.dropna(subset=['open', 'high', 'low', 'close', 'volume'])
                
                if len(df) < 1000:
                    self.logger.warning(f"股票 {stock_code} 清理后数据不足1000条")
                    return None
            
            # 数据合理性验证
            invalid_rows = (
                (df['high'] < df['low']) |
                (df['high'] < df['open']) |
                (df['high'] < df['close']) |
                (df['low'] > df['open']) |
                (df['low'] > df['close']) |
                (df['volume'] < 0) |
                (df['amount'] < 0)
            )
            
            if invalid_rows.any():
                self.logger.warning(f"股票 {stock_code} 存在 {invalid_rows.sum()} 行不合理数据，将进行清理")
                df = df[~invalid_rows]
                
                if len(df) < 1000:
                    self.logger.warning(f"股票 {stock_code} 清理后数据不足1000条")
                    return None
            
            # 按时间排序
            df = df.sort_values('trade_time').reset_index(drop=True)
            
            # 检查时间连续性（允许少量缺失）
            # date_diff = df['trade_time'].diff().dt.days
            # large_gaps = date_diff > 7  # 超过7天的间隔
            # if large_gaps.sum() > 10:  # 如果有超过10个大间隔
            #     self.logger.warning(f"股票 {stock_code} 存在较多时间间隔，可能影响指标计算")
            
            # 集成基础指标计算
            df = self._calculate_indicators(df, stock_code)
            if df is None:
                return None
            
            self.logger.debug(f"股票 {stock_code} 数据预处理完成，有效数据: {len(df)} 条")
            return df
            
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 数据预处理失败: {e}")
            return None
    
    def _calculate_indicators(self, df: pd.DataFrame, stock_code: str) -> Optional[pd.DataFrame]:
        """
        计算所有基础指标
        
        Args:
            df: 原始K线DataFrame
            stock_code: 股票代码
            
        Returns:
            包含所有指标的DataFrame或None（如果计算失败）
        """
        try:
            # 导入indicators模块
            from indicators.talib_wrapper import calculate_ema_series, calculate_volume_ma
            from indicators.channel_indicators import calculate_dual_channels
            from indicators.position_indicators import calculate_price_position
            
            # 1. 计算EMA指标
            ema_periods = [144, 169, 576, 676]
            df = calculate_ema_series(df, ema_periods, 'close')
            
            # 2. 计算20周期成交量移动平均
            try:
                from indicators.talib_wrapper import calculate_volume_ma
                df = calculate_volume_ma(df, [20], 'volume')

                # 验证volume_ma_20列是否成功创建
                if 'volume_ma_20' in df.columns:
                    self.logger.debug(f"股票 {stock_code} volume_ma_20计算成功")
                else:
                    self.logger.warning(f"股票 {stock_code} volume_ma_20列未创建")

            except Exception as e:
                self.logger.warning(f"股票 {stock_code} 成交量移动平均计算失败: {e}")
                # 不中断处理，继续后续步骤

            # 3. 计算双通道指标
            channel1_params = {'upper': 144, 'lower': 169}
            channel2_params = {'upper': 576, 'lower': 676}
            df = calculate_dual_channels(df, channel1_params, channel2_params, 'close')

            # 4. 计算价格位置关系
            df = calculate_price_position(df, 'close', ['channel1', 'channel2'])

            # 5. 计算成交量比率（策略需要的指标）
            if 'volume_ma_20' in df.columns:
                df['volume_ratio'] = df['volume'] / df['volume_ma_20']
                self.logger.debug(f"股票 {stock_code} volume_ratio计算成功")
            else:
                self.logger.warning(f"股票 {stock_code} 无法计算volume_ratio，缺少volume_ma_20")

            # 验证指标计算结果（分为关键指标和可选指标）
            critical_indicators = [
                'ema_144', 'ema_169', 'ema_576', 'ema_676',
                'channel1_upper', 'channel1_lower', 'channel2_upper', 'channel2_lower'
            ]

            optional_indicators = [
                'volume_ma_20', 'volume_ratio',
                'channel1_position', 'channel2_position'
            ]

            # 检查关键指标
            missing_critical = [col for col in critical_indicators if col not in df.columns]
            if missing_critical:
                self.logger.error(f"股票 {stock_code} 关键指标计算失败，缺少: {missing_critical}")
                return None

            # 检查可选指标（缺失时给出警告但不中断处理）
            missing_optional = [col for col in optional_indicators if col not in df.columns]
            if missing_optional:
                self.logger.warning(f"股票 {stock_code} 可选指标缺失: {missing_optional}")
                # 为缺失的可选指标提供默认值
                for col in missing_optional:
                    if col == 'volume_ma_20':
                        df[col] = df['volume'].rolling(window=20, min_periods=1).mean()
                        self.logger.info(f"股票 {stock_code} 已补充 {col} 指标")
                    elif col == 'volume_ratio':
                        # 如果volume_ma_20存在，计算volume_ratio
                        if 'volume_ma_20' in df.columns:
                            df[col] = df['volume'] / df['volume_ma_20']
                        else:
                            # 如果volume_ma_20也不存在，先计算volume_ma_20再计算ratio
                            df['volume_ma_20'] = df['volume'].rolling(window=20, min_periods=1).mean()
                            df[col] = df['volume'] / df['volume_ma_20']
                        self.logger.info(f"股票 {stock_code} 已补充 {col} 指标")
                    elif col in ['channel1_position', 'channel2_position']:
                        # 这些指标如果缺失，可以设置为默认值
                        df[col] = 0.5  # 中性位置
                        self.logger.info(f"股票 {stock_code} 已补充 {col} 指标（默认值）")
            
            # 智能检查指标数据有效性
            self._validate_ema_indicators_quality(df, stock_code)
            
            self.logger.debug(f"股票 {stock_code} 指标计算完成，包含 {len([col for col in df.columns if col not in ['trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount']])} 个指标")
            return df
            
        except ImportError as e:
            self.logger.error(f"股票 {stock_code} 导入indicators模块失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 指标计算失败: {e}")
            return None

    def _validate_ema_indicators_quality(self, df: pd.DataFrame, stock_code: str) -> None:
        """
        智能验证EMA指标数据质量

        Args:
            df: 包含EMA指标的DataFrame
            stock_code: 股票代码
        """
        try:
            total_rows = len(df)

            # EMA指标及其对应的预热期
            ema_indicators = {
                'ema_144': 144,
                'ema_169': 169,
                'ema_576': 576,
                'ema_676': 676
            }

            for col, warmup_period in ema_indicators.items():
                if col not in df.columns:
                    continue

                # 计算理论上应该有有效值的行数
                expected_valid_rows = max(0, total_rows - warmup_period)

                if expected_valid_rows <= 0:
                    # 数据长度不足以计算该EMA
                    self.logger.debug(f"股票 {stock_code} 数据长度 {total_rows} 不足以计算 {col}（需要 {warmup_period}）")
                    continue

                # 检查实际有效值数量
                valid_values = df[col].notna().sum()
                invalid_values = df[col].isna().sum()

                # 计算有效值比例
                if expected_valid_rows > 0:
                    valid_ratio = valid_values / total_rows
                    expected_ratio = expected_valid_rows / total_rows

                    # 如果有效值比例明显低于预期，给出警告
                    if valid_ratio < expected_ratio * 0.8:  # 允许20%的容差
                        self.logger.warning(
                            f"股票 {stock_code} 指标 {col} 数据质量较差: "
                            f"有效值 {valid_values}/{total_rows} ({valid_ratio:.1%}), "
                            f"预期 {expected_valid_rows}/{total_rows} ({expected_ratio:.1%})"
                        )
                    else:
                        self.logger.debug(
                            f"股票 {stock_code} 指标 {col} 数据质量良好: "
                            f"有效值 {valid_values}/{total_rows} ({valid_ratio:.1%})"
                        )

        except Exception as e:
            self.logger.error(f"股票 {stock_code} EMA指标质量验证失败: {e}")

    def execute_strategies(self, stock_code: str, stock_name: str,
                          kline_data: Optional[List[Dict]] = None,
                          preprocessed_df: Optional[pd.DataFrame] = None) -> List[Signal]:
        """
        执行所有策略，返回命中的信号
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            kline_data: 原始K线数据（用于向后兼容）
            preprocessed_df: 预处理后的DataFrame（包含所有指标）
            
        Returns:
            信号列表
            
        Note:
            优先使用preprocessed_df，如果为None则使用kline_data（向后兼容）
        """
        signals = []
        strategy_execution_stats = {}
        
        try:
            # 数据格式验证和准备
            df_for_strategies, kline_data_for_legacy = self._prepare_strategy_data(
                stock_code, preprocessed_df, kline_data
            )
            
            if df_for_strategies is None and kline_data_for_legacy is None:
                self.logger.error(f"股票 {stock_code} 没有提供有效的数据")
                return signals
            
            # 记录数据使用模式
            data_mode = "preprocessed" if df_for_strategies is not None else "legacy"
            self.logger.debug(f"股票 {stock_code} 使用 {data_mode} 数据模式")
            

            
            # 执行新策略（使用预处理后的DataFrame）
            if df_for_strategies is not None:
                # 双通道斐波那契策略
                if self.dual_channel_strategy is not None:
                    try:
                        dual_channel_signal = self.dual_channel_strategy.analyze(
                            stock_code, stock_name, preprocessed_df=df_for_strategies
                        )
                        if dual_channel_signal:
                            signals.append(dual_channel_signal)
                            strategy_execution_stats["双通道斐波那契"] = "成功"
                            self.logger.debug(f"股票 {stock_code} 双通道斐波那契策略产生信号")
                        else:
                            strategy_execution_stats["双通道斐波那契"] = "无信号"
                    except Exception as e:
                        self.logger.error(f"股票 {stock_code} 双通道斐波那契策略执行失败: {e}")
                        strategy_execution_stats["双通道斐波那契"] = f"失败: {str(e)}"
                        # 记录详细的异常信息用于调试
                        import traceback
                        self.logger.debug(f"双通道策略异常详情: {traceback.format_exc()}")
                else:
                    self.logger.debug(f"股票 {stock_code} 双通道斐波那契策略未初始化，跳过执行")
            
            # 记录策略执行统计
            if signals:
                self.logger.info(f"股票 {stock_code} 产生 {len(signals)} 个信号")
                for strategy_name, status in strategy_execution_stats.items():
                    if status == "成功":
                        self.logger.debug(f"  {strategy_name}: {status}")
            else:
                self.logger.debug(f"股票 {stock_code} 无信号产生")
                
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 策略执行过程发生异常: {e}")
            # 记录详细的异常信息用于调试
            import traceback
            self.logger.debug(f"股票 {stock_code} 异常详情: {traceback.format_exc()}")
        
        return signals
    
    def _prepare_strategy_data(self, stock_code: str, 
                              preprocessed_df: Optional[pd.DataFrame], 
                              kline_data: Optional[List[Dict]]) -> tuple[Optional[pd.DataFrame], Optional[List[Dict]]]:
        """
        准备策略执行所需的数据格式
        
        Args:
            stock_code: 股票代码
            preprocessed_df: 预处理后的DataFrame
            kline_data: 原始K线数据
            
        Returns:
            (df_for_strategies, kline_data_for_legacy) 元组
        """
        try:
            df_for_strategies = None
            kline_data_for_legacy = None
            
            if preprocessed_df is not None:
                # 验证预处理数据的完整性
                if self._validate_preprocessed_data(preprocessed_df, stock_code):
                    df_for_strategies = preprocessed_df
                    # 为现有策略转换数据格式
                    kline_data_for_legacy = self._convert_df_to_dict_list(preprocessed_df)
                    if not kline_data_for_legacy:
                        self.logger.warning(f"股票 {stock_code} DataFrame转换为Dict列表失败，回退到原始数据")
                        df_for_strategies = None
                else:
                    self.logger.warning(f"股票 {stock_code} 预处理数据验证失败，回退到原始数据")
            
            # 如果预处理数据不可用，使用原始数据
            if df_for_strategies is None and kline_data is not None:
                if self._validate_raw_kline_data(kline_data, stock_code):
                    kline_data_for_legacy = kline_data
                    self.logger.debug(f"股票 {stock_code} 使用原始数据格式（向后兼容模式）")
                else:
                    self.logger.error(f"股票 {stock_code} 原始数据验证失败")
            
            return df_for_strategies, kline_data_for_legacy
            
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 数据准备失败: {e}")
            return None, None
    
    def _validate_preprocessed_data(self, df: pd.DataFrame, stock_code: str) -> bool:
        """
        验证预处理后的DataFrame数据完整性
        
        Args:
            df: 预处理后的DataFrame
            stock_code: 股票代码
            
        Returns:
            验证是否通过
        """
        try:
            # 检查基础字段
            required_basic_columns = ['trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount']
            missing_basic = [col for col in required_basic_columns if col not in df.columns]
            if missing_basic:
                self.logger.error(f"股票 {stock_code} 预处理数据缺少基础字段: {missing_basic}")
                return False
            
            # 检查数据长度
            if len(df) < 20:
                self.logger.warning(f"股票 {stock_code} 预处理数据长度不足: {len(df)}")
                return False
            
            # 检查指标字段（如果存在）
            expected_indicator_columns = [
                'ema_144', 'ema_169', 'ema_576', 'ema_676',
                'volume_ma_20',
                'channel1_upper', 'channel1_lower', 'channel2_upper', 'channel2_lower'
            ]
            
            missing_indicators = [col for col in expected_indicator_columns if col not in df.columns]
            if missing_indicators:
                self.logger.debug(f"股票 {stock_code} 预处理数据缺少部分指标: {missing_indicators}")
                # 指标缺失不影响基础策略执行，只记录调试信息
            
            # 检查数据有效性
            if df[required_basic_columns].isnull().all(axis=1).any():
                self.logger.warning(f"股票 {stock_code} 预处理数据存在完全无效的行")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 预处理数据验证异常: {e}")
            return False
    
    def _validate_raw_kline_data(self, kline_data: List[Dict], stock_code: str) -> bool:
        """
        验证原始K线数据的完整性
        
        Args:
            kline_data: 原始K线数据
            stock_code: 股票代码
            
        Returns:
            验证是否通过
        """
        try:
            if not kline_data:
                self.logger.error(f"股票 {stock_code} 原始K线数据为空")
                return False
            
            if len(kline_data) < 20:
                self.logger.warning(f"股票 {stock_code} 原始K线数据长度不足: {len(kline_data)}")
                return False
            
            # 检查数据格式
            required_fields = ['trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount']
            first_row = kline_data[0]
            
            missing_fields = [field for field in required_fields if field not in first_row]
            if missing_fields:
                self.logger.error(f"股票 {stock_code} 原始数据缺少必要字段: {missing_fields}")
                return False
            
            # 检查数据类型（抽样检查前几行）
            for i, row in enumerate(kline_data[:min(5, len(kline_data))]):
                try:
                    float(row['open'])
                    float(row['high'])
                    float(row['low'])
                    float(row['close'])
                    float(row['volume'])
                    float(row['amount'])
                except (ValueError, TypeError) as e:
                    self.logger.error(f"股票 {stock_code} 第{i+1}行数据类型错误: {e}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 原始数据验证异常: {e}")
            return False
    
    def _convert_df_to_dict_list(self, df: pd.DataFrame) -> List[Dict]:
        """
        将DataFrame转换为Dict列表格式，供现有策略使用
        
        Args:
            df: 包含K线数据的DataFrame
            
        Returns:
            Dict格式的K线数据列表
        """
        try:
            # 只保留基础K线数据字段
            basic_columns = ['trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount']
            df_basic = df[basic_columns].copy()
            
            # 转换为字典列表
            kline_data = []
            for _, row in df_basic.iterrows():
                kline_dict = {
                    'trade_time': row['trade_time'],
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': int(row['volume']),
                    'amount': float(row['amount'])
                }
                kline_data.append(kline_dict)
            
            return kline_data
            
        except Exception as e:
            self.logger.error(f"DataFrame转换为Dict列表失败: {e}")
            return []
    


    def process_stock_batch(self, stocks: List[Dict[str, str]]) -> List[Signal]:
        """处理一批股票"""
        batch_signals = []

        for stock in stocks:
            try:
                stock_code = stock['stock_code']
                stock_name = stock['stock_name']

                # 获取K线数据
                kline_data = self.get_stock_kline_data(stock_code)
                if not kline_data or len(kline_data) < 1000:
                    continue

                # 数据预处理（包含指标计算）
                preprocessed_df = self.preprocess_kline_data(stock_code, kline_data)
                if preprocessed_df is None:
                    # 如果预处理失败，回退到原始数据格式
                    self.logger.warning(f"股票 {stock_code} 预处理失败，使用原始数据格式")
                    signals = self.execute_strategies(stock_code, stock_name, kline_data=kline_data)
                else:
                    # 使用预处理后的数据
                    signals = self.execute_strategies(stock_code, stock_name, preprocessed_df=preprocessed_df)
                
                batch_signals.extend(signals)
                self.logger.debug(f"处理股票 {stock_code} 完成，产生 {len(signals)} 个信号")

            except Exception as e:
                self.logger.error(f"处理股票 {stock.get('stock_code', 'unknown')} 失败: {e}")

        return batch_signals

    def save_signals(self, signals: List) -> bool:
        """批量保存信号到数据库"""
        if not signals:
            return True

        try:
            saved_count = 0

            for signal in signals:
                try:
                    # 统一转换为主信号格式并保存到主信号表
                    if hasattr(signal, 'break_t1_date') and hasattr(signal, 'break_t2_date'):
                        # 这是EnhancedSignal（双通道斐波那契信号）
                        primary_signal_data = self._convert_to_primary_signal(signal)
                    else:
                        # 这是普通Signal，也转换为主信号格式
                        primary_signal_data = self._convert_regular_signal_to_primary(signal)

                    if primary_signal_data and self._save_to_primary_signals_table(primary_signal_data):
                        saved_count += 1
                        self.logger.debug(f"主信号保存成功: {signal.stock_code}")
                    else:
                        self.logger.warning(f"主信号保存失败: {signal.stock_code}")

                except Exception as e:
                    self.logger.error(f"保存信号失败: {getattr(signal, 'stock_code', 'unknown')} - {e}")
                    continue

            self.logger.info(f"成功保存 {saved_count}/{len(signals)} 个信号到数据库")

            # 发送飞书通知
            if self.feishu_notifier and signals:
                try:
                    self.feishu_notifier.send_strategy_signals(signals)
                except Exception as e:
                    self.logger.error(f"发送飞书通知失败: {e}")

            return saved_count > 0

        except Exception as e:
            self.logger.error(f"批量保存信号失败: {e}")
            return False



    def run(self) -> bool:
        """执行盘后策略选股 - 使用B方案"""
        return self.run_b_plan()

    # ==================== B方案新增方法 ====================

    def run_b_plan(self) -> bool:
        """执行盘后策略选股 - B方案优化版本"""
        try:
            self.logger.info("开始执行盘后策略选股 (B方案)")
            start_time = time.time()

            # 1. 初始化
            if not self._init_database():
                return False
            if not self._init_feishu_notifier():
                return False

            # 阶段1: 股票分类
            strategy_stocks, existing_active_stocks = self.classify_stocks()

            # 阶段2: 策略筛选 (只对需要的股票)
            new_signals = self.execute_strategy_screening(strategy_stocks)

            # 阶段3: 统一技术指标更新 (所有活跃信号)
            self.update_all_technical_indicators()

            # 阶段4: 信号失效检查 (在指标更新后)
            self.check_and_deactivate_signals()

            # 阶段5: 统计和通知
            self.generate_summary_report(new_signals, start_time)

            return True

        except Exception as e:
            self.logger.error(f"盘后策略选股执行失败: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return False

    def classify_stocks(self):
        """
        股票分类逻辑

        将所有股票分为两类：
        1. strategy_stocks: 需要进行策略筛选的股票（没有活跃信号）
        2. existing_active_stocks: 已有活跃信号的股票（只需更新技术指标）

        Returns:
            tuple: (strategy_stocks, existing_active_stocks)
        """
        try:
            # 获取所有股票
            all_stocks = self.get_stock_list()
            if not all_stocks:
                self.logger.warning("未获取到股票列表")
                return [], []

            self.logger.info(f"获取到 {len(all_stocks)} 只股票，开始分类")

            # 查询已有活跃信号的股票
            active_signal_query = """
                SELECT DISTINCT stock_code
                FROM stock_primary_signals
                WHERE is_active = true
            """

            active_results = self.db_manager.fetch_all(active_signal_query)

            # 处理查询结果，兼容不同的返回格式
            active_stock_codes = set()
            if active_results:
                for row in active_results:
                    if isinstance(row, dict):
                        active_stock_codes.add(row['stock_code'])
                    elif isinstance(row, (list, tuple)):
                        active_stock_codes.add(row[0])
                    else:
                        # 如果是其他格式，尝试直接添加
                        active_stock_codes.add(str(row))

            self.logger.info(f"查询到 {len(active_stock_codes)} 只股票有活跃信号")

            # 分类
            strategy_stocks = []      # 需要策略筛选
            existing_active_stocks = []  # 已有活跃信号

            for stock in all_stocks:
                stock_code = stock.get('stock_code') if isinstance(stock, dict) else stock
                if stock_code in active_stock_codes:
                    existing_active_stocks.append(stock)
                else:
                    strategy_stocks.append(stock)

            self.logger.info(f"股票分类完成: 策略筛选={len(strategy_stocks)}, 指标更新={len(existing_active_stocks)}")
            return strategy_stocks, existing_active_stocks

        except Exception as e:
            self.logger.error(f"股票分类失败: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return [], []

    def execute_strategy_screening(self, strategy_stocks):
        """执行策略筛选 - 保持原有逻辑不变"""
        if not strategy_stocks:
            self.logger.info("没有需要策略筛选的股票")
            return []

        try:
            new_signals = []

            # 分批处理
            stock_batches = [strategy_stocks[i:i + self.batch_size] for i in range(0, len(strategy_stocks), self.batch_size)]
            self.logger.info(f"开始策略筛选: {len(strategy_stocks)} 只股票，分为 {len(stock_batches)} 批")

            # 使用线程池并行处理
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_batch = {
                    executor.submit(self.process_stock_batch, batch): i
                    for i, batch in enumerate(stock_batches)
                }

                for future in as_completed(future_to_batch, timeout=600):
                    batch_index = future_to_batch[future]
                    try:
                        batch_signals = future.result()
                        new_signals.extend(batch_signals)
                        self.logger.info(f"策略筛选批次 {batch_index + 1}/{len(stock_batches)} 完成，产生 {len(batch_signals)} 个信号")
                    except Exception as e:
                        self.logger.error(f"策略筛选批次 {batch_index + 1} 失败: {e}")

            # 保存新信号
            if new_signals:
                self.save_signals(new_signals)
                self.logger.info(f"新增信号: {len(new_signals)} 个")

            return new_signals

        except Exception as e:
            self.logger.error(f"策略筛选失败: {e}")
            return []

    def update_all_technical_indicators(self):
        """
        统一更新所有活跃信号的技术指标

        该方法会：
        1. 查询所有活跃信号对应的股票
        2. 批量并行更新这些股票的技术指标
        3. 将更新后的指标保存到主信号表
        """
        try:
            # 获取所有活跃信号的股票
            active_stocks_query = """
                SELECT DISTINCT stock_code, stock_name
                FROM stock_primary_signals
                WHERE is_active = true
            """
            active_results = self.db_manager.fetch_all(active_stocks_query)

            if not active_results:
                self.logger.info("没有活跃信号需要更新指标")
                return

            # 处理查询结果，兼容不同的返回格式
            active_stocks = []
            for row in active_results:
                if isinstance(row, dict):
                    active_stocks.append({
                        'stock_code': row['stock_code'],
                        'stock_name': row['stock_name']
                    })
                elif isinstance(row, (list, tuple)):
                    active_stocks.append({
                        'stock_code': row[0],
                        'stock_name': row[1]
                    })

            if not active_stocks:
                self.logger.warning("解析活跃股票列表失败")
                return

            self.logger.info(f"开始更新 {len(active_stocks)} 只股票的技术指标")

            # 批量处理指标更新
            updated_count = 0
            stock_batches = [active_stocks[i:i + self.batch_size] for i in range(0, len(active_stocks), self.batch_size)]

            if not stock_batches:
                self.logger.warning("没有股票需要更新指标")
                return

            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_batch = {
                    executor.submit(self.process_indicator_batch, batch): i
                    for i, batch in enumerate(stock_batches)
                }

                for future in as_completed(future_to_batch, timeout=600):
                    batch_index = future_to_batch[future]
                    try:
                        batch_updated = future.result()
                        updated_count += batch_updated
                        self.logger.info(f"指标更新批次 {batch_index + 1}/{len(stock_batches)} 完成，更新 {batch_updated} 只股票")
                    except Exception as e:
                        self.logger.error(f"指标更新批次 {batch_index + 1} 失败: {e}")
                        import traceback
                        self.logger.error(f"异常详情: {traceback.format_exc()}")

            self.logger.info(f"技术指标更新完成: {updated_count}/{len(active_stocks)}")

        except Exception as e:
            self.logger.error(f"技术指标更新失败: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")

    def process_indicator_batch(self, stock_batch):
        """批量处理技术指标更新"""
        updated_count = 0

        for stock in stock_batch:
            try:
                if self.update_single_stock_indicators(stock):
                    updated_count += 1
            except Exception as e:
                self.logger.error(f"更新股票 {stock['stock_code']} 指标失败: {e}")
                continue

        return updated_count

    def update_single_stock_indicators(self, stock):
        """
        更新单只股票的技术指标

        该方法会：
        1. 获取股票的K线数据
        2. 预处理数据为DataFrame格式
        3. 计算所有技术指标
        4. 更新到数据库中

        Args:
            stock: 包含stock_code和stock_name的字典

        Returns:
            bool: 更新是否成功
        """
        try:
            stock_code = stock['stock_code']
            self.logger.debug(f"开始更新股票 {stock_code} 的技术指标")

            # 获取K线数据
            kline_data = self.get_stock_kline_data(stock_code)
            if not kline_data:
                self.logger.warning(f"股票 {stock_code} 没有K线数据，跳过指标更新")
                return False

            # 预处理数据
            preprocessed_df = self.preprocess_kline_data(stock_code, kline_data)
            if preprocessed_df is None or preprocessed_df.empty:
                self.logger.warning(f"股票 {stock_code} 数据预处理失败，跳过指标更新")
                return False

            self.logger.debug(f"股票 {stock_code} 预处理完成，数据量: {len(preprocessed_df)}")

            # 获取该股票的start_low_price（用于增强斐波那契计算）
            start_low_price = self._get_stock_start_low_price(stock_code)

            # 计算所有技术指标
            indicators = self._calculate_indicators_with_context(
                preprocessed_df, stock_code, start_low_price
            )

            if not indicators:
                self.logger.warning(f"股票 {stock_code} 技术指标计算失败")
                return False

            self.logger.debug(f"股票 {stock_code} 计算了 {len(indicators)} 个技术指标")

            # 更新数据库
            success = self.update_indicators_in_database(stock_code, indicators)

            if success:
                self.logger.debug(f"股票 {stock_code} 技术指标更新成功")
            else:
                self.logger.warning(f"股票 {stock_code} 技术指标数据库更新失败")

            return success

        except Exception as e:
            self.logger.error(f"更新股票 {stock_code} 指标失败: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return False

    def _get_stock_start_low_price(self, stock_code: str) -> Optional[float]:
        """
        获取股票的起始低点价格

        从主信号表中获取该股票活跃信号的start_low_price

        Args:
            stock_code: 股票代码

        Returns:
            起始低点价格，如果没有找到则返回None
        """
        try:
            query = """
                SELECT start_low_price
                FROM stock_primary_signals
                WHERE stock_code = %s AND is_active = true
                LIMIT 1
            """

            result = self.db_manager.fetch_one(query, (stock_code,))

            if result:
                if isinstance(result, dict):
                    return float(result['start_low_price'])
                elif isinstance(result, (list, tuple)):
                    return float(result[0])
                else:
                    return float(result)
            else:
                return None

        except Exception as e:
            self.logger.warning(f"获取股票 {stock_code} 起始低点价格失败: {e}")
            return None

    def _calculate_indicators_with_context(self, df: pd.DataFrame,
                                         stock_code: str,
                                         start_low_price: Optional[float],
                                         start_low_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        带上下文信息的技术指标计算

        Args:
            df: K线数据DataFrame
            stock_code: 股票代码
            start_low_price: 起始低点价格
            start_low_date: 起始低点日期

        Returns:
            技术指标字典
        """
        try:
            indicators = {}

            # 1. EMA系列指标
            from indicators.primary_signal_indicators import calculate_ema_indicators
            ema_indicators = calculate_ema_indicators(df)
            indicators.update(ema_indicators)

            # 2. 价格指标 (TWAP, VWAP)
            from indicators.primary_signal_indicators import calculate_price_indicators
            price_indicators = calculate_price_indicators(df)
            indicators.update(price_indicators)

            # 3. 技术分析指标
            from indicators.primary_signal_indicators import calculate_technical_indicators
            technical_indicators = calculate_technical_indicators(df)
            indicators.update(technical_indicators)

            # 4. 增强的斐波那契指标（使用start_low_price）
            from indicators.primary_signal_indicators import calculate_fibonacci_indicators
            fibonacci_indicators = calculate_fibonacci_indicators(
                df, start_low_price=start_low_price
            )
            indicators.update(fibonacci_indicators)

            # 5. 枢轴点位
            from indicators.primary_signal_indicators import calculate_pivot_indicators
            pivot_indicators = calculate_pivot_indicators(df)
            indicators.update(pivot_indicators)

            # 6. 趋势和结构指标
            from indicators.primary_signal_indicators import calculate_structure_indicators
            structure_indicators = calculate_structure_indicators(df)
            indicators.update(structure_indicators)

            # 7. 重新计算阶段高点 (使用scipy找到从低点开始后的局部最高点)
            stage_high_indicators = self.calculate_stage_high_with_scipy(df, start_low_price, start_low_date)
            indicators.update(stage_high_indicators)

            # 8. 如果计算出了新的阶段高点，重新计算斐波那契各周期值
            if stage_high_indicators.get('stage_high_date') is not None and stage_high_indicators.get('stage_high_price') is not None:
                updated_fib_indicators = self.recalculate_fibonacci_with_new_high(
                    df, start_low_price, start_low_date,
                    stage_high_indicators['stage_high_price'], stage_high_indicators['stage_high_date']
                )
                indicators.update(updated_fib_indicators)
                self.logger.debug(f"股票 {stock_code} 使用新高点重新计算了斐波那契指标")

            self.logger.debug(f"股票 {stock_code} 技术指标计算完成，共 {len(indicators)} 个指标")
            return indicators

        except Exception as e:
            self.logger.error(f"计算股票 {stock_code} 技术指标失败: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return {}

    def calculate_stage_high_with_scipy(self, df, start_low_price, start_low_date):
        """
        使用scipy计算从低点开始后的阶段高点

        找到从低点开始后，两边各5个数据点的局部最高点作为stage_high

        Args:
            df: K线数据DataFrame
            start_low_price: 起始低点价格
            start_low_date: 起始低点日期

        Returns:
            包含stage_high_date和stage_high_price的字典
        """
        try:
            from scipy.signal import argrelextrema
            import numpy as np
            from datetime import datetime

            # 如果没有起始低点信息，返回空值
            if start_low_price is None or start_low_date is None:
                return {'stage_high_date': None, 'stage_high_price': 0.0}

            # 确保df有日期索引或日期列
            if 'trade_time' not in df.columns and df.index.name != 'trade_time':
                self.logger.warning("数据中缺少日期信息，无法计算阶段高点")
                return {'stage_high_date': None, 'stage_high_price': 0.0}

            # 获取日期列
            if 'trade_time' in df.columns:
                df_with_date = df.copy()
                df_with_date['date'] = pd.to_datetime(df_with_date['trade_time'])
            else:
                df_with_date = df.copy()
                df_with_date['date'] = pd.to_datetime(df_with_date.index)

            # 转换start_low_date为datetime
            if isinstance(start_low_date, str):
                start_low_dt = pd.to_datetime(start_low_date)
            else:
                start_low_dt = pd.to_datetime(start_low_date)

            # 找到起始低点在数据中的位置
            low_point_mask = (df_with_date['date'] >= start_low_dt) & \
                           (abs(df_with_date['low'] - start_low_price) < start_low_price * 0.01)  # 1%的误差容忍

            if not low_point_mask.any():
                # 如果找不到精确匹配，找最接近日期的数据点
                date_diff = abs(df_with_date['date'] - start_low_dt)
                low_point_idx = date_diff.idxmin()
                self.logger.debug(f"未找到精确低点匹配，使用最接近日期的点: {df_with_date.loc[low_point_idx, 'date']}")
            else:
                low_point_idx = df_with_date[low_point_mask].index[0]

            # 获取从低点开始之后的数据
            low_point_position = df_with_date.index.get_loc(low_point_idx)
            after_low_df = df_with_date.iloc[low_point_position + 1:]  # 从低点之后开始

            if len(after_low_df) < 11:  # 至少需要11个点才能找到两边各5个的极值
                self.logger.debug(f"低点后数据不足({len(after_low_df)}个点)，无法计算阶段高点")
                return {'stage_high_date': None, 'stage_high_price': 0.0}

            # 使用scipy找到局部最高点 (两边各5个点的窗口)
            high_prices = after_low_df['high'].values
            local_maxima_indices = argrelextrema(high_prices, np.greater, order=5)[0]

            if len(local_maxima_indices) == 0:
                # 如果没有找到局部最高点，使用整个区间的最高点
                max_idx = np.argmax(high_prices)
                stage_high_price = high_prices[max_idx]
                stage_high_date = after_low_df.iloc[max_idx]['date']
                self.logger.debug(f"未找到局部最高点，使用全局最高点: {stage_high_date}, {stage_high_price}")
            else:
                # 找到第一个局部最高点（最接近低点的）
                first_max_idx = local_maxima_indices[0]
                stage_high_price = high_prices[first_max_idx]
                stage_high_date = after_low_df.iloc[first_max_idx]['date']
                self.logger.debug(f"找到阶段高点: {stage_high_date}, {stage_high_price}")

            return {
                'stage_high_date': stage_high_date,
                'stage_high_price': float(stage_high_price)
            }

        except Exception as e:
            self.logger.error(f"计算阶段高点失败: {e}")
            import traceback
            self.logger.debug(f"异常详情: {traceback.format_exc()}")
            return {'stage_high_date': None, 'stage_high_price': 0.0}

    def recalculate_fibonacci_with_new_high(self, df, start_low_price, start_low_date, stage_high_price, stage_high_date):
        """
        使用新的低点和高点重新计算斐波那契各周期值

        Args:
            df: K线数据DataFrame
            start_low_price: 起始低点价格
            start_low_date: 起始低点日期
            stage_high_price: 阶段高点价格
            stage_high_date: 阶段高点日期

        Returns:
            更新后的斐波那契指标字典
        """
        try:
            # 计算价格差值
            price_diff = stage_high_price - start_low_price

            if price_diff <= 0:
                self.logger.warning(f"高点价格({stage_high_price})不高于低点价格({start_low_price})，无法计算斐波那契")
                return {}

            # 计算斐波那契回撤位（从高点向下）
            fib_retracements = {
                'fib_level_0_236': float(stage_high_price - price_diff * 0.236),
                'fib_level_0_382': float(stage_high_price - price_diff * 0.382),
                'fib_level_0_5': float(stage_high_price - price_diff * 0.5),
                'fib_level_0_618': float(stage_high_price - price_diff * 0.618),
                'fib_level_0_786': float(stage_high_price - price_diff * 0.786),
            }

            # 计算斐波那契扩展位（从高点向上）
            fib_extensions = {
                'fib_level_1_272': float(stage_high_price + price_diff * 0.272),
                'fib_level_1_382': float(stage_high_price + price_diff * 0.382),
                'fib_level_1_618': float(stage_high_price + price_diff * 0.618),
                'f_fib_level_1_272': float(stage_high_price + price_diff * 1.272),
                'f_fib_level_1_382': float(stage_high_price + price_diff * 1.382),
                'f_fib_level_1_618': float(stage_high_price + price_diff * 1.618),
            }

            # 合并所有斐波那契指标
            updated_fib_indicators = {}
            updated_fib_indicators.update(fib_retracements)
            updated_fib_indicators.update(fib_extensions)

            # 添加基础信息用于验证
            updated_fib_indicators.update({
                'fib_base_low': float(start_low_price),
                'fib_base_high': float(stage_high_price),
                'fib_price_range': float(price_diff)
            })

            self.logger.debug(f"重新计算斐波那契指标: 低点={start_low_price:.4f}, 高点={stage_high_price:.4f}, 差值={price_diff:.4f}")

            return updated_fib_indicators

        except Exception as e:
            self.logger.error(f"重新计算斐波那契指标失败: {e}")
            import traceback
            self.logger.debug(f"异常详情: {traceback.format_exc()}")
            return {}

    def update_indicators_in_database(self, stock_code, indicators):
        """
        更新数据库中的技术指标

        只更新数据库中存在的字段，忽略不存在的字段

        Args:
            stock_code: 股票代码
            indicators: 技术指标字典

        Returns:
            bool: 更新是否成功
        """
        if not indicators:
            self.logger.warning(f"股票 {stock_code} 没有指标数据需要更新")
            return False

        try:
            # 定义数据库中存在的技术指标字段
            db_indicator_fields = {
                'ema12', 'ema62', 'ema144', 'ema169', 'ema377', 'ema576', 'ema676',
                'rsi', 'macd', 'macd_signal', 'macd_hist',
                'fib_level_0_236', 'fib_level_0_382', 'fib_level_0_5', 'fib_level_0_618', 'fib_level_0_786',
                'fib_level_1_272', 'fib_level_1_382', 'fib_level_1_618',
                'f_fib_level_1_272', 'f_fib_level_1_382', 'f_fib_level_1_618',
                'boll_upper', 'boll_middle', 'boll_lower',
                'atr', 'adx', 'aroon_up', 'aroon_down',
                'obv', 'mfi', 'twap', 'vwap',
                'pivot_point', 'pivot_r1', 'pivot_r2', 'pivot_s1', 'pivot_s2',
                'stage_high_price'  # 添加阶段高点价格字段
            }

            # 定义可选的扩展字段（如果数据库中存在则更新，不存在则忽略）
            optional_indicator_fields = {
                'fib_base_low', 'fib_base_high', 'fib_price_range'  # 斐波那契基础信息字段
            }

            # 定义需要特殊处理的日期字段
            db_date_fields = {'stage_high_date'}

            # 构建更新SQL，只包含数据库中存在的字段
            update_fields = []
            params = {'stock_code': stock_code}
            updated_count = 0

            for key, value in indicators.items():
                if key in db_indicator_fields:
                    # 确保值是有效的数字
                    if value is not None and not (isinstance(value, float) and (value != value)):  # 检查NaN
                        update_fields.append(f"{key} = %({key})s")
                        params[key] = float(value)
                        updated_count += 1
                elif key in optional_indicator_fields:
                    # 处理可选字段（如果数据库中存在则更新）
                    if value is not None and not (isinstance(value, float) and (value != value)):  # 检查NaN
                        # 先检查字段是否存在于数据库中
                        try:
                            check_sql = f"SELECT column_name FROM information_schema.columns WHERE table_name = 'stock_primary_signals' AND column_name = '{key}'"
                            result = self.db_manager.fetch_one(check_sql)
                            if result:  # 字段存在
                                update_fields.append(f"{key} = %({key})s")
                                params[key] = float(value)
                                updated_count += 1
                        except Exception as e:
                            self.logger.debug(f"检查可选字段 {key} 失败: {e}")
                elif key in db_date_fields:
                    # 处理日期字段
                    if value is not None:
                        update_fields.append(f"{key} = %({key})s")
                        # 确保日期格式正确
                        if hasattr(value, 'strftime'):
                            params[key] = value.strftime('%Y-%m-%d')
                        else:
                            params[key] = str(value)
                        updated_count += 1
                    else:
                        self.logger.debug(f"跳过无效指标值: {key} = {value}")
                else:
                    self.logger.debug(f"跳过数据库中不存在的字段: {key}")

            if not update_fields:
                self.logger.warning(f"股票 {stock_code} 没有有效的指标字段需要更新")
                return False

            update_sql = f"""
                UPDATE stock_primary_signals
                SET {', '.join(update_fields)}, updated_at = NOW()
                WHERE stock_code = %(stock_code)s AND is_active = true
            """

            success = self.db_manager.execute_query(update_sql, params)

            if success:
                self.logger.debug(f"股票 {stock_code} 成功更新 {updated_count} 个技术指标")
            else:
                self.logger.warning(f"股票 {stock_code} 数据库更新执行失败")

            return success

        except Exception as e:
            self.logger.error(f"更新数据库指标失败 {stock_code}: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return False

    def check_and_deactivate_signals(self):
        """检查并失效过期信号"""
        try:
            if self.signal_deactivation_manager:
                result = self.signal_deactivation_manager.check_and_deactivate_all_signals()
                self.logger.info(f"信号失效检查完成: {result}")
            else:
                self.logger.warning("信号失效管理器未初始化，跳过失效检查")

        except Exception as e:
            self.logger.error(f"信号失效检查失败: {e}")

    def generate_summary_report(self, new_signals, start_time):
        """生成总结报告"""
        try:
            elapsed_time = time.time() - start_time

            # 获取统计信息
            if self.signal_deactivation_manager:
                stats = self.signal_deactivation_manager.get_deactivation_statistics()
            else:
                stats = {'total_signals': 0, 'active_signals': 0, 'inactive_signals': 0}

            self.logger.info(f"盘后策略选股完成 (B方案):")
            self.logger.info(f"  新增信号: {len(new_signals)} 个")
            self.logger.info(f"  总信号数: {stats['total_signals']} 个")
            self.logger.info(f"  活跃信号: {stats['active_signals']} 个")
            self.logger.info(f"  执行时间: {elapsed_time:.2f} 秒")

            # 发送飞书通知
            if self.feishu_notifier and new_signals:
                try:
                    self.feishu_notifier.send_strategy_signals(new_signals)
                except Exception as e:
                    self.logger.error(f"发送飞书通知失败: {e}")

        except Exception as e:
            self.logger.error(f"生成总结报告失败: {e}")

    def _convert_to_primary_signal(self, enhanced_signal):
        """将EnhancedSignal转换为主信号表格式"""
        try:
            from datetime import datetime

            # 基础信号数据
            signal_data = {
                'signal_time': enhanced_signal.break_t2_date,
                'stock_code': enhanced_signal.stock_code,
                'stock_name': enhanced_signal.stock_name,
                'strategy_name': enhanced_signal.strategy_name,
                'start_low_date': enhanced_signal.start_low_date,
                'start_low_price': float(enhanced_signal.start_low_price),
                'stage_high_date': enhanced_signal.target_high_date,
                'stage_high_price': float(enhanced_signal.target_high_price),
                'break_t1_price': float(enhanced_signal.break_t1_price),
                'break_t2_price': float(enhanced_signal.break_t2_price),
                'signal_strength': float(enhanced_signal.signal_strength),
                'volume_ratio_t1': float(enhanced_signal.volume_ratio_t1),
                'volume_ratio_t2': float(enhanced_signal.volume_ratio_t2),
                'breakout_amplitude': float(enhanced_signal.breakout_amplitude),
                'stability_score': float(enhanced_signal.stability_score),
                'structure_score': float(enhanced_signal.structure_score),
                'is_active': True,
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            }

            # 设置默认值
            default_values = {
                'pivot_point': 0.0, 'pivot_r1': 0.0, 'pivot_s1': 0.0, 'pivot_r2': 0.0, 'pivot_s2': 0.0,
                'ema12': 0.0, 'ema62': 0.0, 'ema144': 0.0, 'ema169': 0.0, 'ema377': 0.0, 'ema576': 0.0, 'ema676': 0.0,
                'fib_level_0_236': 0.0, 'fib_level_0_382': 0.0, 'fib_level_0_5': 0.0, 'fib_level_0_618': 0.0, 'fib_level_0_786': 0.0,
                'fib_level_1_272': 0.0, 'fib_level_1_382': 0.0, 'fib_level_1_618': 0.0,
                'f_fib_level_1_272': 0.0, 'f_fib_level_1_382': 0.0, 'f_fib_level_1_618': 0.0,
                'twap': 0.0, 'vwap': 0.0, 'aroon_up': 0.0, 'aroon_down': 0.0, 'atr': 0.0, 'adx': 0.0,
                'upup_high': False, 'upup_low': False, 'downdown_high': False, 'downdown_low': False,
                'macd': 0.0, 'macd_signal': 0.0, 'macd_hist': 0.0, 'rsi': 0.0,
                'boll_upper': 0.0, 'boll_middle': 0.0, 'boll_lower': 0.0, 'obv': 0.0, 'mfi': 0.0,
                'concept': ""
            }

            signal_data.update(default_values)
            return signal_data

        except Exception as e:
            self.logger.error(f"转换EnhancedSignal格式失败: {e}")
            return None

    def _convert_regular_signal_to_primary(self, signal):
        """将普通Signal转换为主信号表格式"""
        try:
            from datetime import datetime

            # 基础信号数据
            signal_data = {
                'signal_time': getattr(signal, 'signal_time', datetime.now()),
                'stock_code': signal.stock_code,
                'stock_name': getattr(signal, 'stock_name', ''),
                'strategy_name': getattr(signal, 'strategy_name', 'unknown'),
                'start_low_date': getattr(signal, 'start_low_date', datetime.now()),
                'start_low_price': float(getattr(signal, 'start_low_price', 0.0)),
                'stage_high_date': getattr(signal, 'stage_high_date', None),
                'stage_high_price': float(getattr(signal, 'stage_high_price', 0.0)),
                'break_t1_price': float(getattr(signal, 'break_t1_price', 0.0)),
                'break_t2_price': float(getattr(signal, 'break_t2_price', 0.0)),
                'signal_strength': float(getattr(signal, 'signal_strength', 0.0)),
                'volume_ratio_t1': float(getattr(signal, 'volume_ratio_t1', 0.0)),
                'volume_ratio_t2': float(getattr(signal, 'volume_ratio_t2', 0.0)),
                'breakout_amplitude': float(getattr(signal, 'breakout_amplitude', 0.0)),
                'stability_score': float(getattr(signal, 'stability_score', 0.0)),
                'structure_score': float(getattr(signal, 'structure_score', 0.0)),
                'is_active': True,
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            }

            # 设置默认值
            default_values = {
                'pivot_point': 0.0, 'pivot_r1': 0.0, 'pivot_s1': 0.0, 'pivot_r2': 0.0, 'pivot_s2': 0.0,
                'ema12': 0.0, 'ema62': 0.0, 'ema144': 0.0, 'ema169': 0.0, 'ema377': 0.0, 'ema576': 0.0, 'ema676': 0.0,
                'fib_level_0_236': 0.0, 'fib_level_0_382': 0.0, 'fib_level_0_5': 0.0, 'fib_level_0_618': 0.0, 'fib_level_0_786': 0.0,
                'fib_level_1_272': 0.0, 'fib_level_1_382': 0.0, 'fib_level_1_618': 0.0,
                'f_fib_level_1_272': 0.0, 'f_fib_level_1_382': 0.0, 'f_fib_level_1_618': 0.0,
                'twap': 0.0, 'vwap': 0.0, 'aroon_up': 0.0, 'aroon_down': 0.0, 'atr': 0.0, 'adx': 0.0,
                'upup_high': False, 'upup_low': False, 'downdown_high': False, 'downdown_low': False,
                'macd': 0.0, 'macd_signal': 0.0, 'macd_hist': 0.0, 'rsi': 0.0,
                'boll_upper': 0.0, 'boll_middle': 0.0, 'boll_lower': 0.0, 'obv': 0.0, 'mfi': 0.0,
                'concept': ""
            }

            signal_data.update(default_values)
            return signal_data

        except Exception as e:
            self.logger.error(f"转换普通Signal格式失败: {e}")
            return None

    def _save_to_primary_signals_table(self, signal_data):
        """保存信号到主信号表"""
        try:
            # 构建插入SQL
            columns = list(signal_data.keys())
            placeholders = [f"%({col})s" for col in columns]

            insert_sql = f"""
                INSERT INTO stock_primary_signals ({', '.join(columns)})
                VALUES ({', '.join(placeholders)})
                ON CONFLICT (stock_code, strategy_name, start_low_date)
                DO UPDATE SET
                    signal_time = EXCLUDED.signal_time,
                    stage_high_date = EXCLUDED.stage_high_date,
                    stage_high_price = EXCLUDED.stage_high_price,
                    break_t1_price = EXCLUDED.break_t1_price,
                    break_t2_price = EXCLUDED.break_t2_price,
                    signal_strength = EXCLUDED.signal_strength,
                    volume_ratio_t1 = EXCLUDED.volume_ratio_t1,
                    volume_ratio_t2 = EXCLUDED.volume_ratio_t2,
                    breakout_amplitude = EXCLUDED.breakout_amplitude,
                    stability_score = EXCLUDED.stability_score,
                    structure_score = EXCLUDED.structure_score,
                    updated_at = EXCLUDED.updated_at
            """

            return self.db_manager.execute_query(insert_sql, signal_data)

        except Exception as e:
            self.logger.error(f"保存主信号到数据库失败: {e}")
            return False


def main():
    """主函数"""
    try:
        scheduler = AfterMarketScheduler()
        success = scheduler.run()

        if success:
            print("盘后策略选股执行成功")
            return 0
        else:
            print("盘后策略选股执行失败")
            return 1

    except Exception as e:
        print(f"程序执行异常: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
