#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成交量激增处理器 - 多线程数据库版本

该模块负责实时监控股票成交量激增情况，并生成相应的交易信号。
采用多线程架构和数据库技术，实现高性能的盘中策略处理。

核心功能：
1. 从stock_info表获取股票列表，多线程分工处理
2. 从数据库实时获取tick数据
3. 增量计算5分钟K线数据
4. 开盘期：当前成交量 vs 前10日同时间平均值（阈值50）
5. 盘中期：当前5分钟成交量 vs 当日之前5分钟周期平均值（阈值10）
6. 连续信号管理和飞书通知推送
7. 性能监控和执行时间统计

技术特性：
- 多线程并发处理
- 数据库高速数据访问
- 增量K线计算
- 信号去重和连续性管理
- 详细的性能监控

作者: QuantFM Team
创建时间: 2025-08-18
"""

import threading
import time
import queue
import pandas as pd
from datetime import datetime, time as dt_time, timedelta
from typing import List, Dict, Optional, Any
from collections import defaultdict
import sys
import os
import signal

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 核心组件导入
from data.db_manager import get_db_manager
from data.strategy_db_manager import get_strategy_db_manager
from services.feishu_notifier import FeishuNotifier
from config.config_manager import get_config
from utils.logger import get_logger  # 使用正确的logger导入


class VolumeSurgeProcessor:
    """
    成交量激增处理器 - 多线程数据库版本
    
    核心功能：
    1. 多线程处理股票列表
    2. 数据库实时数据获取
    3. 增量5分钟K线计算
    4. 开盘期/盘中期成交量激增检测
    5. 连续信号管理
    6. 飞书通知推送
    """
    
    def __init__(self):
        """初始化成交量激增处理器"""
        # 修复logger初始化
        self.logger = get_logger("VolumeSurgeProcessor")

        # 运行状态
        self.is_running = False
        self.stop_event = threading.Event()

        # 线程管理
        self.worker_threads = []
        self.thread_count = 4  # 默认4个工作线程
        self.signal_queue = queue.Queue()  # 信号队列

        # 数据库管理器 - 修复导入错误
        self.db_manager = get_db_manager()
        self.strategy_db = get_strategy_db_manager()

        # K线计算器已被TimescaleDB批量方法替代
        

        
        # 飞书通知器
        self.feishu_notifier = self._init_feishu_notifier()
        
        # 股票列表和分组
        self.stock_list = []
        self.stock_groups = []  # 按线程分组的股票列表
        
        # 信号管理
        self.signal_history = defaultdict(list)  # 连续信号历史
        self.last_signal_time = defaultdict(float)  # 上次信号时间
        self.signal_window = 300  # 5分钟信号窗口（秒）
        
        # 阈值配置
        self.opening_threshold = 50  # 开盘期阈值
        self.intraday_threshold = 10  # 盘中期阈值
        
        # 时间配置
        self.opening_start = dt_time(9, 30, 0)   # 开盘期开始
        self.opening_end = dt_time(9, 45, 0)     # 开盘期结束
        self.intraday_start = dt_time(9, 45, 0)  # 盘中期开始
        self.intraday_end = dt_time(15, 0, 0)    # 盘中期结束
        
        # 性能统计
        self.stats = {
            'start_time': None,
            'total_cycles': 0,
            'total_signals': 0,
            'avg_cycle_time': 0.0,
            'last_cycle_time': 0.0,
            'processed_stocks': 0,
            'errors': 0
        }
        
        # TimescaleDB K线生成配置（精简版）
        self.kline_periods = ['5min', '15min']  # 支持的K线周期

        # K线缓存
        self.kline_cache = {}  # 线程级K线缓存
        self.enable_kline_storage = True  # 启用K线数据存储
        self.execution_interval = 60  # 执行间隔（秒），每分钟执行一次
        
        self.logger.info("成交量激增处理器初始化完成")
    
    def _init_feishu_notifier(self) -> Optional[FeishuNotifier]:
        """
        初始化飞书通知器 - 从配置文件读取最新的webhook和secret

        Returns:
            FeishuNotifier实例或None
        """
        try:
            # 从配置文件读取飞书配置 - 修复硬编码问题
            config = get_config()
            feishu_config = config.get('notification', {}).get('feishu', {})

            # 优先使用notification.feishu.webhook1和secret1
            webhook_url = feishu_config.get('webhook1', '')
            secret = feishu_config.get('secret1', '')

            # 如果没有配置，尝试使用旧的feishu配置
            if not webhook_url or not secret:
                old_feishu_config = config.get('feishu', {})
                webhook_url = old_feishu_config.get('webhook_url', '')
                secret = old_feishu_config.get('secret', '')

            self.logger.info(f"飞书配置读取:")
            self.logger.info(f"  webhook_url: {webhook_url}")
            self.logger.info(f"  secret: {secret[:10]}...{secret[-5:] if len(secret) > 15 else secret}")

            if webhook_url and secret:
                # 启用签名验证 - 已修复签名算法
                use_signature = True  # 使用修复后的正确签名算法
                self.logger.info(f"✅ 飞书通知器初始化成功 - {'启用' if use_signature else '禁用'}签名验证")
                return FeishuNotifier(webhook_url, secret, self.logger, use_signature)
            else:
                self.logger.warning("⚠️ 飞书配置不完整")
                return None

        except Exception as e:
            self.logger.warning(f"⚠️ 初始化飞书通知器失败: {e}")
            import traceback
            self.logger.debug(f"错误详情: {traceback.format_exc()}")
            return None
    
    def _load_stock_list_from_db(self) -> List[Dict[str, Any]]:
        """
        从stock_info表加载所有股票列表 - 修改为加载所有股票而不是仅限市值大于10亿的股票

        Returns:
            股票信息列表
        """
        try:
            # 查询所有股票 - 移除市值和数量限制
            query = """
            SELECT stock_code, stock_name,
                   COALESCE(close, 0) as current_price,
                   0 as change_percent,
                   COALESCE(market_cap, 0) as market_cap
            FROM stock_info
            ORDER BY stock_code
            """

            results = self.db_manager.fetch_all(query)

            if results:
                stock_list = []
                for row in results:
                    # 处理字典格式的返回结果
                    if isinstance(row, dict):
                        stock_info = {
                            'code': row['stock_code'],
                            'name': row['stock_name'] or f'股票{row["stock_code"]}',
                            'current_price': float(row['current_price']) if row['current_price'] else 0.0,
                            'change_percent': float(row['change_percent']) if row['change_percent'] else 0.0,
                            'market_cap': float(row['market_cap']) if row['market_cap'] else 0.0
                        }
                    else:
                        # 处理元组格式的返回结果
                        stock_info = {
                            'code': row[0],
                            'name': row[1] or f'股票{row[0]}',
                            'current_price': float(row[2]) if row[2] else 0.0,
                            'change_percent': float(row[3]) if row[3] else 0.0,
                            'market_cap': float(row[4]) if row[4] else 0.0
                        }
                    stock_list.append(stock_info)

                self.logger.info(f"📊 从数据库加载 {len(stock_list)} 只股票（所有股票）")
                return stock_list
            else:
                self.logger.warning("⚠️ 数据库中没有找到股票数据")
                return []

        except Exception as e:
            self.logger.error(f"❌ 从数据库加载股票列表失败: {e}")
            # 返回默认测试股票列表
            return [
                {'code': '000001', 'name': '平安银行', 'current_price': 10.0, 'change_percent': 0.0, 'market_cap': 100000000000},
                {'code': '000002', 'name': '万科A', 'current_price': 8.0, 'change_percent': 0.0, 'market_cap': 80000000000},
                {'code': '600000', 'name': '浦发银行', 'current_price': 7.0, 'change_percent': 0.0, 'market_cap': 70000000000},
                {'code': '600036', 'name': '招商银行', 'current_price': 35.0, 'change_percent': 0.0, 'market_cap': 350000000000},
                {'code': '300001', 'name': '特锐德', 'current_price': 15.0, 'change_percent': 0.0, 'market_cap': 15000000000}
            ]
    
    def _split_stocks_into_groups(self, stock_list: List[Dict], thread_count: int) -> List[List[Dict]]:
        """
        将股票列表分配给多个线程
        
        Args:
            stock_list: 股票列表
            thread_count: 线程数量
            
        Returns:
            分组后的股票列表
        """
        if not stock_list:
            return []
        
        # 计算每组大小
        group_size = len(stock_list) // thread_count
        remainder = len(stock_list) % thread_count
        
        groups = []
        start_idx = 0
        
        for i in range(thread_count):
            # 前remainder个组多分配一只股票
            current_group_size = group_size + (1 if i < remainder else 0)
            end_idx = start_idx + current_group_size
            
            group = stock_list[start_idx:end_idx]
            if group:  # 只添加非空组
                groups.append(group)
            
            start_idx = end_idx
        
        self.logger.info(f"📊 股票分组完成: {len(groups)} 个线程组，每组股票数: {[len(g) for g in groups]}")
        return groups

    def start(self):
        """启动成交量激增处理器"""
        try:
            if self.is_running:
                self.logger.warning("⚠️ 处理器已在运行中")
                return

            self.logger.info("🚀 启动成交量激增处理器...")

            # 加载股票列表
            self.stock_list = self._load_stock_list_from_db()
            if not self.stock_list:
                self.logger.error("❌ 无法加载股票列表，启动失败")
                return

            # 分组股票
            self.stock_groups = self._split_stocks_into_groups(self.stock_list, self.thread_count)
            if not self.stock_groups:
                self.logger.error("❌ 股票分组失败，启动失败")
                return

            # 使用数据库模式获取数据
            self.logger.info("✅ 使用数据库模式获取数据")

            # 启动工作线程
            self._start_worker_threads()

            # 启动信号处理线程
            self._start_signal_processor()

            # 设置运行状态
            self.is_running = True
            self.stats['start_time'] = datetime.now()

            self.logger.info(f"✅ 成交量激增处理器启动成功，监控 {len(self.stock_list)} 只股票")

        except Exception as e:
            self.logger.error(f"❌ 启动处理器失败: {e}")
            self.stop()

    def stop(self):
        """停止成交量激增处理器"""
        try:
            if not self.is_running:
                return

            self.logger.info("🛑 停止成交量激增处理器...")

            # 设置停止标志
            self.is_running = False
            self.stop_event.set()

            # 等待工作线程结束
            for thread in self.worker_threads:
                if thread.is_alive():
                    thread.join(timeout=5)


            self.worker_threads.clear()

            # 输出统计信息
            self._log_final_stats()

            self.logger.info("✅ 成交量激增处理器已停止")

        except Exception as e:
            self.logger.error(f"❌ 停止处理器失败: {e}")



    def _start_worker_threads(self):
        """启动工作线程"""
        try:
            self.worker_threads.clear()

            for i, stock_group in enumerate(self.stock_groups):
                # 数据库模式：传递None，线程内部会使用数据库
                reader = None
                mode = "数据库"

                thread = threading.Thread(
                    target=self._worker_thread,
                    args=(i, stock_group, reader),
                    name=f"VolumeSurgeWorker-{i}"
                )
                thread.daemon = True
                thread.start()
                self.worker_threads.append(thread)
                self.logger.info(f"✅ 启动工作线程 {i}，处理 {len(stock_group)} 只股票 (模式: {mode})")

            self.logger.info(f"✅ 所有 {len(self.worker_threads)} 个工作线程启动完成")

        except Exception as e:
            self.logger.error(f"❌ 启动工作线程失败: {e}")

    def _start_signal_processor(self):
        """启动信号处理线程"""
        try:
            signal_thread = threading.Thread(
                target=self._signal_processor_thread,
                name="SignalProcessor"
            )
            signal_thread.daemon = True
            signal_thread.start()

            self.logger.info("✅ 信号处理线程启动完成")

        except Exception as e:
            self.logger.error(f"❌ 启动信号处理线程失败: {e}")

    def _worker_thread(self, thread_id: int, stock_group: List[Dict], reader: Optional[None]):
        """
        工作线程 - 处理分配的股票组

        Args:
            thread_id: 线程ID
            stock_group: 分配的股票组
            reader: 保留参数兼容性（未使用）
        """
        self.logger.info(f"🔄 工作线程 {thread_id} 开始运行，处理 {len(stock_group)} 只股票")

        # 初始化线程级别的K线缓存
        self.kline_cache[thread_id] = {}

        cycle_count = 0

        try:
            while not self.stop_event.is_set():
                cycle_start_time = time.time()

                # 检查是否为交易时间
                current_time = datetime.now().time()

                # 检查是否到达15:05，直接退出进程
                if current_time >= dt_time(15, 5, 0):
                    self.logger.info(f"🔄 线程 {thread_id} 到达15:05收盘时间，准备退出进程")
                    self._trigger_process_exit()
                    break

                if not self._is_trading_time(current_time):
                    # 非交易时间，进入休市暂停模式
                    self._enter_market_pause_mode(thread_id)
                    # 重新设置cycle_start_time，避免统计错误
                    cycle_start_time = time.time()
                    continue

                # 使用批量处理优化性能
                try:
                    self._batch_process_stocks(thread_id, stock_group, current_time)
                except Exception as e:
                    self.logger.debug(f"批量处理股票失败: {e}")
                    # 降级为单只股票处理
                    for stock_info in stock_group:
                        try:
                            self._process_single_stock(thread_id, stock_info, reader, current_time)
                        except Exception as e:
                            self.logger.debug(f"处理股票 {stock_info['code']} 失败: {e}")
                            self.stats['errors'] += 1

                # 更新统计信息
                cycle_time = time.time() - cycle_start_time
                self.stats['last_cycle_time'] = cycle_time
                self.stats['total_cycles'] += 1

                # 计算平均周期时间
                if self.stats['total_cycles'] > 0:
                    self.stats['avg_cycle_time'] = (
                        (self.stats['avg_cycle_time'] * (self.stats['total_cycles'] - 1) + cycle_time)
                        / self.stats['total_cycles']
                    )

                cycle_count += 1

                # 每10个周期输出一次性能统计（1分钟执行频率下，相当于每10分钟输出一次）
                if cycle_count % 10 == 0:
                    self.logger.info(f"🔄 线程 {thread_id} 性能统计: "
                                   f"周期={cycle_count}, "
                                   f"平均周期时间={self.stats['avg_cycle_time']:.3f}s, "
                                   f"最近周期时间={cycle_time:.3f}s")

                # 高效的整分钟执行，降低系统负载
                self._sleep_until_next_minute()

        except Exception as e:
            self.logger.error(f"❌ 工作线程 {thread_id} 异常: {e}")
        finally:
            self.logger.info(f"🛑 工作线程 {thread_id} 结束运行")

    def _sleep_until_next_minute(self):
        """
        高效的整分钟等待 - 精确等待到下一个整分钟

        优势：
        1. 所有线程同步在整分钟执行
        2. 时间可预测，便于监控和调试
        3. 与K线时间对齐，逻辑更清晰
        """
        try:
            current_time = datetime.now()

            # 计算到下一个整分钟的秒数
            seconds_to_next_minute = 60 - current_time.second - current_time.microsecond / 1000000

            # 如果已经是整分钟（误差在0.1秒内），等待到下一分钟
            if seconds_to_next_minute < 0.1:
                seconds_to_next_minute += 60

            # 记录等待信息（仅在首次或每小时记录一次，避免日志过多）
            if current_time.minute == 0 or not hasattr(self, '_last_sleep_log'):
                next_execution = current_time + timedelta(seconds=seconds_to_next_minute)
                self.logger.info(f"⏰ 等待到下一个整分钟执行: {next_execution.strftime('%H:%M:%S')}")
                self._last_sleep_log = current_time.hour

            # 精确等待到下一个整分钟
            time.sleep(seconds_to_next_minute)

        except Exception as e:
            self.logger.warning(f"⚠️ 整分钟等待失败，使用固定间隔: {e}")
            # 降级为固定间隔
            time.sleep(self.execution_interval)

    def _batch_process_stocks(self, thread_id: int, stock_group: List[Dict], current_time: dt_time):
        """
        批量处理股票组 - 使用TimescaleDB批量获取K线数据

        Args:
            thread_id: 线程ID
            stock_group: 股票组
            current_time: 当前时间
        """
        try:
            # 提取股票代码列表
            stock_codes = [stock['code'] for stock in stock_group]

            # 批量获取K线数据
            batch_klines = self._get_batch_klines_timescale(stock_codes, ['5min'])

            # 处理每只股票
            for stock_info in stock_group:
                stock_code = stock_info['code']

                try:
                    # 检查数据完整性
                    if not self._check_data_integrity(stock_code, None):
                        continue

                    # 根据时间段选择处理策略
                    if self._is_opening_period(current_time):
                        # 开盘期处理
                        self._process_opening_period(thread_id, stock_info, None)
                    elif self._is_intraday_period(current_time):
                        # 盘中期处理 - 使用批量获取的K线数据
                        self._process_intraday_period_with_kline(
                            thread_id, stock_info, batch_klines.get(stock_code, {}).get('5min')
                        )

                    self.stats['processed_stocks'] += 1

                except Exception as e:
                    self.logger.debug(f"批量处理中单只股票 {stock_code} 失败: {e}")
                    self.stats['errors'] += 1

        except Exception as e:
            self.logger.error(f"批量处理股票组失败: {e}")
            raise  # 重新抛出异常，让调用者处理降级逻辑

    def _process_intraday_period_with_kline(self, thread_id: int, stock_info: Dict, current_kline: Optional[Dict]):
        """
        使用预获取的K线数据处理盘中期成交量激增检测

        Args:
            thread_id: 线程ID
            stock_info: 股票信息
            current_kline: 预获取的K线数据
        """
        stock_code = stock_info['code']

        try:
            if not current_kline or current_kline['volume'] <= 0:
                return

            # 获取当日之前5分钟周期的平均成交量
            intraday_avg = self._get_intraday_average_volume(stock_code)
            if intraday_avg <= 0:
                return

            # 计算成交量比值
            volume_ratio = current_kline['volume'] / intraday_avg

            # 检查是否达到阈值
            if volume_ratio >= self.intraday_threshold:
                # 检查信号窗口
                signal_key = f"{stock_code}_intraday"
                current_time = time.time()

                if self._should_generate_signal(signal_key, current_time):
                    # 生成信号
                    signal_data = {
                        'stock_code': stock_code,
                        'stock_name': stock_info['name'],
                        'current_price': current_kline['close'],
                        'change_percent': ((current_kline['close'] - current_kline['open']) / current_kline['open'] * 100) if current_kline['open'] > 0 else 0,
                        'signal_type': 'intraday',
                        'volume_ratio': volume_ratio,
                        'current_volume': current_kline['volume'],
                        'historical_avg': intraday_avg,
                        'threshold': self.intraday_threshold,
                        'timestamp': datetime.now(),
                        'continuous_count': self._get_continuous_count(signal_key),
                        'kline_period': current_kline['period']
                    }

                    # 发送到信号队列
                    self.signal_queue.put(signal_data)

                    # 更新信号历史
                    self._update_signal_history(signal_key, current_time)

                    self.logger.info(f"🔥 盘中期激增信号: {stock_code} {stock_info['name']} "
                                   f"比值={volume_ratio:.1f}x (阈值={self.intraday_threshold})")

        except Exception as e:
            self.logger.debug(f"使用预获取K线处理盘中期股票 {stock_code} 失败: {e}")

    def _process_single_stock(self, thread_id: int, stock_info: Dict, reader: Optional[None], current_time: dt_time):
        """
        处理单只股票

        Args:
            thread_id: 线程ID
            stock_info: 股票信息
            reader: 保留参数兼容性（未使用）
            current_time: 当前时间
        """
        stock_code = stock_info['code']

        try:
            # 检查最近两个5分钟周期的数据完整性
            if not self._check_data_integrity(stock_code, reader):
                return

            # 根据时间段选择处理策略
            if self._is_opening_period(current_time):
                # 开盘期处理
                self._process_opening_period(thread_id, stock_info, reader)
            elif self._is_intraday_period(current_time):
                # 盘中期处理
                self._process_intraday_period(thread_id, stock_info, reader)

            self.stats['processed_stocks'] += 1

        except Exception as e:
            self.logger.debug(f"处理股票 {stock_code} 失败: {e}")
            self.stats['errors'] += 1

    def _check_data_integrity(self, stock_code: str, reader: Optional[None]) -> bool:
        """
        检查最近两个5分钟周期的数据完整性

        Args:
            stock_code: 股票代码
            reader: 保留参数兼容性（未使用）

        Returns:
            数据是否完整
        """
        try:
            # 数据库模式：简化检查，假设数据完整
            return True

        except Exception as e:
            self.logger.debug(f"检查股票 {stock_code} 数据完整性失败: {e}")
            return False

    def _process_opening_period(self, thread_id: int, stock_info: Dict, reader: Optional[None]):
        """
        处理开盘期成交量激增检测

        Args:
            thread_id: 线程ID
            stock_info: 股票信息
            reader: 保留参数兼容性（未使用）
        """
        stock_code = stock_info['code']

        try:
            # 数据库模式：从stock_tick_data表获取当天累计成交量
            current_volume = self._get_current_volume_from_db(stock_code)

            if current_volume <= 0:
                return

            # 获取前10日同时间段平均成交量
            historical_avg = self._get_opening_historical_average(stock_code)
            if historical_avg <= 0:
                return

            # 计算成交量比值
            volume_ratio = current_volume / historical_avg

            # 检查是否达到阈值
            if volume_ratio >= self.opening_threshold:
                # 检查信号窗口
                signal_key = f"{stock_code}_opening"
                current_time = time.time()

                if self._should_generate_signal(signal_key, current_time):
                    # 生成信号
                    signal_data = {
                        'stock_code': stock_code,
                        'stock_name': stock_info['name'],
                        'current_price': stock_info['current_price'],
                        'change_percent': stock_info['change_percent'],
                        'signal_type': 'opening',
                        'volume_ratio': volume_ratio,
                        'current_volume': current_volume,
                        'historical_avg': historical_avg,
                        'threshold': self.opening_threshold,
                        'timestamp': datetime.now(),
                        'continuous_count': self._get_continuous_count(signal_key)
                    }

                    # 发送到信号队列
                    self.signal_queue.put(signal_data)

                    # 更新信号历史
                    self._update_signal_history(signal_key, current_time)

                    self.logger.info(f"🔥 开盘期激增信号: {stock_code} {stock_info['name']} "
                                   f"比值={volume_ratio:.1f}x (阈值={self.opening_threshold})")

        except Exception as e:
            self.logger.debug(f"处理开盘期股票 {stock_code} 失败: {e}")

    def _process_intraday_period(self, thread_id: int, stock_info: Dict, reader: Optional[None]):
        """
        处理盘中期成交量激增检测 - 使用TimescaleDB优化版本

        Args:
            thread_id: 线程ID
            stock_info: 股票信息
            reader: 保留参数兼容性（未使用）
        """
        stock_code = stock_info['code']

        try:
            # 使用新的TimescaleDB方式获取K线数据
            current_kline = self._get_current_kline_timescale(stock_code, '5min')
            if not current_kline or current_kline['volume'] <= 0:
                return

            # 获取当日之前5分钟周期的平均成交量
            intraday_avg = self._get_intraday_average_volume(stock_code)
            if intraday_avg <= 0:
                return

            # 计算成交量比值
            volume_ratio = current_kline['volume'] / intraday_avg

            # 检查是否达到阈值
            if volume_ratio >= self.intraday_threshold:
                # 检查信号窗口
                signal_key = f"{stock_code}_intraday"
                current_time = time.time()

                if self._should_generate_signal(signal_key, current_time):
                    # 生成信号
                    signal_data = {
                        'stock_code': stock_code,
                        'stock_name': stock_info['name'],
                        'current_price': current_kline['close'],
                        'change_percent': ((current_kline['close'] - current_kline['open']) / current_kline['open'] * 100) if current_kline['open'] > 0 else 0,
                        'signal_type': 'intraday',
                        'volume_ratio': volume_ratio,
                        'current_volume': current_kline['volume'],
                        'historical_avg': intraday_avg,
                        'threshold': self.intraday_threshold,
                        'timestamp': datetime.now(),
                        'continuous_count': self._get_continuous_count(signal_key),
                        'kline_period': current_kline['period']
                    }

                    # 发送到信号队列
                    self.signal_queue.put(signal_data)

                    # 更新信号历史
                    self._update_signal_history(signal_key, current_time)

                    self.logger.info(f"🔥 盘中期激增信号: {stock_code} {stock_info['name']} "
                                   f"比值={volume_ratio:.1f}x (阈值={self.intraday_threshold})")

        except Exception as e:
            self.logger.debug(f"处理盘中期股票 {stock_code} 失败: {e}")

    def _get_opening_historical_average(self, stock_code: str) -> float:
        """
        获取前10日开盘期同时间段平均成交量

        Args:
            stock_code: 股票代码

        Returns:
            历史平均成交量
        """
        try:
            current_time = datetime.now()
            current_minute = current_time.hour * 60 + current_time.minute

            # 查询前10个交易日同时间段的成交量
            query = """
            SELECT AVG(daily_volume) as avg_volume
            FROM (
                SELECT DATE(trade_time) as trade_date, SUM(cur_vol) as daily_volume
                FROM stock_tick_data
                WHERE stock_code = %s
                  AND EXTRACT(HOUR FROM trade_time) * 60 + EXTRACT(MINUTE FROM trade_time) <= %s
                  AND EXTRACT(HOUR FROM trade_time) >= 9 AND EXTRACT(MINUTE FROM trade_time) >= 30
                  AND trade_time >= CURRENT_DATE - INTERVAL '10 days'
                  AND trade_time < CURRENT_DATE
                GROUP BY DATE(trade_time)
                HAVING COUNT(*) > 10  -- 确保有足够的数据点
            ) daily_stats
            """

            result = self.db_manager.fetch_one(query, (stock_code, current_minute))

            if result and result[0]:
                return float(result[0])
            else:
                return 1000.0  # 默认值

        except Exception as e:
            self.logger.debug(f"获取股票 {stock_code} 开盘期历史平均失败: {e}")
            return 1000.0

    def _get_intraday_average_volume(self, stock_code: str) -> float:
        """
        获取当日之前5分钟周期的平均成交量

        Args:
            stock_code: 股票代码

        Returns:
            当日平均成交量
        """
        try:
            # 查询当日之前所有5分钟周期的成交量
            query = """
            SELECT AVG(bucket_volume) as avg_volume
            FROM (
                SELECT time_bucket('5 minutes', trade_time) as bucket,
                       SUM(cur_vol) as bucket_volume
                FROM stock_tick_data
                WHERE stock_code = %s
                  AND DATE(trade_time) = CURRENT_DATE
                  AND trade_time < NOW() - INTERVAL '5 minutes'  -- 排除当前周期
                GROUP BY bucket
                HAVING SUM(cur_vol) > 0
            ) bucket_stats
            """

            result = self.db_manager.fetch_one(query, (stock_code,))

            if result and result[0]:
                return float(result[0])
            else:
                return 1000.0  # 默认值

        except Exception as e:
            self.logger.debug(f"获取股票 {stock_code} 盘中期平均失败: {e}")
            return 1000.0

    def _get_current_kline_timescale(self, stock_code: str, period: str = '5min') -> Optional[Dict]:
        """
        使用TimescaleDB的time_bucket获取当前K线数据 - 高性能版本

        Args:
            stock_code: 股票代码
            period: K线周期 ('5min' 或 '15min')

        Returns:
            当前K线数据
        """
        try:
            current_time = datetime.now()

            # 根据周期计算查询范围
            if period == '5min':
                minutes_back = 10  # 查询最近10分钟
                bucket_interval = '5 minutes'
            elif period == '15min':
                minutes_back = 30  # 查询最近30分钟
                bucket_interval = '15 minutes'
            else:
                self.logger.warning(f"不支持的K线周期: {period}")
                return None

            period_start = current_time - timedelta(minutes=minutes_back)

            # 使用TimescaleDB的time_bucket直接生成K线（向后靠齐）
            sql = """
            SELECT
                stock_code,
                time_bucket(%s, trade_time) + INTERVAL %s AS period_time,  -- 向后靠齐
                FIRST(price, trade_time) AS open,
                MAX(price) AS high,
                MIN(price) AS low,
                LAST(price, trade_time) AS close,
                SUM(cur_vol) AS volume,
                SUM(amount) AS amount,
                COUNT(*) AS tick_count
            FROM stock_tick_data
            WHERE stock_code = %s
              AND trade_time >= %s
              AND trade_time <= %s
            GROUP BY stock_code, time_bucket(%s, trade_time)
            ORDER BY period_time DESC
            LIMIT 1
            """

            result = self.db_manager.fetch_one(sql, (
                bucket_interval, bucket_interval, stock_code, period_start, current_time, bucket_interval
            ))

            if result and result['volume'] and result['volume'] > 0:
                kline_data = {
                    'stock_code': stock_code,
                    'period': result['period_time'].strftime('%H:%M'),
                    'period_time': result['period_time'],
                    'open': float(result['open']),
                    'high': float(result['high']),
                    'low': float(result['low']),
                    'close': float(result['close']),
                    'volume': int(result['volume']),
                    'amount': float(result['amount']),
                    'tick_count': int(result['tick_count']),
                    'last_update': current_time
                }

                self.logger.debug(f"TimescaleDB获取 {stock_code} {period}K线: "
                               f"成交量={kline_data['volume']}, tick数={kline_data['tick_count']}")
                return kline_data
            else:
                self.logger.debug(f"TimescaleDB未获取到 {stock_code} {period}K线数据")
                return None

        except Exception as e:
            self.logger.debug(f"TimescaleDB获取 {stock_code} {period}K线失败: {e}")
            return None

    def _get_batch_klines_timescale(self, stock_codes: List[str], periods: List[str] = None) -> Dict[str, Dict[str, Dict]]:
        """
        批量获取多只股票的多周期K线数据 - 超高性能版本

        Args:
            stock_codes: 股票代码列表
            periods: K线周期列表，默认为['5min', '15min']

        Returns:
            {stock_code: {period: kline_data}}
        """
        if not stock_codes:
            return {}

        if periods is None:
            periods = self.kline_periods

        try:
            current_time = datetime.now()
            period_start = current_time - timedelta(minutes=20)  # 查询最近20分钟（适配1分钟执行频率）

            # 构建股票代码列表
            stock_codes_str = "','".join(stock_codes)

            # 构建多周期查询的UNION语句
            union_queries = []
            params = []

            for period in periods:
                if period == '5min':
                    bucket_interval = '5 minutes'
                elif period == '15min':
                    bucket_interval = '15 minutes'
                else:
                    continue

                query = f"""
                SELECT
                    stock_code,
                    '{period}' as period_type,
                    time_bucket('{bucket_interval}', trade_time) + INTERVAL '{bucket_interval}' AS period_time,
                    FIRST(price, trade_time) AS open,
                    MAX(price) AS high,
                    MIN(price) AS low,
                    LAST(price, trade_time) AS close,
                    SUM(cur_vol) AS volume,
                    SUM(amount) AS amount,
                    COUNT(*) AS tick_count
                FROM stock_tick_data
                WHERE stock_code IN ('{stock_codes_str}')
                  AND trade_time >= %s
                  AND trade_time <= %s
                GROUP BY stock_code, time_bucket('{bucket_interval}', trade_time)
                HAVING SUM(cur_vol) > 0
                """
                union_queries.append(query)
                params.extend([period_start, current_time])

            if not union_queries:
                return {}

            # 合并所有查询
            final_sql = " UNION ALL ".join(union_queries) + " ORDER BY stock_code, period_type, period_time DESC"

            results = self.db_manager.fetch_all(final_sql, params)

            # 组织结果数据
            klines_data = {}
            for row in results:
                stock_code = row['stock_code']
                period_type = row['period_type']

                if stock_code not in klines_data:
                    klines_data[stock_code] = {}

                if period_type not in klines_data[stock_code]:
                    # 只保留最新的K线数据
                    klines_data[stock_code][period_type] = {
                        'stock_code': stock_code,
                        'period': row['period_time'].strftime('%H:%M'),
                        'period_time': row['period_time'],
                        'open': float(row['open']),
                        'high': float(row['high']),
                        'low': float(row['low']),
                        'close': float(row['close']),
                        'volume': int(row['volume']),
                        'amount': float(row['amount']),
                        'tick_count': int(row['tick_count']),
                        'last_update': current_time
                    }

            self.logger.debug(f"批量获取K线: {len(stock_codes)}只股票, "
                           f"获得{sum(len(periods) for periods in klines_data.values())}条K线")

            # 直接保存K线数据到对应的K线表
            if klines_data:
                self._save_klines_to_tables(klines_data)

            return klines_data

        except Exception as e:
            self.logger.error(f"批量获取K线数据失败: {e}")
            return {}

    def _save_klines_to_tables(self, klines_data: Dict[str, Dict[str, Dict]]):
        """
        将K线数据批量保存到对应的K线表中

        Args:
            klines_data: {stock_code: {period: kline_data}}
        """
        try:
            # 按周期分组K线数据，准备批量插入
            period_groups = {}

            for stock_code, periods_data in klines_data.items():
                for period, kline_data in periods_data.items():
                    if period not in period_groups:
                        period_groups[period] = []

                    # 准备插入数据
                    insert_data = {
                        'stock_code': kline_data['stock_code'],
                        'trade_time': kline_data['period_time'],
                        'open': kline_data['open'],
                        'high': kline_data['high'],
                        'low': kline_data['low'],
                        'close': kline_data['close'],
                        'volume': kline_data['volume'],
                        'amount': kline_data['amount']
                    }
                    period_groups[period].append(insert_data)

            # 批量插入每个周期的K线数据
            for period, klines_list in period_groups.items():
                if not klines_list:
                    continue

                table_name = f"stock_kline_{period}"

                # 使用批量插入提高性能
                success = self.db_manager.insert_many(
                    table_name,
                    klines_list,
                    on_conflict="(trade_time, stock_code) DO UPDATE SET "
                              "open = EXCLUDED.open, "
                              "high = EXCLUDED.high, "
                              "low = EXCLUDED.low, "
                              "close = EXCLUDED.close, "
                              "volume = EXCLUDED.volume, "
                              "amount = EXCLUDED.amount",
                    batch_size=1000
                )

                if success:
                    self.logger.info(f"✅ 批量保存{period}K线: {len(klines_list)}条记录")
                else:
                    self.logger.warning(f"⚠️ 保存{period}K线失败: {len(klines_list)}条记录")

        except Exception as e:
            self.logger.error(f"❌ 批量保存K线数据失败: {e}")
            import traceback
            self.logger.debug(f"错误详情: {traceback.format_exc()}")

    # 废弃方法：_get_or_update_current_kline 已被TimescaleDB批量方法替代

    # 废弃方法：_get_period_ticks_from_db 已被TimescaleDB批量方法替代

    def _get_current_volume_from_db(self, stock_code: str) -> int:
        """
        从数据库获取当前累计成交量（降级方案）

        Args:
            stock_code: 股票代码

        Returns:
            当前累计成交量
        """
        try:
            from datetime import date
            today = date.today()

            # 获取当天的最新累计成交量（volume字段本身就是累计值）
            sql = """
            SELECT volume as total_volume
            FROM stock_tick_data
            WHERE stock_code = %s
              AND DATE(trade_time) = %s
            ORDER BY trade_time DESC
            LIMIT 1
            """

            result = self.db_manager.fetch_one(sql, (stock_code, today))

            if result and result['total_volume']:
                total_volume = int(result['total_volume'])
                self.logger.debug(f"从数据库获取 {stock_code} 当天累计成交量: {total_volume}")
                return total_volume
            else:
                return 0

        except Exception as e:
            self.logger.debug(f"从数据库获取当前累计成交量失败 {stock_code}: {e}")
            return 0

    def _should_generate_signal(self, signal_key: str, current_time: float) -> bool:
        """
        检查是否应该生成信号（一个周期内只出一个信号）

        Args:
            signal_key: 信号键值
            current_time: 当前时间戳

        Returns:
            是否应该生成信号
        """
        last_time = self.last_signal_time.get(signal_key, 0)

        # 检查是否在信号窗口内
        if current_time - last_time >= self.signal_window:
            return True

        return False

    def _update_signal_history(self, signal_key: str, current_time: float):
        """
        更新信号历史

        Args:
            signal_key: 信号键值
            current_time: 当前时间戳
        """
        self.last_signal_time[signal_key] = current_time

        # 更新连续信号历史
        if signal_key not in self.signal_history:
            self.signal_history[signal_key] = deque(maxlen=10)  # 最多保留10个历史信号

        self.signal_history[signal_key].append(current_time)

    def _get_continuous_count(self, signal_key: str) -> int:
        """
        获取连续信号次数

        Args:
            signal_key: 信号键值

        Returns:
            连续信号次数
        """
        if signal_key not in self.signal_history:
            return 1

        history = self.signal_history[signal_key]
        if not history:
            return 1

        # 计算连续信号（时间间隔小于2个信号窗口的认为是连续的）
        continuous_count = 1
        current_time = time.time()

        for i in range(len(history) - 1, -1, -1):
            if current_time - history[i] <= self.signal_window * 2:
                continuous_count += 1
            else:
                break

        return continuous_count

    def _signal_processor_thread(self):
        """信号处理线程"""
        self.logger.info("🔄 信号处理线程开始运行")

        try:
            while not self.stop_event.is_set():
                try:
                    # 从队列获取信号（超时1秒）
                    signal_data = self.signal_queue.get(timeout=1)

                    # 处理信号
                    self._process_signal(signal_data)

                    # 标记任务完成
                    self.signal_queue.task_done()

                except queue.Empty:
                    continue
                except Exception as e:
                    self.logger.error(f"❌ 处理信号失败: {e}")

        except Exception as e:
            self.logger.error(f"❌ 信号处理线程异常: {e}")
        finally:
            self.logger.info("🛑 信号处理线程结束运行")

    def _process_signal(self, signal_data: Dict):
        """
        处理信号

        Args:
            signal_data: 信号数据
        """
        try:
            # 发送飞书通知
            if self.feishu_notifier:
                self._send_feishu_notification(signal_data)

            # 记录到数据库
            self._save_signal_to_db(signal_data)

            # 更新统计
            self.stats['total_signals'] += 1

            self.logger.info(f"✅ 信号处理完成: {signal_data['stock_code']} {signal_data['stock_name']}")

        except Exception as e:
            self.logger.error(f"❌ 处理信号失败: {e}")

    def _send_feishu_notification(self, signal_data: Dict):
        """
        发送飞书通知 - 修复消息格式以匹配FeishuNotifier.send_volume_surge_alert的期望格式

        Args:
            signal_data: 信号数据
        """
        try:
            # 构造符合send_volume_surge_alert期望格式的通知消息
            signal_type_name = "开盘期激增" if signal_data['signal_type'] == 'opening' else "盘中期激增"

            # 转换为send_volume_surge_alert期望的格式
            formatted_signal_data = {
                'stock_code': signal_data['stock_code'],
                'signal_type': 'OPENING' if signal_data['signal_type'] == 'opening' else 'INTRADAY',
                'surge_ratio': signal_data['volume_ratio'],  # 成交量比值
                'current_volume': signal_data['current_volume'],
                'historical_avg_volume': signal_data.get('historical_avg', 0),
                'confidence': 0.8,  # 默认置信度
                'continuous_count': signal_data['continuous_count'],
                'timestamp': signal_data['timestamp'],
                'period_info': signal_data.get('kline_period', signal_type_name)
            }

            # 发送通知 - 添加空检查
            if self.feishu_notifier:
                self.feishu_notifier.send_volume_surge_alert(formatted_signal_data)
            else:
                self.logger.warning("⚠️ 飞书通知器未初始化，跳过通知发送")

        except Exception as e:
            self.logger.error(f"❌ 发送飞书通知失败: {e}")
            # 记录详细的错误信息用于调试
            self.logger.debug(f"信号数据: {signal_data}")
            import traceback
            self.logger.debug(f"错误详情: {traceback.format_exc()}")

    def _save_signal_to_db(self, signal_data: Dict):
        """
        保存信号到数据库

        Args:
            signal_data: 信号数据
        """
        try:
            # 这里可以添加数据库保存逻辑
            # 暂时只记录日志
            self.logger.debug(f"保存信号到数据库: {signal_data['stock_code']}")

        except Exception as e:
            self.logger.error(f"❌ 保存信号到数据库失败: {e}")

    def _enter_market_pause_mode(self, thread_id: int):
        """
        进入休市暂停模式 - 高效的非交易时间处理

        Args:
            thread_id: 线程ID
        """
        current_time = datetime.now().time()

        # 判断当前处于哪个休市时段
        if current_time < dt_time(9, 25, 0):
            # 早盘前休市
            next_trading_time = dt_time(9, 25, 0)
            pause_type = "早盘前"
        elif dt_time(11, 30, 0) <= current_time < dt_time(13, 0, 0):
            # 午休时间
            next_trading_time = dt_time(13, 0, 0)
            pause_type = "午休"
        elif current_time > dt_time(15, 5, 0):
            # 收盘后休市
            next_trading_time = dt_time(9, 25, 0)  # 次日早盘
            pause_type = "收盘后"
        else:
            # 其他时间，短暂休眠
            time.sleep(10)
            return

        # 计算到下次交易时间的秒数
        now = datetime.now()
        if pause_type == "收盘后":
            # 次日早盘
            next_trading_datetime = datetime.combine(
                now.date() + timedelta(days=1),
                next_trading_time
            )
        else:
            # 当日
            next_trading_datetime = datetime.combine(now.date(), next_trading_time)

        sleep_seconds = (next_trading_datetime - now).total_seconds()

        # 限制最大休眠时间为1小时，避免过长等待
        max_sleep = 3600  # 1小时
        actual_sleep = min(sleep_seconds, max_sleep)

        if actual_sleep > 60:  # 超过1分钟才记录日志
            self.logger.info(f"🔄 线程 {thread_id} 进入{pause_type}休市暂停模式，"
                           f"将在 {actual_sleep/60:.1f} 分钟后恢复运行")

        time.sleep(actual_sleep)

    def _is_trading_time(self, current_time: dt_time) -> bool:
        """
        检查是否为交易时间 - 精确的交易时间判断

        交易时间：
        - 早盘：09:25:00 - 11:30:00
        - 午盘：13:00:00 - 15:05:00
        """
        # 早盘时间
        if dt_time(9, 25, 0) <= current_time <= dt_time(11, 30, 0):
            return True

        # 午盘时间
        if dt_time(13, 0, 0) <= current_time <= dt_time(15, 5, 0):
            return True

        return False

    def _trigger_process_exit(self):
        """
        触发进程退出 - 15:05收盘后完全清除资源
        """
        self.logger.info("🛑 VolumeSurgeProcessor到达15:05收盘时间，开始清理资源并退出进程")

        try:
            # 1. 设置停止标志
            self.is_running = False
            self.stop_event.set()

            # 2. 记录最终统计
            self._log_final_stats()

            # 3. 清理所有资源
            self._cleanup_all_resources()

            # 4. 强制退出进程
            self.logger.info("🔥 VolumeSurgeProcessor进程即将强制退出")

            # 使用os._exit()强制退出，完全清除进程资源
            import os
            os._exit(0)

        except Exception as e:
            self.logger.error(f"❌ 进程退出过程中发生异常: {e}")
            # 即使发生异常也要强制退出
            import os
            os._exit(1)

    def _cleanup_all_resources(self):
        """
        清理所有资源 - 确保完全释放
        """
        try:
            self.logger.info("🧹 开始清理VolumeSurgeProcessor所有资源...")

            # 1. 停止所有工作线程
            for thread in self.worker_threads:
                if thread.is_alive():
                    try:
                        thread.join(timeout=2)  # 最多等待2秒
                    except:
                        pass



            # 3. 清理数据库连接
            try:
                if hasattr(self, 'db_manager') and self.db_manager:
                    self.db_manager.close_all_connections()
            except:
                pass

            try:
                if hasattr(self, 'strategy_db') and self.strategy_db:
                    self.strategy_db.close_all_connections()
            except:
                pass

            # 4. 清理队列
            try:
                while not self.signal_queue.empty():
                    self.signal_queue.get_nowait()
            except:
                pass

            # 5. 清理缓存
            self.worker_threads.clear()
            self.stock_list.clear()
            self.stock_groups.clear()
            self.signal_history.clear()
            self.last_signal_time.clear()

            self.logger.info("✅ VolumeSurgeProcessor资源清理完成")

        except Exception as e:
            self.logger.error(f"❌ 资源清理过程中发生异常: {e}")
            # 继续执行，不阻止进程退出

    def _is_opening_period(self, current_time: dt_time) -> bool:
        """检查是否为开盘期"""
        return self.opening_start <= current_time < self.opening_end

    def _is_intraday_period(self, current_time: dt_time) -> bool:
        """检查是否为盘中期"""
        return self.intraday_start <= current_time < self.intraday_end

    def _log_final_stats(self):
        """输出最终统计信息"""
        try:
            if self.stats['start_time']:
                runtime = datetime.now() - self.stats['start_time']

                self.logger.info("📊 最终统计信息:")
                self.logger.info(f"   运行时间: {runtime}")
                self.logger.info(f"   总周期数: {self.stats['total_cycles']}")
                self.logger.info(f"   总信号数: {self.stats['total_signals']}")
                self.logger.info(f"   平均周期时间: {self.stats['avg_cycle_time']:.3f}秒")
                self.logger.info(f"   处理股票数: {self.stats['processed_stocks']}")
                self.logger.info(f"   错误次数: {self.stats['errors']}")

                if self.stats['total_cycles'] > 0:
                    self.logger.info(f"   平均执行一轮时间: {self.stats['avg_cycle_time']:.3f}秒")

        except Exception as e:
            self.logger.error(f"❌ 输出统计信息失败: {e}")

    def get_status(self) -> Dict[str, Any]:
        """获取处理器状态"""
        return {
            'is_running': self.is_running,
            'start_time': self.stats['start_time'],
            'stock_count': len(self.stock_list),
            'thread_count': len(self.worker_threads),
            'total_cycles': self.stats['total_cycles'],
            'total_signals': self.stats['total_signals'],
            'avg_cycle_time': self.stats['avg_cycle_time'],
            'processed_stocks': self.stats['processed_stocks'],
            'errors': self.stats['errors']
        }


def main():
    """主函数"""
    processor = VolumeSurgeProcessor()

    def signal_handler(signum, frame):
        """信号处理器"""
        processor.logger.info(f"接收到信号 {signum}，正在停止处理器...")
        processor.stop()
        sys.exit(0)

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        processor.start()

        # 保持主线程运行
        while processor.is_running:
            time.sleep(1)

    except KeyboardInterrupt:
        processor.logger.info("接收到键盘中断，正在停止...")
    except Exception as e:
        processor.logger.error(f"处理器运行异常: {e}")
    finally:
        processor.stop()


if __name__ == "__main__":
    main()
