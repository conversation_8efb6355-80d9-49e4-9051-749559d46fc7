#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模块化盘中选股监控进程

使用新的因子框架重构的版本，展示如何使用模块化的因子系统

功能模块：
1. 股票列表管理 - 从stock_primary_signals表获取活跃股票
2. 多线程监控 - 4个工作线程并行处理
3. 数据库数据获取 - 复用market_data_fetcher.py的tick数据
4. 模块化因子计算 - 使用独立的因子模块
5. 信号检测 - 统一的信号检测框架
6. 信号去重 - 每个指标每天只发送一次
7. 飞书通知 - 发送卡片格式的信号通知

作者: QuantFM团队
创建时间: 2025-08-21
"""

import time
import threading
import queue
from datetime import datetime, date
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np

# 导入现有模块
from utils.logger import get_logger
from config.config_manager import get_config
from data.db_manager import get_db_manager
from services.feishu_notifier import FeishuNotifier


# 导入新的因子模块
from factors import (
    FactorManager,
    FibonacciFactor,
    MultiFibonacciFactor,
    EmaFactor,
    EmaTurnaroundFactor,
    BollingerFactor,
    BollingerSqueezeReleaseFactor,
    EmaTurnaroundCompositeFactor,
    EmaTurnaroundSignalDetector
)


class ModularIntradayStockMonitor:
    """模块化盘中选股监控主控制器"""
    
    def __init__(self):
        self.logger = get_logger('ModularIntradayStockMonitor')

        # 加载专用配置文件
        self.config = get_config(config_file="config/intraday_stock_monitor.toml")
        self.main_config = get_config()  # 主配置文件
        self.db_manager = get_db_manager()

        # 配置参数
        monitor_config = self.config.get('monitor', {})
        self.thread_count = monitor_config.get('thread_count', 4)
        self.monitoring_interval = monitor_config.get('monitoring_interval', 1.0)
        self.stock_refresh_interval = monitor_config.get('stock_refresh_interval', 300)
        self.price_threshold = self.config.get('signal_detection', {}).get('price_threshold', 0.005)
        
        # 核心组件
        self.feishu_notifier = None
        
        # 因子管理器
        self.factor_manager = FactorManager()
        self._init_factors()
        
        # 线程管理
        self.worker_threads = []
        self.stock_queue = queue.Queue()
        self.running = False
        
        # 信号去重缓存
        self.sent_signals_cache = {}
        
        # 性能统计
        self.stats = {
            'processed_stocks': 0,
            'signals_detected': 0,
            'signals_sent': 0,
            'errors': 0
        }
        
        self.logger.info("🚀 模块化盘中选股监控进程初始化完成")
    
    def _init_factors(self):
        """初始化因子"""
        try:
            # 1. 斐波那契因子
            fib_factor = FibonacciFactor(
                lookback_period=252,
                price_threshold=self.price_threshold
            )
            self.factor_manager.register_factor(fib_factor)
            
            # 2. 多时间周期斐波那契因子
            multi_fib_factor = MultiFibonacciFactor(
                periods=[63, 126, 252],
                price_threshold=self.price_threshold
            )
            self.factor_manager.register_factor(multi_fib_factor)
            
            # 3. EMA因子
            daily_ema_periods = self.config.get('signal_detection', {}).get(
                'daily_ema_periods', [12, 62, 144, 169, 377, 576, 676]
            )
            ema_factor = EmaFactor(
                periods=daily_ema_periods,
                price_threshold=self.price_threshold
            )
            self.factor_manager.register_factor(ema_factor)
            
            # 4. EMA拐头因子
            ema_turnaround_factor = EmaTurnaroundFactor(
                target_period=12,
                lookback_days=5,
                price_threshold=0.02
            )
            self.factor_manager.register_factor(ema_turnaround_factor)
            
            # 5. 布林带因子
            bollinger_factor = BollingerFactor(
                period=20,
                std_dev=2.0,
                price_threshold=self.price_threshold
            )
            self.factor_manager.register_factor(bollinger_factor)
            
            # 6. 布林带收缩释放因子
            bb_squeeze_factor = BollingerSqueezeReleaseFactor(
                period=20,
                std_dev=2.0,
                squeeze_threshold=0.1,
                lookback_days=10
            )
            self.factor_manager.register_factor(bb_squeeze_factor)
            
            # 7. EMA拐头向上综合因子
            ema_composite_factor = EmaTurnaroundCompositeFactor(
                ema_periods=daily_ema_periods,
                oscillation_threshold=0.02,
                lookback_days=5,
                target_ema_period=12
            )
            self.factor_manager.register_factor(ema_composite_factor)
            
            self.logger.info(f"✅ 已注册 {len(self.factor_manager.get_factor_list())} 个因子")
            
        except Exception as e:
            self.logger.error(f"初始化因子失败: {e}")
    
    def start(self):
        """启动监控进程"""
        try:
            self.logger.info("📊 启动模块化盘中选股监控进程...")
            self.running = True
            
            # 1. 使用数据库模式
            self.logger.info("✅ 使用数据库模式获取数据")

            # 2. 初始化飞书通知器
            feishu_success = self._init_feishu_notifier()
            if not feishu_success:
                self.logger.warning("⚠️ 飞书通知器初始化失败")
                self.feishu_notifier = None
            else:
                self.logger.info("✅ 飞书通知器初始化成功")
            
            # 3. 获取活跃股票列表
            active_stocks = self._get_active_stocks()
            if not active_stocks:
                self.logger.error("❌ 未获取到活跃股票列表，监控进程退出")
                return
            
            self.logger.info(f"📈 获取到 {len(active_stocks)} 只活跃股票")
            
            # 4. 启动工作线程
            self._start_worker_threads()
            
            # 5. 主循环
            self._main_monitoring_loop(active_stocks)
            
        except Exception as e:
            self.logger.error(f"❌ 启动监控进程失败: {e}")
        finally:
            self.stop()
    
    def _process_stock(self, stock_info: Dict):
        """处理单只股票（使用新的因子框架）"""
        try:
            stock_code = stock_info['stock_code']
            
            # 1. 获取当前价格
            current_price = self._get_current_price(stock_code)
            if current_price is None:
                return
            
            # 2. 获取历史数据
            df = self._get_stock_historical_data(stock_code)
            if df is None or len(df) < 50:
                return
            
            # 3. 使用因子管理器计算所有因子
            factor_results = self.factor_manager.calculate_all_factors(
                df,
                stock_code=stock_code,
                current_price=current_price,
                start_low_price=stock_info.get('start_low_price', None)
            )
            
            # 4. 处理因子结果，生成信号
            signals = self._convert_factors_to_signals(
                factor_results, stock_info, current_price
            )
            
            # 5. 处理检测到的信号
            for signal in signals:
                self._handle_signal(signal, stock_info)
            
            self.stats['processed_stocks'] += 1
            
        except Exception as e:
            self.logger.error(f"❌ 处理股票 {stock_info.get('stock_code', '')} 失败: {e}")
            self.stats['errors'] += 1
    
    def _convert_factors_to_signals(self, factor_results: Dict, stock_info: Dict, 
                                  current_price: float) -> List[Dict]:
        """将因子结果转换为信号"""
        signals = []
        
        try:
            stock_code = stock_info['stock_code']
            stock_name = stock_info.get('stock_name', stock_code)
            
            for factor_name, result in factor_results.items():
                # 只处理有效信号
                if result.signal_strength in ['WEAK', 'MEDIUM', 'STRONG']:
                    
                    # 确定指标值（用于显示）
                    indicator_value = current_price
                    if result.metadata:
                        # 尝试从元数据中获取更合适的指标值
                        if 'best_level' in result.metadata:
                            indicator_value = result.metadata['best_level'].get('value', current_price)
                        elif 'best_oscillation_value' in result.metadata:
                            indicator_value = result.metadata['best_oscillation_value']
                        elif 'ema_value' in result.metadata:
                            indicator_value = result.metadata['ema_value']
                        elif 'bb_values' in result.metadata and result.metadata['near_bands']:
                            indicator_value = result.metadata['near_bands'][0]['value']
                    
                    signal = {
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'signal_type': result.factor_type,
                        'indicator_name': factor_name,
                        'current_price': current_price,
                        'indicator_value': indicator_value,
                        'deviation': abs(current_price - indicator_value) / current_price,
                        'signal_time': datetime.now(),
                        'factor_value': result.factor_value,
                        'signal_strength': result.signal_strength,
                        'confidence': result.confidence,
                        'metadata': result.metadata
                    }
                    
                    signals.append(signal)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"转换因子结果为信号失败: {e}")
            return []
    
    def _get_stock_historical_data(self, stock_code: str) -> Optional[pd.DataFrame]:
        """获取股票历史数据"""
        try:
            # 获取日线数据
            query = """
            SELECT trade_time, open, high, low, close, volume
            FROM daily_kline 
            WHERE stock_code = %s 
            ORDER BY trade_time DESC 
            LIMIT 700
            """
            
            result = self.db_manager.fetch_all(query, [stock_code])
            if not result:
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(result, columns=['trade_time', 'open', 'high', 'low', 'close', 'volume'])
            df = df.sort_values('trade_time').reset_index(drop=True)
            df['trade_time'] = pd.to_datetime(df['trade_time'])
            
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col])
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取股票 {stock_code} 历史数据失败: {e}")
            return None
    
    # 其他方法保持与原版本相似，但使用新的因子框架
    # 这里省略了一些辅助方法的实现，重点展示因子框架的使用
    

    
    def _init_feishu_notifier(self) -> bool:
        """初始化飞书通知器（与原版本相同）"""
        # 实现与原版本相同
        return True
    
    def _get_active_stocks(self) -> List[Dict]:
        """获取活跃股票列表（与原版本相同）"""
        # 实现与原版本相同
        return []
    
    def _get_current_price(self, stock_code: str) -> Optional[float]:
        """获取当前价格（与原版本相同）"""
        # 实现与原版本相同
        return None
    
    def _handle_signal(self, signal: Dict, stock_info: Dict):
        """处理信号（与原版本相同）"""
        # 实现与原版本相同
        pass
    
    def _start_worker_threads(self):
        """启动工作线程（与原版本相同）"""
        # 实现与原版本相同
        pass
    
    def _main_monitoring_loop(self, active_stocks: List[Dict]):
        """主监控循环（与原版本相同）"""
        # 实现与原版本相同
        pass
    
    def stop(self):
        """停止监控进程"""
        self.running = False
        self.logger.info("📊 模块化盘中选股监控进程已停止")
    
    def get_factor_info(self) -> str:
        """获取因子信息"""
        info_lines = ["📊 已注册的因子列表:"]
        
        for factor_name in self.factor_manager.get_factor_list():
            factor_info = self.factor_manager.get_factor_info(factor_name)
            if factor_info:
                info_lines.append(f"\n{factor_name}:")
                info_lines.append(factor_info)
        
        return "\n".join(info_lines)


def main():
    """主函数"""
    monitor = ModularIntradayStockMonitor()
    
    # 显示因子信息
    print(monitor.get_factor_info())
    
    # 启动监控（在实际使用中）
    # monitor.start()


if __name__ == "__main__":
    main()
