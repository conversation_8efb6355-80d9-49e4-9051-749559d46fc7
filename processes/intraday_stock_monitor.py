#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
盘中选股监控进程

功能模块：
1. 股票列表管理 - 从stock_primary_signals表获取活跃股票
2. 多线程监控 - 4个工作线程并行处理
3. 数据库数据获取 - 复用market_data_fetcher.py的tick数据
4. 技术指标计算 - 斐波那契、EMA、布林线
5. 信号检测 - 0.5%精度的价格接近度检测
6. 信号去重 - 每个指标每天只发送一次
7. 飞书通知 - 发送卡片格式的信号通知

作者: QuantFM团队
创建时间: 2025-08-19
"""

import time
import threading
import queue
from datetime import datetime, date
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np

# 导入现有模块
from utils.logger import get_logger
from config.config_manager import get_config
from data.db_manager import get_db_manager
from services.feishu_notifier import FeishuNotifier



class IntradayStockMonitor:
    """盘中选股监控主控制器"""
    
    def __init__(self):
        self.logger = get_logger('IntradayStockMonitor')

        # 加载专用配置文件
        self.config = get_config(config_file="config/intraday_stock_monitor.toml")
        self.main_config = get_config()  # 主配置文件
        self.db_manager = get_db_manager()

        # 配置参数
        monitor_config = self.config.get('monitor', {})
        self.thread_count = monitor_config.get('thread_count', 4)
        self.monitoring_interval = monitor_config.get('monitoring_interval', 1.0)
        self.stock_refresh_interval = monitor_config.get('stock_refresh_interval', 300)
        self.price_threshold = self.config.get('signal_detection', {}).get('price_threshold', 0.005)
        
        # 核心组件
        self.feishu_notifier = None
        
        # 线程管理
        self.worker_threads = []
        self.stock_queue = queue.Queue()
        self.running = False
        
        # 信号去重缓存 {stock_code_signal_type_indicator_name_date: True}
        self.sent_signals_cache = {}
        
        # 性能统计
        self.stats = {
            'processed_stocks': 0,
            'signals_detected': 0,
            'signals_sent': 0,
            'errors': 0
        }
        
        self.logger.info("🚀 盘中选股监控进程初始化完成")
    
    def start(self):
        """启动监控进程"""
        try:
            self.logger.info("📊 启动盘中选股监控进程...")
            self.running = True
            
            # 1. 使用数据库模式获取数据
            self.logger.info("✅ 使用数据库模式获取数据")

            # 2. 初始化飞书通知器（允许失败，不影响主要功能）
            feishu_success = self._init_feishu_notifier()
            if not feishu_success:
                self.logger.warning("⚠️ 飞书通知器初始化失败，通知功能将被禁用")
                self.feishu_notifier = None  # 确保设置为None
            else:
                self.logger.info("✅ 飞书通知器初始化成功")
            
            # 3. 获取活跃股票列表
            active_stocks = self._get_active_stocks()
            if not active_stocks:
                self.logger.error("❌ 未获取到活跃股票列表")
                return False
            
            self.logger.info(f"📈 获取到 {len(active_stocks)} 只活跃股票")
            
            # 4. 将股票分配到队列
            self._distribute_stocks_to_queue(active_stocks)
            
            # 5. 启动工作线程
            self._start_worker_threads()
            
            # 6. 主监控循环
            self._main_monitoring_loop()
            
        except Exception as e:
            self.logger.error(f"❌ 启动监控进程失败: {e}")
            self.stop()
            return False
    
    def stop(self):
        """停止监控进程"""
        self.logger.info("🛑 停止盘中选股监控进程...")
        self.running = False
        
        # 停止工作线程
        for _ in range(self.thread_count):
            self.stock_queue.put(None)  # 发送停止信号
        
        for thread in self.worker_threads:
            thread.join(timeout=5)
        
        # 清理资源
        self.logger.info("✅ 监控进程已停止")
    

    
    def _init_feishu_notifier(self) -> bool:
        """
        初始化飞书通知器

        盘中选股监控进程使用专用的webhook2和secret2配置
        这样可以将盘中信号与其他通知分开，便于管理和监控
        """
        try:
            # 从主配置读取飞书配置，使用专用的webhook2和secret2
            feishu_config = self.main_config.get('notification', {}).get('feishu', {})
            webhook_url = feishu_config.get('webhook2', '')
            secret = feishu_config.get('secret2', '')

            if webhook_url and secret:
                self.feishu_notifier = FeishuNotifier(webhook_url, secret, self.logger, use_signature=True)
                self.logger.info("✅ 飞书通知器初始化成功 (使用webhook2)")
                return True
            else:
                self.logger.warning("⚠️ 飞书webhook2配置不完整，通知功能将被禁用")
                self.logger.warning(f"   webhook2: {'已配置' if webhook_url else '未配置'}")
                self.logger.warning(f"   secret2: {'已配置' if secret else '未配置'}")
                return False
        except Exception as e:
            self.logger.error(f"❌ 初始化飞书通知器异常: {e}")
            return False
    
    def _get_active_stocks(self) -> List[Dict]:
        """从stock_primary_signals表获取活跃股票"""
        try:
            sql = """
            SELECT stock_code, stock_name,
                   fib_level_0_236, fib_level_0_382, fib_level_0_5, 
                   fib_level_0_618, fib_level_0_786, fib_level_1_272,
                   fib_level_1_382, fib_level_1_618
            FROM stock_primary_signals 
            WHERE is_active = TRUE
            ORDER BY stock_code
            """
            
            result = self.db_manager.fetch_all(sql)
            
            stocks = []
            for row in result:
                stock_info = {
                    'stock_code': row['stock_code'],
                    'stock_name': row['stock_name'] or row['stock_code'],
                    'fibonacci_levels': {
                        'fib_23_6': row.get('fib_level_0_236'),
                        'fib_38_2': row.get('fib_level_0_382'),
                        'fib_50_0': row.get('fib_level_0_5'),
                        'fib_61_8': row.get('fib_level_0_618'),
                        'fib_78_6': row.get('fib_level_0_786'),
                        'fib_127_2': row.get('fib_level_1_272'),
                        'fib_138_2': row.get('fib_level_1_382'),
                        'fib_161_8': row.get('fib_level_1_618'),
                    }
                }
                stocks.append(stock_info)
            
            return stocks
            
        except Exception as e:
            self.logger.error(f"❌ 获取活跃股票列表失败: {e}")
            return []
    
    def _distribute_stocks_to_queue(self, stocks: List[Dict]):
        """将股票分配到队列"""
        for stock in stocks:
            self.stock_queue.put(stock)
        self.logger.info(f"📋 已将 {len(stocks)} 只股票分配到监控队列")
    
    def _start_worker_threads(self):
        """启动工作线程"""
        for i in range(self.thread_count):
            thread = threading.Thread(
                target=self._worker_thread_main,
                args=(i,),
                name=f"IntradayWorker-{i}",
                daemon=True
            )
            thread.start()
            self.worker_threads.append(thread)
        
        self.logger.info(f"🔄 已启动 {self.thread_count} 个工作线程")
    
    def _main_monitoring_loop(self):
        """主监控循环 - 支持休市暂停"""
        last_refresh_time = time.time()
        last_stats_time = time.time()

        while self.running:
            try:
                current_timestamp = time.time()

                # 检查是否到达15:05，直接退出进程
                from datetime import datetime, time as dt_time
                current_time = datetime.now().time()
                if current_time >= dt_time(15, 5, 0):
                    self.logger.info("📊 IntradayStockMonitor到达15:05收盘时间，准备退出进程")
                    self._trigger_process_exit()
                    break

                # 检查是否为交易时间
                if not self._is_trading_time():
                    self._enter_market_pause_mode()
                    continue

                # 定期刷新股票列表
                if current_timestamp - last_refresh_time > self.stock_refresh_interval:
                    self._refresh_stock_list()
                    last_refresh_time = current_timestamp

                # 定期输出统计信息
                if current_timestamp - last_stats_time > 60:  # 每分钟输出一次
                    self._log_performance_stats()
                    last_stats_time = current_timestamp

                # 检查线程健康状态
                self._check_thread_health()

                time.sleep(10)  # 主循环间隔

            except Exception as e:
                self.logger.error(f"❌ 主监控循环异常: {e}")
                time.sleep(5)
    
    def _worker_thread_main(self, thread_id: int):
        """工作线程主函数"""
        self.logger.info(f"🔄 工作线程 {thread_id} 开始运行")
        
        while self.running:
            try:
                # 从队列获取股票
                stock_info = self.stock_queue.get(timeout=1)
                if stock_info is None:  # 停止信号
                    break
                
                # 处理单只股票
                self._process_single_stock(stock_info, thread_id)
                
                # 将股票放回队列末尾（循环处理）
                self.stock_queue.put(stock_info)
                
                # 短暂休息
                time.sleep(self.monitoring_interval)
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"❌ 工作线程 {thread_id} 异常: {e}")
                self.stats['errors'] += 1
                time.sleep(1)
        
        self.logger.info(f"🏁 工作线程 {thread_id} 结束运行")

    def _enter_market_pause_mode(self):
        """
        进入休市暂停模式 - 高效的非交易时间处理
        """
        from datetime import datetime, time as dt_time, timedelta

        current_time = datetime.now().time()

        # 判断当前处于哪个休市时段
        if current_time < dt_time(9, 25, 0):
            # 早盘前休市
            next_trading_time = dt_time(9, 25, 0)
            pause_type = "早盘前"
        elif dt_time(11, 30, 0) <= current_time < dt_time(13, 0, 0):
            # 午休时间
            next_trading_time = dt_time(13, 0, 0)
            pause_type = "午休"
        elif current_time > dt_time(15, 5, 0):
            # 收盘后休市
            next_trading_time = dt_time(9, 25, 0)  # 次日早盘
            pause_type = "收盘后"
        else:
            # 其他时间，短暂休眠
            import time
            time.sleep(10)
            return

        # 计算到下次交易时间的秒数
        now = datetime.now()
        if pause_type == "收盘后":
            # 次日早盘
            next_trading_datetime = datetime.combine(
                now.date() + timedelta(days=1),
                next_trading_time
            )
        else:
            # 当日
            next_trading_datetime = datetime.combine(now.date(), next_trading_time)

        sleep_seconds = (next_trading_datetime - now).total_seconds()

        # 限制最大休眠时间为1小时，避免过长等待
        max_sleep = 3600  # 1小时
        actual_sleep = min(sleep_seconds, max_sleep)

        if actual_sleep > 60:  # 超过1分钟才记录日志
            self.logger.info(f"📊 IntradayStockMonitor进入{pause_type}休市暂停模式，"
                           f"将在 {actual_sleep/60:.1f} 分钟后恢复运行")

        import time
        time.sleep(actual_sleep)

    def _is_trading_time(self) -> bool:
        """
        检查是否为交易时间 - 精确的交易时间判断

        交易时间：
        - 早盘：09:25:00 - 11:30:00
        - 午盘：13:00:00 - 15:05:00
        """
        from datetime import datetime, time as dt_time

        current_time = datetime.now().time()

        # 早盘时间
        if dt_time(9, 25, 0) <= current_time <= dt_time(11, 30, 0):
            return True

        # 午盘时间
        if dt_time(13, 0, 0) <= current_time <= dt_time(15, 5, 0):
            return True

        return False

    def _trigger_process_exit(self):
        """
        触发进程退出 - 15:05收盘后完全清除资源
        """
        self.logger.info("🛑 IntradayStockMonitor到达15:05收盘时间，开始清理资源并退出进程")

        try:
            # 1. 设置停止标志
            self.running = False

            # 2. 记录最终统计
            self._log_performance_stats()

            # 3. 清理所有资源
            self._cleanup_all_resources()

            # 4. 强制退出进程
            self.logger.info("🔥 IntradayStockMonitor进程即将强制退出")

            # 使用os._exit()强制退出，完全清除进程资源
            import os
            os._exit(0)

        except Exception as e:
            self.logger.error(f"❌ 进程退出过程中发生异常: {e}")
            # 即使发生异常也要强制退出
            import os
            os._exit(1)

    def _cleanup_all_resources(self):
        """
        清理所有资源 - 确保完全释放
        """
        try:
            self.logger.info("🧹 开始清理IntradayStockMonitor所有资源...")

            # 1. 停止所有工作线程
            for _ in range(self.thread_count):
                try:
                    self.stock_queue.put(None)  # 发送停止信号
                except:
                    pass

            for thread in self.worker_threads:
                if thread.is_alive():
                    try:
                        thread.join(timeout=2)  # 最多等待2秒
                    except:
                        pass



            # 3. 清理数据库连接
            try:
                if hasattr(self, 'db_manager') and self.db_manager:
                    # 使用通用的关闭方法
                    if hasattr(self.db_manager, 'close'):
                        self.db_manager.close()
            except:
                pass

            # 4. 清理队列
            try:
                while not self.stock_queue.empty():
                    self.stock_queue.get_nowait()
            except:
                pass

            # 5. 清理缓存
            self.worker_threads.clear()
            self.signal_cache.clear()

            self.logger.info("✅ IntradayStockMonitor资源清理完成")

        except Exception as e:
            self.logger.error(f"❌ 资源清理过程中发生异常: {e}")
            # 继续执行，不阻止进程退出
    
    def _process_single_stock(self, stock_info: Dict, thread_id: int):
        """处理单只股票的监控"""
        stock_code = stock_info['stock_code']
        
        try:
            # 1. 获取最新tick数据
            latest_tick = self._get_latest_tick_data(stock_code)
            if not latest_tick:
                return
            
            current_price = latest_tick['price']
            self.stats['processed_stocks'] += 1
            
            # 2. 检测各类信号
            all_signals = []
            stock_name = stock_info.get('stock_name', stock_code)

            # 2.1 斐波那契信号检测
            fib_signals = self._detect_fibonacci_signals(stock_info, current_price)
            all_signals.extend(fib_signals)

            # 2.2 日线EMA和布林线信号检测
            daily_signals = self._detect_daily_indicators_signals(stock_code, current_price, stock_name)
            all_signals.extend(daily_signals)

            # 2.3 5分钟EMA信号检测
            fivemin_signals = self._detect_5min_ema_signals(stock_code, current_price, stock_name)
            all_signals.extend(fivemin_signals)
            
            # 3. 处理检测到的信号
            for signal in all_signals:
                self._handle_signal(signal, stock_info)
                
        except Exception as e:
            self.logger.error(f"❌ 处理股票 {stock_code} 异常: {e}")
            self.stats['errors'] += 1

    # =====================================================
    # 数据获取模块
    # =====================================================

    def _get_latest_tick_data(self, stock_code: str) -> Optional[Dict]:
        """
        获取最新tick数据（数据库模式）
        """
        try:
            # 数据库模式：从stock_tick_data表获取最新数据
            return self._get_latest_tick_from_db(stock_code)

        except Exception as e:
            self.logger.debug(f"从数据库获取最新tick数据失败 {stock_code}: {e}")
            return None

    def _validate_tick_data(self, tick_data: Dict) -> bool:
        """验证tick数据的完整性和时效性"""
        required_fields = ['price', 'volume', 'trade_time']
        if not all(field in tick_data for field in required_fields):
            return False

        # 检查价格合理性
        if tick_data['price'] <= 0:
            return False

        return True

    def _get_latest_tick_from_db(self, stock_code: str) -> Optional[Dict]:
        """
        从数据库获取最新tick数据（降级方案）

        从stock_tick_data表获取最新的tick数据
        """
        try:
            from datetime import date
            today = date.today()

            # 优化：只查询当天的数据，提高查询效率
            sql = """
            SELECT price, cur_vol, trade_time,
                   open, high, low, amount
            FROM stock_tick_data
            WHERE stock_code = %s
              AND DATE(trade_time) = %s
            ORDER BY trade_time DESC
            LIMIT 1
            """
            result = self.db_manager.fetch_one(sql, (stock_code, today))

            if result:
                return {
                    'price': float(result['price']),
                    'volume': int(result['cur_vol']),  # cur_vol是当前成交量（单笔）
                    'trade_time': result['trade_time'],
                    'open': float(result.get('open', result['price'])),
                    'high': float(result.get('high', result['price'])),
                    'low': float(result.get('low', result['price'])),
                    'close': float(result['price']),  # tick数据中，当前价格就是收盘价
                    'amount': float(result.get('amount', 0))
                }
            else:
                # 如果当天没有数据，查询最近的数据
                sql_fallback = """
                SELECT price, cur_vol, trade_time,
                       open, high, low, amount
                FROM stock_tick_data
                WHERE stock_code = %s
                ORDER BY trade_time DESC
                LIMIT 1
                """
                result_fallback = self.db_manager.fetch_one(sql_fallback, (stock_code,))

                if result_fallback:
                    return {
                        'price': float(result_fallback['price']),
                        'volume': int(result_fallback['cur_vol']),  # cur_vol是当前成交量（单笔）
                        'trade_time': result_fallback['trade_time'],
                        'open': float(result_fallback.get('open', result_fallback['price'])),
                        'high': float(result_fallback.get('high', result_fallback['price'])),
                        'low': float(result_fallback.get('low', result_fallback['price'])),
                        'close': float(result_fallback['price']),  # tick数据中，当前价格就是收盘价
                        'amount': float(result_fallback.get('amount', 0))
                    }

            return None

        except Exception as e:
            self.logger.debug(f"从数据库获取最新tick数据失败 {stock_code}: {e}")
            return None

    def _get_historical_daily_kline(self, stock_code: str, limit: int = 100) -> List[Dict]:
        """获取历史日线数据"""
        try:
            sql = """
            SELECT trade_time, open, high, low, close, volume
            FROM stock_kline_day
            WHERE stock_code = %s
            ORDER BY trade_time DESC
            LIMIT %s
            """
            result = self.db_manager.fetch_all(sql, (stock_code, limit))

            if result:
                # 按时间升序排列
                result.reverse()
                return result
            return []

        except Exception as e:
            self.logger.error(f"获取历史日线数据失败 {stock_code}: {e}")
            return []

    def _get_historical_5min_kline(self, stock_code: str) -> List[Dict]:
        """获取当日5分钟历史K线数据"""
        try:
            sql = """
            SELECT trade_time, open, high, low, close, volume
            FROM stock_kline_5min
            WHERE stock_code = %s
            AND trade_time >= CURRENT_DATE
            ORDER BY trade_time ASC
            """
            result = self.db_manager.fetch_all(sql, (stock_code,))
            return result or []

        except Exception as e:
            self.logger.error(f"获取5分钟历史数据失败 {stock_code}: {e}")
            return []



    def _get_today_ticks_from_db(self, stock_code: str) -> List[Dict]:
        """
        从数据库获取当天的tick数据（降级方案）

        从stock_tick_data表获取当天的所有tick数据
        """
        try:
            from datetime import date
            today = date.today()

            sql = """
            SELECT price, cur_vol, trade_time,
                   open, high, low, amount
            FROM stock_tick_data
            WHERE stock_code = %s
              AND DATE(trade_time) = %s
            ORDER BY trade_time ASC
            """

            results = self.db_manager.fetch_all(sql, (stock_code, today))

            if results:
                tick_data = []
                for row in results:
                    try:
                        # 转换为标准格式
                        tick = {
                            'price': float(row['price']),
                            'volume': int(row['cur_vol']),  # cur_vol是当前成交量（单笔）
                            'trade_time': row['trade_time'],
                            'open': float(row.get('open', row['price'])),
                            'high': float(row.get('high', row['price'])),
                            'low': float(row.get('low', row['price'])),
                            'close': float(row['price']),  # tick数据中，当前价格就是收盘价
                            'amount': float(row.get('amount', 0))
                        }
                        tick_data.append(tick)
                    except (ValueError, TypeError, KeyError) as e:
                        self.logger.debug(f"转换tick数据失败 {stock_code}: {e}")
                        continue

                self.logger.debug(f"从数据库获取 {stock_code} 当天tick数据: {len(tick_data)} 条")
                return tick_data
            else:
                self.logger.debug(f"数据库中没有 {stock_code} 当天的tick数据")
                return []

        except Exception as e:
            self.logger.error(f"从数据库获取当天tick数据失败 {stock_code}: {e}")
            return []

    # =====================================================
    # 信号检测模块
    # =====================================================

    def _detect_fibonacci_signals(self, stock_info: Dict, current_price: float) -> List[Dict]:
        """检测斐波那契信号"""
        signals = []
        stock_code = stock_info['stock_code']
        fibonacci_levels = stock_info['fibonacci_levels']

        for fib_name, fib_value in fibonacci_levels.items():
            if fib_value and self._is_price_near_level(current_price, fib_value):
                signal = {
                    'stock_code': stock_code,
                    'stock_name': stock_info['stock_name'],
                    'signal_type': 'fibonacci',
                    'indicator_name': fib_name,
                    'current_price': current_price,
                    'indicator_value': fib_value,
                    'deviation': abs(current_price - fib_value) / fib_value,
                    'signal_time': datetime.now()
                }
                signals.append(signal)

        return signals

    def _detect_daily_indicators_signals(self, stock_code: str, current_price: float, stock_name: str = None) -> List[Dict]:
        """检测日线EMA和布林线信号"""
        signals = []

        try:
            # 1. 获取历史日线数据
            historical_data = self._get_historical_daily_kline(stock_code, 100)
            if len(historical_data) < 60:  # 数据不足
                return signals

            # 2. 组装当日tick数据为日K线
            today_ticks = self._get_today_ticks_from_db(stock_code)
            if today_ticks:
                today_kline = self._ticks_to_daily_kline(today_ticks)
                complete_kline = historical_data + [today_kline]
            else:
                complete_kline = historical_data

            # 3. 转换为DataFrame
            df = pd.DataFrame(complete_kline)
            df['close'] = df['close'].astype(float)

            # 4. 计算EMA指标
            ema_periods = self.config.get('signal_detection', {}).get('daily_ema_periods', [12, 62, 144, 169, 377, 576, 676])
            for period in ema_periods:
                ema_value = self._calculate_ema(df['close'], period)
                if ema_value and self._is_price_near_level(current_price, ema_value):
                    signal = {
                        'stock_code': stock_code,
                        'stock_name': stock_name or stock_code,
                        'signal_type': 'daily_ema',
                        'indicator_name': f'ema_{period}',
                        'current_price': current_price,
                        'indicator_value': ema_value,
                        'deviation': abs(current_price - ema_value) / ema_value,
                        'signal_time': datetime.now()
                    }
                    signals.append(signal)

            # 5. 计算布林线
            bb_values = self._calculate_bollinger_bands(df['close'])
            for bb_name, bb_value in bb_values.items():
                if bb_value and self._is_price_near_level(current_price, bb_value):
                    signal = {
                        'stock_code': stock_code,
                        'stock_name': stock_name or stock_code,
                        'signal_type': 'bollinger',
                        'indicator_name': bb_name,
                        'current_price': current_price,
                        'indicator_value': bb_value,
                        'deviation': abs(current_price - bb_value) / bb_value,
                        'signal_time': datetime.now()
                    }
                    signals.append(signal)

        except Exception as e:
            self.logger.error(f"检测日线指标信号失败 {stock_code}: {e}")

        return signals

    def _detect_5min_ema_signals(self, stock_code: str, current_price: float, stock_name: str = None) -> List[Dict]:
        """检测5分钟EMA信号"""
        signals = []

        try:
            # 1. 获取5分钟历史数据
            historical_5min = self._get_historical_5min_kline(stock_code)

            # 2. 获取当日tick数据并组装5分钟K线
            today_ticks = self._get_today_ticks_from_db(stock_code)
            if today_ticks:
                tick_5min_klines = self._ticks_to_5min_klines(today_ticks)
                complete_5min = historical_5min + tick_5min_klines
            else:
                complete_5min = historical_5min

            if len(complete_5min) < 30:  # 数据不足
                return signals

            # 3. 转换为DataFrame并计算EMA
            df = pd.DataFrame(complete_5min)
            df['close'] = df['close'].astype(float)

            # 注意：5分钟EMA周期建议使用较小的数值，因为5分钟数据更适合短期分析
            # 过大的周期（如676）在5分钟级别意味着需要3380分钟（约56小时）的数据
            # 建议：日线用长周期[12,62,144,169,377,576,676]，5分钟用短周期[5,10,20,30,60]
            ema_periods = self.config.get('signal_detection', {}).get('min_5_ema_periods', [12, 62, 144, 169, 377, 576, 676])
            for period in ema_periods:
                ema_value = self._calculate_ema(df['close'], period)
                if ema_value and self._is_price_near_level(current_price, ema_value):
                    signal = {
                        'stock_code': stock_code,
                        'stock_name': stock_name or stock_code,
                        'signal_type': '5min_ema',
                        'indicator_name': f'ema_{period}',
                        'current_price': current_price,
                        'indicator_value': ema_value,
                        'deviation': abs(current_price - ema_value) / ema_value,
                        'signal_time': datetime.now()
                    }
                    signals.append(signal)

        except Exception as e:
            self.logger.error(f"检测5分钟EMA信号失败 {stock_code}: {e}")

        return signals

    # =====================================================
    # 技术指标计算模块
    # =====================================================

    def _calculate_ema(self, prices: pd.Series, period: int) -> Optional[float]:
        """计算EMA指标"""
        try:
            if len(prices) < period:
                return None

            # 使用pandas计算EMA
            ema = prices.ewm(span=period, adjust=False).mean()
            return float(ema.iloc[-1])

        except Exception as e:
            self.logger.debug(f"计算EMA失败: {e}")
            return None

    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: float = 2.0) -> Dict[str, float]:
        """计算布林线指标"""
        try:
            if len(prices) < period:
                return {}

            # 计算移动平均和标准差
            sma = prices.rolling(window=period).mean()
            std = prices.rolling(window=period).std()

            # 计算布林线
            upper = sma + (std * std_dev)
            middle = sma
            lower = sma - (std * std_dev)

            return {
                'bb_upper': float(upper.iloc[-1]),
                'bb_middle': float(middle.iloc[-1]),
                'bb_lower': float(lower.iloc[-1])
            }

        except Exception as e:
            self.logger.debug(f"计算布林线失败: {e}")
            return {}

    def _ticks_to_daily_kline(self, ticks: List[Dict]) -> Dict:
        """将tick数据组装成日K线"""
        if not ticks:
            return {}

        try:
            prices = [tick['price'] for tick in ticks]
            volumes = [tick.get('volume', 0) for tick in ticks]

            return {
                'trade_time': datetime.now().date(),
                'open': prices[0],
                'high': max(prices),
                'low': min(prices),
                'close': prices[-1],
                'volume': sum(volumes)
            }
        except Exception as e:
            self.logger.debug(f"组装日K线失败: {e}")
            return {}

    def _ticks_to_5min_klines(self, ticks: List[Dict]) -> List[Dict]:
        """将tick数据组装成5分钟K线"""
        if not ticks:
            return []

        try:
            # 按5分钟时间窗口分组
            klines = []
            current_window = None
            window_ticks = []

            for tick in ticks:
                # 计算5分钟时间窗口
                tick_time = tick.get('trade_time')
                if isinstance(tick_time, str):
                    tick_time = datetime.fromisoformat(tick_time)
                elif not isinstance(tick_time, datetime):
                    continue

                # 计算5分钟对齐时间
                minute = tick_time.minute
                aligned_minute = (minute // 5) * 5
                window_time = tick_time.replace(minute=aligned_minute, second=0, microsecond=0)

                if current_window != window_time:
                    # 处理上一个窗口
                    if window_ticks:
                        kline = self._create_kline_from_ticks(current_window, window_ticks)
                        if kline:
                            klines.append(kline)

                    # 开始新窗口
                    current_window = window_time
                    window_ticks = [tick]
                else:
                    window_ticks.append(tick)

            # 处理最后一个窗口
            if window_ticks:
                kline = self._create_kline_from_ticks(current_window, window_ticks)
                if kline:
                    klines.append(kline)

            return klines

        except Exception as e:
            self.logger.debug(f"组装5分钟K线失败: {e}")
            return []

    def _create_kline_from_ticks(self, window_time: datetime, ticks: List[Dict]) -> Optional[Dict]:
        """从tick数据创建K线"""
        if not ticks:
            return None

        try:
            prices = [tick['price'] for tick in ticks]
            volumes = [tick.get('volume', 0) for tick in ticks]

            return {
                'trade_time': window_time,
                'open': prices[0],
                'high': max(prices),
                'low': min(prices),
                'close': prices[-1],
                'volume': sum(volumes)
            }
        except Exception as e:
            self.logger.debug(f"创建K线失败: {e}")
            return None

    def _is_price_near_level(self, current_price: float, level: float, threshold: float = None) -> bool:
        """检测价格是否接近指标值（0.5%阈值）"""
        if level <= 0:
            return False

        if threshold is None:
            threshold = self.price_threshold

        deviation = abs(current_price - level) / level
        return deviation <= threshold

    # =====================================================
    # 信号处理模块
    # =====================================================

    def _handle_signal(self, signal: Dict, stock_info: Dict):
        """处理单个信号"""
        try:
            # 1. 检查信号去重
            if self._is_signal_sent_today(signal):
                return

            # 2. 检测重合信号
            overlapping_signals = self._detect_overlapping_signals(signal, stock_info)

            # 3. 构建飞书卡片
            card = self._build_signal_card(signal, overlapping_signals)

            # 4. 发送通知
            if self._send_feishu_notification(card, signal['stock_code']):
                # 5. 标记信号已发送
                self._mark_signal_as_sent(signal)
                self.stats['signals_sent'] += 1
                self.logger.info(f"✅ 发送信号: {signal['stock_code']} {signal['signal_type']} {signal['indicator_name']}")
            else:
                self.logger.error(f"❌ 发送信号失败: {signal['stock_code']} {signal['signal_type']}")

            self.stats['signals_detected'] += 1

        except Exception as e:
            self.logger.error(f"❌ 处理信号失败: {e}")

    def _is_signal_sent_today(self, signal: Dict) -> bool:
        """检查今天是否已发送过此信号"""
        cache_key = f"{signal['stock_code']}_{signal['signal_type']}_{signal['indicator_name']}_{date.today()}"

        # 先检查内存缓存
        if cache_key in self.sent_signals_cache:
            return True

        # 检查数据库记录
        try:
            sql = """
            SELECT 1 FROM intraday_signals_sent
            WHERE stock_code = %s AND signal_type = %s
            AND indicator_name = %s AND signal_date = %s
            """
            result = self.db_manager.fetch_one(sql, [
                signal['stock_code'],
                signal['signal_type'],
                signal['indicator_name'],
                date.today()
            ])

            if result:
                self.sent_signals_cache[cache_key] = True
                return True
            return False

        except Exception as e:
            self.logger.error(f"检查信号去重失败: {e}")
            return False

    def _mark_signal_as_sent(self, signal: Dict):
        """标记信号为已发送"""
        # 更新内存缓存
        cache_key = f"{signal['stock_code']}_{signal['signal_type']}_{signal['indicator_name']}_{date.today()}"
        self.sent_signals_cache[cache_key] = True

        # 写入数据库
        try:
            data = {
                'stock_code': signal['stock_code'],
                'stock_name': signal.get('stock_name', ''),
                'signal_type': signal['signal_type'],
                'indicator_name': signal['indicator_name'],
                'signal_date': date.today(),
                'signal_time': signal['signal_time'],
                'current_price': signal['current_price'],
                'indicator_value': signal['indicator_value'],
                'deviation': signal['deviation']
            }

            self.db_manager.insert(
                'intraday_signals_sent',
                data,
                on_conflict='DO NOTHING'
            )

        except Exception as e:
            self.logger.error(f"记录信号到数据库失败: {e}")

    def _detect_overlapping_signals(self, signal: Dict, stock_info: Dict) -> List[str]:
        """检测重合信号"""
        overlapping = []
        current_price = signal['current_price']

        try:
            # 检查斐波那契重合
            for fib_name, fib_value in stock_info['fibonacci_levels'].items():
                if fib_value and fib_name != signal.get('indicator_name'):
                    if self._is_price_near_level(current_price, fib_value):
                        overlapping.append(f"斐波那契{fib_name}")

            # 这里可以扩展检查其他指标的重合
            # 由于需要重新计算指标，为了性能考虑，暂时只检查斐波那契重合

        except Exception as e:
            self.logger.debug(f"检测重合信号失败: {e}")

        return overlapping

    # =====================================================
    # 飞书通知模块
    # =====================================================

    def _get_signal_level_and_circle(self, signal: Dict) -> tuple:
        """
        获取信号等级和圆圈标识

        Returns:
            tuple: (level, circle_emoji, color)
        """
        signal_type = signal['signal_type']
        indicator_name = signal['indicator_name']

        if signal_type in ['daily_ema', '5min_ema']:
            # EMA信号分级
            if 'ema_12' in indicator_name or 'ema_62' in indicator_name:
                # 第一等级：12, 62周期 - 浅色系实心圆圈
                return (1, '🟢', 'green')
            elif any(period in indicator_name for period in ['ema_144', 'ema_169', 'ema_576', 'ema_676']):
                # 第二等级：144, 169, 576, 676周期 - 稍深实心圆
                return (2, '🔵', 'blue')
            elif 'ema_377' in indicator_name:
                # 第三等级：377周期 - 深色系实心圆
                return (3, '🟣', 'purple')
            else:
                # 默认等级
                return (2, '🔵', 'blue')

        elif signal_type == 'fibonacci':
            # 斐波那契信号按阶段分级
            if 'fib_23_6' in indicator_name:
                return (1, '🟡', 'orange')  # 23.6% 回调
            elif 'fib_38_2' in indicator_name:
                return (2, '🟠', 'orange')  # 38.2% 回调
            elif 'fib_50' in indicator_name:
                return (3, '🔴', 'red')     # 50% 回调
            elif 'fib_61_8' in indicator_name:
                return (4, '🟤', 'red')     # 61.8% 回调
            elif 'fib_78_6' in indicator_name:
                return (5, '⚫', 'red')     # 78.6% 回调
            else:
                return (2, '🟠', 'orange')

        elif signal_type == 'bollinger':
            # 布林线信号
            return (2, '🟢', 'green')

        # 默认
        return (2, '🔵', 'blue')

    def _calculate_change_percent(self, current_price: float, last_close: float) -> float:
        """计算涨跌幅"""
        if last_close and last_close > 0:
            return ((current_price - last_close) / last_close) * 100
        return 0.0

    def _format_change_percent(self, change_percent: float) -> str:
        """格式化涨跌幅显示"""
        if change_percent > 0:
            return f"+{change_percent:.2f}%"
        elif change_percent < 0:
            return f"{change_percent:.2f}%"
        else:
            return "0.00%"

    def _get_enhanced_stock_info(self, signal: Dict) -> Dict:
        """获取增强的股票信息，包括涨跌幅等"""
        stock_code = signal['stock_code']

        try:
            # 获取最新tick数据以获取昨收价等信息
            latest_tick = self._get_latest_tick_data(stock_code)
            if latest_tick:
                last_close = latest_tick.get('last_close', 0)
                change_percent = self._calculate_change_percent(signal['current_price'], last_close)

                return {
                    'stock_code': stock_code,
                    'stock_name': signal.get('stock_name', stock_code),
                    'current_price': signal['current_price'],
                    'last_close': last_close,
                    'change_percent': change_percent,
                    'change_percent_str': self._format_change_percent(change_percent),
                    'open_price': latest_tick.get('open', 0),
                    'high': latest_tick.get('high', 0),
                    'low': latest_tick.get('low', 0),
                    'volume': latest_tick.get('total_volume', 0),
                    'amount': latest_tick.get('amount', 0)
                }
        except Exception as e:
            self.logger.debug(f"获取增强股票信息失败 {stock_code}: {e}")

        # 降级返回基础信息
        return {
            'stock_code': stock_code,
            'stock_name': signal.get('stock_name', stock_code),
            'current_price': signal['current_price'],
            'last_close': 0,
            'change_percent': 0,
            'change_percent_str': "0.00%",
            'open_price': 0,
            'high': 0,
            'low': 0,
            'volume': 0,
            'amount': 0
        }

    def _build_signal_card(self, signal: Dict, overlapping_signals: List[str]) -> Dict:
        """
        构建优化的信号卡片

        新格式：
        - 标题：圆圈标识 + 信号类型 + 股票代码 + 股票名称 + 涨跌幅
        - 内容：四排显示，包含所有最新指标值
        """
        # 获取信号等级和圆圈标识
        level, circle_emoji, card_color = self._get_signal_level_and_circle(signal)

        # 获取增强的股票信息
        stock_info = self._get_enhanced_stock_info(signal)

        # 信号类型中文名称
        signal_type_names = {
            'fibonacci': '斐波那契',
            'daily_ema': '日线EMA',
            'bollinger': '布林线',
            '5min_ema': '5分钟EMA'
        }

        signal_type_name = signal_type_names.get(signal['signal_type'], signal['signal_type'])

        # 构建优化的标题：圆圈 + 信号类型 + 股票代码 + 股票名称 + 涨跌幅
        title_content = f"{circle_emoji} {signal_type_name}信号 {stock_info['stock_code']} {stock_info['stock_name']} {stock_info['change_percent_str']}"

        # 构建四排内容显示
        # 第一排：当前价格 + 指标值 + 偏离度
        row1_content = f"**当前价**: ¥{stock_info['current_price']:.3f}  **{signal['indicator_name']}**: ¥{signal['indicator_value']:.3f}  **偏离**: {signal['deviation']:.2%}"

        # 第二排：开盘价 + 最高价 + 最低价 + 昨收价
        row2_content = f"**开盘**: ¥{stock_info['open_price']:.3f}  **最高**: ¥{stock_info['high']:.3f}  **最低**: ¥{stock_info['low']:.3f}  **昨收**: ¥{stock_info['last_close']:.3f}"

        # 第三排：成交量 + 成交额
        volume_str = self._format_volume(stock_info['volume'])
        amount_str = self._format_amount(stock_info['amount'])
        row3_content = f"**成交量**: {volume_str}  **成交额**: {amount_str}"

        # 第四排：重合信号 + 信号时间
        row4_parts = []
        if overlapping_signals:
            overlap_text = "、".join(overlapping_signals)
            row4_parts.append(f"**重合指标**: {overlap_text}")
        row4_parts.append(f"**信号时间**: {signal['signal_time'].strftime('%H:%M:%S')}")
        row4_content = "  ".join(row4_parts)

        # 构建卡片
        card = {
            "header": {
                "template": card_color,
                "title": {
                    "content": title_content,
                    "tag": "plain_text"
                }
            },
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "content": row1_content,
                        "tag": "lark_md"
                    }
                },
                {
                    "tag": "div",
                    "text": {
                        "content": row2_content,
                        "tag": "lark_md"
                    }
                },
                {
                    "tag": "div",
                    "text": {
                        "content": row3_content,
                        "tag": "lark_md"
                    }
                },
                {
                    "tag": "div",
                    "text": {
                        "content": row4_content,
                        "tag": "lark_md"
                    }
                }
            ]
        }

        return card

    def _format_volume(self, volume: int) -> str:
        """格式化成交量显示"""
        if volume >= 100000000:  # 1亿
            return f"{volume / 100000000:.2f}亿"
        elif volume >= 10000:  # 1万
            return f"{volume / 10000:.2f}万"
        else:
            return str(volume)

    def _format_amount(self, amount: float) -> str:
        """格式化成交额显示"""
        if amount >= 100000000:  # 1亿
            return f"{amount / 100000000:.2f}亿"
        elif amount >= 10000:  # 1万
            return f"{amount / 10000:.2f}万"
        else:
            return f"{amount:.2f}"

    def _send_feishu_notification(self, card: Dict, stock_code: str) -> bool:
        """发送飞书通知"""
        if not self.feishu_notifier:
            return False

        try:
            return self.feishu_notifier.send_card_message(card, stock_code)
        except Exception as e:
            self.logger.error(f"发送飞书通知失败: {e}")
            return False

    # =====================================================
    # 辅助功能模块
    # =====================================================

    def _refresh_stock_list(self):
        """刷新股票列表"""
        try:
            new_stocks = self._get_active_stocks()
            if new_stocks:
                # 清空当前队列
                while not self.stock_queue.empty():
                    try:
                        self.stock_queue.get_nowait()
                    except queue.Empty:
                        break

                # 重新分配股票
                self._distribute_stocks_to_queue(new_stocks)
                self.logger.info(f"🔄 股票列表已刷新: {len(new_stocks)} 只股票")

        except Exception as e:
            self.logger.error(f"刷新股票列表失败: {e}")

    def _check_thread_health(self):
        """检查线程健康状态"""
        alive_threads = [t for t in self.worker_threads if t.is_alive()]
        if len(alive_threads) < self.thread_count:
            self.logger.warning(f"⚠️ 检测到线程异常，存活线程数: {len(alive_threads)}/{self.thread_count}")
            # 这里可以实现线程重启逻辑

    def _log_performance_stats(self):
        """输出性能统计信息"""
        # 获取当前活跃股票数量
        active_stocks_count = len(self._get_active_stocks())

        self.logger.info(f"📊 性能统计 - 活跃股票: {active_stocks_count}, "
                        f"累积处理: {self.stats['processed_stocks']}, "
                        f"检测信号: {self.stats['signals_detected']}, "
                        f"发送信号: {self.stats['signals_sent']}, "
                        f"错误数: {self.stats['errors']}")

        # 重置周期性统计（保留累积统计）
        self.stats['signals_detected'] = 0
        self.stats['signals_sent'] = 0
        self.stats['errors'] = 0

    def get_stats(self) -> Dict:
        """获取统计信息"""
        return self.stats.copy()

    def send_strategy_signals(self, signals) -> bool:
        """
        发送策略信号（测试接口）

        Args:
            signals: 信号列表，每个信号应包含必要的字段

        Returns:
            bool: 发送是否成功
        """
        if not self.feishu_notifier or not self.feishu_notifier.enabled:
            self.logger.warning("飞书通知器未启用，无法发送策略信号")
            return False

        try:
            # 构建测试消息
            signal_count = len(signals)
            signal_text = f"📊 盘中策略信号测试\n\n"
            signal_text += f"🎯 信号数量: {signal_count} 个\n"
            signal_text += f"⏰ 发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            for i, signal in enumerate(signals[:3]):  # 只显示前3个信号
                signal_text += f"📈 信号 {i+1}:\n"
                signal_text += f"   股票: {getattr(signal, 'stock_code', 'N/A')} {getattr(signal, 'stock_name', 'N/A')}\n"
                signal_text += f"   策略: {getattr(signal, 'strategy_name', 'N/A')}\n"
                signal_text += f"   强度: {getattr(signal, 'signal_strength', 0):.2f}\n"
                signal_text += f"   价格: ¥{getattr(signal, 'latest_close', 0):.2f}\n\n"

            if signal_count > 3:
                signal_text += f"... 还有 {signal_count - 3} 个信号\n\n"

            signal_text += "QuantFM盘中选股监控系统"

            # 发送文本消息
            result = self.feishu_notifier._send_text_message(signal_text)

            if result:
                self.logger.info(f"✅ 成功发送 {signal_count} 个策略信号")
            else:
                self.logger.error(f"❌ 发送策略信号失败")

            return result

        except Exception as e:
            self.logger.error(f"❌ 发送策略信号异常: {e}")
            return False


# =====================================================
# 数据库表创建
# =====================================================

def create_intraday_signals_table():
    """创建盘中信号记录表"""
    db_manager = get_db_manager()

    create_table_sql = """
    CREATE TABLE IF NOT EXISTS intraday_signals_sent (
        id BIGSERIAL PRIMARY KEY,
        stock_code VARCHAR(10) NOT NULL,
        stock_name VARCHAR(50),
        signal_type VARCHAR(20) NOT NULL,
        indicator_name VARCHAR(30) NOT NULL,
        signal_date DATE NOT NULL,
        signal_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        current_price DECIMAL(10,3) NOT NULL,
        indicator_value DECIMAL(10,3) NOT NULL,
        deviation DECIMAL(8,5) NOT NULL,
        overlapping_signals TEXT[],
        created_at TIMESTAMPTZ DEFAULT NOW(),

        CONSTRAINT uk_daily_signal UNIQUE (stock_code, signal_type, indicator_name, signal_date)
    );
    """

    # 创建索引
    create_indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_intraday_signals_stock_date ON intraday_signals_sent(stock_code, signal_date);",
        "CREATE INDEX IF NOT EXISTS idx_intraday_signals_type_date ON intraday_signals_sent(signal_type, signal_date);",
        "CREATE INDEX IF NOT EXISTS idx_intraday_signals_time ON intraday_signals_sent(signal_time);"
    ]

    try:
        # 创建表
        db_manager.execute_query(create_table_sql)

        # 创建索引
        for index_sql in create_indexes_sql:
            db_manager.execute_query(index_sql)

        print("✅ 盘中信号记录表创建成功")
        return True

    except Exception as e:
        print(f"❌ 创建盘中信号记录表失败: {e}")
        return False


# =====================================================
# 主程序入口
# =====================================================

def main():
    """主程序入口"""
    logger = get_logger('IntradayStockMonitor')

    try:
        logger.info("🚀 启动盘中选股监控进程...")

        # 1. 创建数据库表
        if not create_intraday_signals_table():
            logger.error("❌ 数据库表创建失败，程序退出")
            return

        # 2. 创建监控实例
        monitor = IntradayStockMonitor()

        # 3. 启动监控
        monitor.start()

    except KeyboardInterrupt:
        logger.info("🛑 接收到停止信号，正在关闭...")
        if 'monitor' in locals():
            monitor.stop()
    except Exception as e:
        logger.error(f"❌ 程序异常: {e}")
    finally:
        logger.info("👋 盘中选股监控进程已退出")


if __name__ == "__main__":
    main()
