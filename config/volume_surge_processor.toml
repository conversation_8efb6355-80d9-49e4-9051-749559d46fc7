# 成交量激增处理器配置文件
# 用于检测开盘期100倍和盘中期10倍成交量激增情绪因子

[volume_surge_processor]
# 基本配置
enable = true                     # 启用成交量激增处理器
debug_mode = false               # 调试模式

# 因子配置
[factor_config]
opening_ratio_threshold = 100    # 开盘期成交量比值阈值
intraday_ratio_threshold = 10     # 盘中期成交量比值阈值
historical_days = 10              # 历史数据统计天数
min_volume_threshold = 1000       # 最小成交量阈值（过滤小成交量股票）
enable_cache = true               # 启用缓存
cache_ttl = 3600                  # 缓存生存时间（秒）

# 时间窗口配置
[time_windows]
preheat_time = "09:27:00"         # 预热开始时间
opening_start = "09:30:00"        # 开盘期开始时间
opening_end = "09:45:00"          # 开盘期结束时间
intraday_start = "09:45:00"       # 盘中期开始时间
intraday_end = "15:00:00"         # 盘中期结束时间

# 处理配置
[processing]
continuous_monitoring = true      # 启用连续监控
max_concurrent_stocks = 200       # 最大并发处理股票数
event_driven = true               # 启用事件驱动模式
enable_cache = true               # 启用缓存
cache_ttl = 1800                  # 缓存生存时间（秒）
max_cache_size = 10000            # 最大缓存条目数
preload_historical_data = true    # 预加载历史数据

# 信号连续性配置
[signal_continuity]
enable_continuity_check = true    # 启用连续性检查
max_signal_gap_seconds = 300      # 最大信号间隔秒数（5分钟）
reset_on_discontinuity = true     # 不连续时重置计数

# 性能配置
[performance]
enable_monitoring = true          # 启用性能监控
target_latency_ms = 500           # 目标处理延迟（毫秒）
max_memory_mb = 1024              # 最大内存使用（MB）
alert_threshold_latency_ms = 1000 # 延迟告警阈值（毫秒）

# 数据库配置
[database]
connection_pool_size = 5          # 连接池大小
max_connections = 10              # 最大连接数
acquire_timeout = 5               # 获取连接超时（秒）
query_timeout = 30                # 查询超时时间（秒）
query_retry_count = 3             # 查询重试次数
retry_delay = 1                   # 重试延迟（秒）

# 日志配置
[logging]
log_level = "INFO"                # 日志级别
log_file_max_size = "100MB"       # 日志文件最大大小
log_file_backup_count = 7         # 日志文件备份数量
enable_detailed_logging = false   # 启用详细日志

# 飞书通知配置
[feishu]
enable_startup_notification = true      # 启用启动通知
enable_shutdown_notification = true     # 启用关闭通知
enable_signal_notification = true       # 启用信号通知
enable_error_notification = true        # 启用错误通知
signal_notification_batch_size = 10     # 信号通知批量大小
notification_rate_limit = 60            # 通知频率限制（秒）

# 监控和告警配置
[monitoring]
enable_health_check = true        # 启用健康检查
health_check_interval = 60        # 健康检查间隔（秒）
enable_metrics_collection = true  # 启用指标收集
metrics_report_interval = 300     # 指标报告间隔（秒）

# SLA配置
[sla]
max_processing_latency_ms = 1000   # 最大处理延迟（毫秒）
min_success_rate = 0.95            # 最小成功率
max_error_rate = 0.05              # 最大错误率
max_memory_usage_mb = 2048         # 最大内存使用（MB）
