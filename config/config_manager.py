"""
配置管理模块

负责加载和管理系统配置信息。支持从TOML文件加载配置，并提供统一的配置访问接口。
"""

import os
import logging
import sys
from pathlib import Path
from typing import Any, Dict, Optional

# 使用 Python 3.11+ 内置的 tomllib 读取 TOML 文件
if sys.version_info >= (3, 11):
    import tomllib
else:
    try:
        import tomli as tomllib
    except ImportError:
        # 如果 tomli 不可用，尝试使用 toml
        try:
            import toml as tomllib
        except ImportError:
            raise ImportError("请安装 tomli 库: pip install tomli")

# 用于写入 TOML 文件的库
try:
    import tomli_w
    HAS_TOMLI_W = True
except ImportError:
    try:
        import toml
        HAS_TOMLI_W = False
    except ImportError:
        raise ImportError("请安装 tomli-w 库: pip install tomli-w")

# 获取日志记录器
logger = logging.getLogger(__name__)

# 默认配置路径
DEFAULT_CONFIG_PATH = "config/main.toml"
CONFIG_CACHE = {}

def get_config(section: Optional[str] = None, config_file: str = DEFAULT_CONFIG_PATH) -> Dict[str, Any]:
    """
    获取配置信息

    Args:
        section: 配置节点名称，如果为None则返回全部配置
        config_file: 配置文件路径，默认为config/main.toml

    Returns:
        配置信息字典
    """
    global CONFIG_CACHE

    # 如果配置已加载过且没有指定加载特定文件，直接返回
    if config_file in CONFIG_CACHE:
        config = CONFIG_CACHE[config_file]
        if section is not None:
            return config.get(section, {})
        return config

    # 尝试加载配置文件
    try:
        config_path = Path(config_file)
        if not config_path.exists():
            logger.warning(f"配置文件不存在: {config_file}，将使用默认配置")
            config = {}
        else:
            # 使用 tomllib 加载 TOML 文件
            with open(config_path, "rb") as f:
                config = tomllib.load(f)
            #logger.info(f"成功加载配置文件: {config_file}")

        # 缓存配置
        CONFIG_CACHE[config_file] = config

        # 返回配置
        if section is not None:
            return config.get(section, {})
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return {}

def reload_config(config_file: str = DEFAULT_CONFIG_PATH) -> Dict[str, Any]:
    """
    重新加载配置文件

    Args:
        config_file: 配置文件路径

    Returns:
        重新加载后的配置信息
    """
    global CONFIG_CACHE

    # 从缓存中移除配置
    if config_file in CONFIG_CACHE:
        del CONFIG_CACHE[config_file]

    # 重新加载配置
    return get_config(config_file=config_file)

def save_config(config: Dict[str, Any], config_file: str = DEFAULT_CONFIG_PATH) -> bool:
    """
    保存配置到文件

    Args:
        config: 配置信息
        config_file: 配置文件路径

    Returns:
        是否保存成功
    """
    try:
        # 确保目录存在
        config_path = Path(config_file)
        os.makedirs(config_path.parent, exist_ok=True)

        # 写入配置文件
        if HAS_TOMLI_W:
            with open(config_path, 'wb') as f:
                tomli_w.dump(config, f)
        else:
            # 确保toml库被导入
            import toml
            with open(config_path, 'w', encoding='utf-8') as f:
                toml.dump(config, f)

        # 更新缓存
        CONFIG_CACHE[config_file] = config

        logger.info(f"成功保存配置到文件: {config_file}")
        return True
    except Exception as e:
        logger.error(f"保存配置文件失败: {e}")
        return False

