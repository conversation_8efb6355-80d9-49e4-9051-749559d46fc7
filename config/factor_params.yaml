# 因子参数配置文件
# 统一管理所有因子的参数配置，支持不同使用场景

# =============================================================================
# MACD背离因子参数配置
# =============================================================================
macd_divergence:
  # 基础MACD参数
  macd_params:
    fastperiod: 12        # MACD快线周期 (建议范围: 8-20)
    slowperiod: 26        # MACD慢线周期 (建议范围: 20-35)
    signalperiod: 9       # MACD信号线周期 (建议范围: 5-15)
  
  # 背离检测参数
  divergence_params:
    window: 3             # 局部极值识别窗口 (建议范围: 3-10)
    min_bars_between: 5   # 两个极值点最小间隔 (建议范围: 5-20)
    min_price_change: 0.01 # 最小价格变化幅度 (建议范围: 0.01-0.05)
  
  # 输出控制参数
  output_params:
    output_mode: "continuous"  # 输出模式: "boolean" | "continuous"
    normalize_output: true     # 是否标准化输出
    feature_engineering: true # 是否启用特征工程
  
  # 性能优化参数
  performance_params:
    smoothing_window: 3       # 信号平滑窗口 (建议范围: 1-5)
    strength_decay: 0.95      # 信号强度衰减系数 (建议范围: 0.8-0.99)
    enable_cache: true        # 是否启用缓存
    vectorized_compute: true  # 是否使用向量化计算
  
  # Qlib集成参数
  qlib_params:
    enable_qlib_mode: true    # 是否启用Qlib兼容模式
    auto_optimization: false  # 是否启用自动参数优化
    ml_ready: true           # 是否为机器学习准备特征
  
  # 参数优化空间定义
  param_space:
    fastperiod:
      type: "int"
      range: [8, 20]
      default: 12
      description: "MACD快线周期，影响信号敏感度"
    
    slowperiod:
      type: "int"
      range: [20, 35]
      default: 26
      description: "MACD慢线周期，影响信号稳定性"
    
    signalperiod:
      type: "int"
      range: [5, 15]
      default: 9
      description: "MACD信号线周期，影响信号确认"
    
    window:
      type: "int"
      range: [3, 10]
      default: 5
      description: "局部极值识别窗口，影响背离检测精度"
    
    min_bars_between:
      type: "int"
      range: [5, 20]
      default: 10
      description: "极值点最小间隔，过滤噪音信号"
    
    min_price_change:
      type: "float"
      range: [0.01, 0.05]
      default: 0.02
      description: "最小价格变化幅度，过滤微小波动"
    
    smoothing_window:
      type: "int"
      range: [1, 5]
      default: 3
      description: "信号平滑窗口，减少信号抖动"
    
    strength_decay:
      type: "float"
      range: [0.8, 0.99]
      default: 0.95
      description: "信号强度衰减系数，控制信号持续性"

# =============================================================================
# EMA转向因子参数配置
# =============================================================================
ema_turnaround:
  # EMA参数
  ema_params:
    short_period: 5       # 短期EMA周期
    medium_period: 10     # 中期EMA周期
    long_period: 20       # 长期EMA周期
  
  # 转向检测参数
  turnaround_params:
    min_angle_change: 15  # 最小角度变化（度）
    confirmation_bars: 3  # 确认周期数
  
  # 参数优化空间
  param_space:
    short_period:
      type: "int"
      range: [3, 10]
      default: 5
    medium_period:
      type: "int"
      range: [8, 15]
      default: 10
    long_period:
      type: "int"
      range: [15, 30]
      default: 20

# =============================================================================
# 高阶布林带因子参数配置
# =============================================================================
bollinger_bands:
  # 布林带基础参数
  bb_params:
    period: 20                    # 布林带周期
    std_dev: 2.0                 # 标准差倍数

  # 波动率分析参数
  volatility_params:
    bbw_lookback: 252            # BBW历史分位数计算周期
    squeeze_threshold: 0.2       # 波动率压缩阈值（历史分位数）
    expansion_threshold: 0.8     # 波动率扩张阈值（历史分位数）

  # 突破验证参数
  breakout_params:
    breakout_volume_ratio: 1.2   # 突破时成交量放大倍数
    fake_breakout_days: 3        # 假突破判断天数

  # 贴轨效应参数
  walking_params:
    walking_days: 3              # 贴轨最少天数
    walking_tolerance: 0.02      # 贴轨容忍度

  # 中轨确认参数
  trend_params:
    middle_slope_period: 5       # 中轨斜率计算周期
    trend_confirmation: true     # 是否启用趋势确认

  # 输出控制参数
  output_params:
    output_mode: "continuous"    # 输出模式: "boolean" | "continuous"
    normalize_output: true       # 是否标准化输出
    feature_engineering: true   # 是否启用特征工程

  # 性能优化参数
  performance_params:
    smoothing_window: 3          # 信号平滑窗口
    strength_decay: 0.95         # 信号强度衰减系数
    enable_cache: true           # 是否启用缓存
    vectorized_compute: true     # 是否启用向量化计算

  # Qlib集成参数
  qlib_params:
    enable_qlib_mode: false      # 是否启用Qlib模式
    auto_optimization: false     # 是否自动优化参数
    ml_ready: true              # 是否为机器学习准备

  # 参数优化空间定义
  param_space:
    period: {type: int, range: [10, 30], default: 20}
    std_dev: {type: float, range: [1.5, 2.5], default: 2.0}
    bbw_lookback: {type: int, range: [126, 504], default: 252}
    squeeze_threshold: {type: float, range: [0.1, 0.3], default: 0.2}
    expansion_threshold: {type: float, range: [0.7, 0.9], default: 0.8}
    breakout_volume_ratio: {type: float, range: [1.1, 2.0], default: 1.2}
    walking_days: {type: int, range: [2, 5], default: 3}
    smoothing_window: {type: int, range: [1, 5], default: 3}
    strength_decay: {type: float, range: [0.8, 0.99], default: 0.95}

# =============================================================================
# 斐波那契因子参数配置
# =============================================================================
fibonacci:
  # 斐波那契参数
  fib_params:
    lookback_period: 50   # 回看周期
    fib_levels: [0.236, 0.382, 0.5, 0.618, 0.786] # 斐波那契水平
  
  # 多重斐波那契参数
  multi_fib_params:
    timeframes: [20, 50, 100] # 多时间框架
    weight_decay: 0.8     # 权重衰减
  
  # 参数优化空间
  param_space:
    lookback_period:
      type: "int"
      range: [30, 100]
      default: 50

# =============================================================================
# 全局配置
# =============================================================================
global_config:
  # 默认输出模式
  default_output_mode: "continuous"  # "boolean" | "continuous"
  
  # 性能配置
  performance:
    enable_parallel: true      # 启用并行计算
    max_workers: 4            # 最大工作线程数
    chunk_size: 1000          # 数据块大小
  
  # 缓存配置
  cache:
    enable_cache: true        # 启用缓存
    cache_size: 1000         # 缓存大小
    cache_ttl: 3600          # 缓存过期时间（秒）
  
  # 日志配置
  logging:
    level: "INFO"            # 日志级别
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Qlib集成配置
  qlib_integration:
    auto_detect: true        # 自动检测Qlib环境
    fallback_mode: true      # Qlib不可用时的降级模式
    
  # 参数优化配置
  optimization:
    default_method: "bayesian"  # 默认优化方法: "grid" | "random" | "bayesian"
    max_trials: 100            # 最大试验次数
    cv_folds: 5               # 交叉验证折数
    random_state: 42          # 随机种子
    
    # 优化目标
    objectives:
      primary: "ic"           # 主要目标: "ic" | "rank_ic" | "sharpe"
      secondary: "stability"  # 次要目标: "stability" | "drawdown"
  
  # 验证配置
  validation:
    min_data_points: 100     # 最小数据点数
    max_missing_ratio: 0.1   # 最大缺失比例
    outlier_threshold: 3.0   # 异常值阈值（标准差倍数）

# =============================================================================
# 使用场景配置
# =============================================================================
scenarios:
  # 开发测试场景
  development:
    bollinger_bands:
      bb_params:
        period: 15
        std_dev: 1.8
      output_params:
        output_mode: "boolean"
        normalize_output: false
        feature_engineering: false
      performance_params:
        enable_cache: false

    macd_divergence:
      macd_params:
        fastperiod: 8
        slowperiod: 20
      output_params:
        output_mode: "boolean"
        normalize_output: false
        feature_engineering: false
      performance_params:
        enable_cache: false
  
  # 生产环境场景
  production:
    bollinger_bands:
      output_params:
        output_mode: "continuous"
        normalize_output: true
        feature_engineering: true
      performance_params:
        enable_cache: true
        vectorized_compute: true

    macd_divergence:
      macd_params:
        fastperiod: 12
        slowperiod: 26
      output_params:
        output_mode: "continuous"
        normalize_output: true
      performance_params:
        enable_cache: true
        vectorized_compute: true
  
  # Qlib训练场景
  qlib_training:
    bollinger_bands:
      output_params:
        output_mode: "continuous"
        normalize_output: true
        feature_engineering: true
      qlib_params:
        enable_qlib_mode: true
        ml_ready: true
        auto_optimization: true
      performance_params:
        enable_cache: true
        vectorized_compute: true

    macd_divergence:
      output_params:
        output_mode: "continuous"
        normalize_output: true
        feature_engineering: true
      qlib_params:
        enable_qlib_mode: true
        ml_ready: true
        auto_optimization: true

# =============================================================================
# 版本信息
# =============================================================================
version_info:
  config_version: "1.0.0"
  last_updated: "2025-08-23"
  author: "QuantFM Team"
  description: "统一的因子参数配置文件，支持多种使用场景和参数优化"
