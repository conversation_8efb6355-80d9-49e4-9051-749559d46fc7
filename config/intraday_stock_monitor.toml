# =====================================================
# 盘中选股监控进程配置文件
# =====================================================

[monitor]
# 基础配置
enabled = true
thread_count = 4
monitoring_interval = 1.0  # 每只股票的监控间隔（秒）
stock_refresh_interval = 300  # 股票列表刷新间隔（秒）

# 信号检测配置
[signal_detection]
price_threshold = 0.005  # 价格接近阈值（0.5%）
fibonacci_enabled = true
daily_ema_enabled = true
bollinger_bands_enabled = true
min_5_ema_enabled = true

# EMA周期配置
daily_ema_periods = [12, 62, 144, 169, 377, 576, 676]  # 日线EMA：适合长期趋势分析
min_5_ema_periods = [12, 62, 144, 169, 377, 576, 676]  # 5分钟EMA：用户自定义周期

# 说明：
# - 日线EMA使用较大周期，用于识别中长期趋势
# - 5分钟EMA当前使用与日线相同的周期（用户配置）
# - 注意：在5分钟级别，676周期需要约56小时的数据
# - 建议：如需短期分析，可考虑使用[5,10,20,30,60]等较小周期

# 布林线配置
bollinger_period = 20
bollinger_std_dev = 2.0

# 缓存配置
[cache]
daily_kline_cache_ttl = 3600  # 日线数据缓存时间（秒）
indicator_cache_ttl = 1800    # 指标计算缓存时间（秒）
max_cache_size = 1000         # 最大缓存条目数

# 性能配置
[performance]
max_processing_time_per_stock = 5.0  # 单只股票最大处理时间（秒）
thread_health_check_interval = 30    # 线程健康检查间隔（秒）
error_threshold = 10                 # 错误阈值（超过则重启线程）

# 通知配置
[notification]
feishu_enabled = true
max_signals_per_minute = 20  # 每分钟最大信号数
signal_retry_count = 3       # 信号发送重试次数
signal_retry_delay = 5       # 重试延迟（秒）

# 数据库配置
[database]
# 信号记录表配置
signals_table_name = "intraday_signals_sent"
enable_signal_history = true
history_retention_days = 30  # 信号历史保留天数



# 日志配置
[logging]
log_level = "INFO"
log_file = "logs/intraday_stock_monitor.log"
max_log_size = "100MB"
backup_count = 5
enable_performance_logging = true
performance_log_interval = 60  # 性能日志输出间隔（秒）
