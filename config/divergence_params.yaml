# 背离因子统一配置文件
# 支持MACD、KDJ、RSI等多种技术指标的背离分析

# MACD背离因子配置
macd_divergence:
  # MACD指标参数
  macd_params:
    fastperiod: 12            # 快速EMA周期 (建议范围: 8-20)
    slowperiod: 26            # 慢速EMA周期 (建议范围: 20-35)
    signalperiod: 9           # 信号线周期 (建议范围: 5-15)
    use_macd_line: true       # 是否使用MACD线进行背离检测（否则使用信号线）
    macd_threshold: 0.001     # MACD变化阈值 (建议范围: 0.0001-0.01)

  # 背离检测参数
  divergence_params:
    window: 3             # 局部极值识别窗口 (建议范围: 3-10)
    min_bars_between: 5   # 两个极值点最小间隔 (建议范围: 5-20)
    min_price_change: 0.01 # 最小价格变化幅度 (建议范围: 0.01-0.05)
    min_indicator_change: 0.01 # 最小指标变化幅度

  # 输出控制参数
  output_params:
    output_mode: "continuous"  # 输出模式: "boolean" | "continuous"
    normalize_output: true     # 是否标准化输出
    feature_engineering: true # 是否启用特征工程

  # 性能优化参数
  performance_params:
    smoothing_window: 3       # 信号平滑窗口 (建议范围: 1-5)
    strength_decay: 0.95      # 信号强度衰减系数 (建议范围: 0.8-0.99)
    enable_cache: true        # 是否启用缓存

  # Qlib集成参数
  qlib_params:
    enable_qlib_mode: false   # 是否启用Qlib模式
    auto_optimization: false  # 是否自动优化参数
    ml_ready: true           # 是否为机器学习准备

  # 参数优化空间定义
  param_space:
    fastperiod: {type: int, range: [8, 20], default: 12}
    slowperiod: {type: int, range: [20, 35], default: 26}
    signalperiod: {type: int, range: [5, 15], default: 9}
    window: {type: int, range: [3, 10], default: 3}
    min_bars_between: {type: int, range: [5, 20], default: 5}
    min_price_change: {type: float, range: [0.01, 0.05], default: 0.01}
    macd_threshold: {type: float, range: [0.0001, 0.01], default: 0.001}

# KDJ背离因子配置
kdj_divergence:
  # KDJ指标参数
  kdj_params:
    fastk_period: 9       # K线周期 (建议范围: 5-14)
    slowk_period: 3       # K线平滑周期 (建议范围: 1-5)
    slowd_period: 3       # D线平滑周期 (建议范围: 1-5)
    use_k_line: true      # 是否使用K线进行背离检测（否则使用D线）
    kdj_threshold: 0.5    # KDJ变化阈值 (建议范围: 0.1-2.0)

  # 背离检测参数
  divergence_params:
    window: 3             # 局部极值识别窗口 (建议范围: 3-10)
    min_bars_between: 5   # 两个极值点最小间隔 (建议范围: 5-20)
    min_price_change: 0.01 # 最小价格变化幅度 (建议范围: 0.01-0.05)
    min_indicator_change: 0.01 # 最小指标变化幅度

  # 输出控制参数
  output_params:
    output_mode: "continuous"  # 输出模式: "boolean" | "continuous"
    normalize_output: true     # 是否标准化输出
    feature_engineering: true # 是否启用特征工程

  # 性能优化参数
  performance_params:
    smoothing_window: 3       # 信号平滑窗口 (建议范围: 1-5)
    strength_decay: 0.95      # 信号强度衰减系数 (建议范围: 0.8-0.99)
    enable_cache: true        # 是否启用缓存

  # Qlib集成参数
  qlib_params:
    enable_qlib_mode: false   # 是否启用Qlib模式
    auto_optimization: false  # 是否自动优化参数
    ml_ready: true           # 是否为机器学习准备

  # 参数优化空间定义
  param_space:
    fastk_period: {type: int, range: [5, 14], default: 9}
    slowk_period: {type: int, range: [1, 5], default: 3}
    slowd_period: {type: int, range: [1, 5], default: 3}
    window: {type: int, range: [3, 10], default: 3}
    min_bars_between: {type: int, range: [5, 20], default: 5}
    min_price_change: {type: float, range: [0.01, 0.05], default: 0.01}
    kdj_threshold: {type: float, range: [0.1, 2.0], default: 0.5}

# RSI背离因子配置
rsi_divergence:
  # RSI指标参数
  rsi_params:
    rsi_period: 14            # RSI周期 (建议范围: 6-21)
    rsi_threshold: 2.0        # RSI变化阈值 (建议范围: 1.0-5.0)
    overbought_level: 70.0    # 超买线 (建议范围: 65-80)
    oversold_level: 30.0      # 超卖线 (建议范围: 20-35)

  # 背离检测参数
  divergence_params:
    window: 3             # 局部极值识别窗口 (建议范围: 3-10)
    min_bars_between: 5   # 两个极值点最小间隔 (建议范围: 5-20)
    min_price_change: 0.01 # 最小价格变化幅度 (建议范围: 0.01-0.05)
    min_indicator_change: 0.01 # 最小指标变化幅度

  # 输出控制参数
  output_params:
    output_mode: "continuous"  # 输出模式: "boolean" | "continuous"
    normalize_output: true     # 是否标准化输出
    feature_engineering: true # 是否启用特征工程

  # 性能优化参数
  performance_params:
    smoothing_window: 3       # 信号平滑窗口 (建议范围: 1-5)
    strength_decay: 0.95      # 信号强度衰减系数 (建议范围: 0.8-0.99)
    enable_cache: true        # 是否启用缓存

  # Qlib集成参数
  qlib_params:
    enable_qlib_mode: false   # 是否启用Qlib模式
    auto_optimization: false  # 是否自动优化参数
    ml_ready: true           # 是否为机器学习准备

  # 参数优化空间定义
  param_space:
    rsi_period: {type: int, range: [6, 21], default: 14}
    window: {type: int, range: [3, 10], default: 3}
    min_bars_between: {type: int, range: [5, 20], default: 5}
    min_price_change: {type: float, range: [0.01, 0.05], default: 0.01}
    rsi_threshold: {type: float, range: [1.0, 5.0], default: 2.0}
    overbought_level: {type: float, range: [65.0, 80.0], default: 70.0}
    oversold_level: {type: float, range: [20.0, 35.0], default: 30.0}

# 场景化配置
scenarios:
  # 开发测试场景
  development:
    macd_divergence:
      macd_params:
        fastperiod: 8
        slowperiod: 20
        signalperiod: 5
      output_params:
        output_mode: "boolean"
        normalize_output: false
        feature_engineering: false
      performance_params:
        enable_cache: false

    kdj_divergence:
      kdj_params:
        fastk_period: 6
        slowk_period: 2
      output_params:
        output_mode: "boolean"
        normalize_output: false
        feature_engineering: false
      performance_params:
        enable_cache: false

    rsi_divergence:
      rsi_params:
        rsi_period: 10
      output_params:
        output_mode: "boolean"
        normalize_output: false
        feature_engineering: false
      performance_params:
        enable_cache: false

  # 生产环境场景
  production:
    macd_divergence:
      output_params:
        output_mode: "continuous"
        normalize_output: true
        feature_engineering: true
      performance_params:
        enable_cache: true

    kdj_divergence:
      output_params:
        output_mode: "continuous"
        normalize_output: true
        feature_engineering: true
      performance_params:
        enable_cache: true

    rsi_divergence:
      output_params:
        output_mode: "continuous"
        normalize_output: true
        feature_engineering: true
      performance_params:
        enable_cache: true

  # Qlib训练场景
  qlib_training:
    macd_divergence:
      output_params:
        output_mode: "continuous"
        normalize_output: true
        feature_engineering: true
      qlib_params:
        enable_qlib_mode: true
        auto_optimization: true
      performance_params:
        enable_cache: true

    kdj_divergence:
      output_params:
        output_mode: "continuous"
        normalize_output: true
        feature_engineering: true
      qlib_params:
        enable_qlib_mode: true
        auto_optimization: true
      performance_params:
        enable_cache: true

    rsi_divergence:
      output_params:
        output_mode: "continuous"
        normalize_output: true
        feature_engineering: true
      qlib_params:
        enable_qlib_mode: true
        auto_optimization: true
      performance_params:
        enable_cache: true

# 全局设置
global_settings:
  # 日志级别
  log_level: "INFO"
  
  # 默认场景
  default_scenario: "production"
  
  # 数据要求
  min_data_length: 50
  
  # 性能监控
  enable_performance_monitoring: true
  
  # 错误处理
  error_handling: "graceful"  # "strict" | "graceful"
