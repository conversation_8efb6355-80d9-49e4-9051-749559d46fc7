# 实时成交量异动检测配置

[volume_anomaly_realtime]
# 基本配置
worker_threads = 10                    # 工作线程数
stocks_per_thread = 450               # 每线程处理股票数
enable_realtime_detection = true      # 启用实时检测

# 检测策略配置
[detection_strategy]
# 开盘期策略 (9:30-9:45)
opening_volume_threshold = 2.0        # 开盘期成交量阈值（倍数）
opening_price_change_threshold = 0.03 # 开盘期价格变动阈值（3%）

# 常规期策略 (9:45后)
regular_volume_threshold = 1.5        # 常规期成交量阈值（倍数）
regular_price_change_threshold = 0.02 # 常规期价格变动阈值（2%）

# 缓存配置
[cache_config]
recent_buckets_count = 6              # 缓存前N个5分钟桶的平均值
historical_days = 20                  # 历史统计天数
cache_refresh_interval = 3600         # 缓存刷新间隔（秒）

# 通知配置
[notification]
enable_feishu = true                  # 启用飞书通知
notify_interval = 300                 # 同一股票通知间隔（秒）
max_signals_per_minute = 20           # 每分钟最大信号数

# 性能配置
[performance]
tick_fetch_interval = 0.1             # Tick数据获取间隔（秒）
max_cycle_time = 2.0                  # 最大周期时间（秒）
enable_performance_monitor = true     # 启用性能监控

# 日志配置
[logging]
level = "INFO"
enable_signal_log = true              # 启用信号日志
enable_performance_log = true         # 启用性能日志
