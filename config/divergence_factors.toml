# 背离因子配置文件
# 遵循项目配置管理模式

[extreme_detection]
# 极值点检测配置
base_distance = 5                      # 基础距离参数
base_prominence_ratio = 0.02           # 基础突出度比率
min_strength_threshold = 0.3           # 最小强度阈值
adaptive_volatility_factor = 1.5       # 自适应波动率因子
max_time_gap = 20                      # 最大时间间隔
completion_probability_threshold = 0.6  # 完成概率阈值

[indicators.macd]
# MACD指标配置
fast_period = 12
slow_period = 26
signal_period = 9
zero_line_significance = 1.2
histogram_threshold = 0.1
cross_confirmation_periods = 3

[indicators.rsi]
# RSI指标配置
period = 14
overbought_threshold = 70
oversold_threshold = 30
centerline = 50
extreme_zone_weight = 1.5
middle_zone_weight = 0.8
stagnation_threshold = 5

[indicators.kdj]
# KDJ指标配置
k_period = 9
d_period = 3
j_period = 3
overbought_threshold = 80
oversold_threshold = 20
cross_confirmation_periods = 2
j_line_sensitivity = 1.2
volume_weight = 0.4

[indicators.cci]
# CCI指标配置
period = 14
upper_threshold = 100
lower_threshold = -100
extreme_upper = 200
extreme_lower = -200
normal_zone_weight = 0.7
extreme_zone_weight = 1.3
regression_confirmation_periods = 3

[indicators.obv]
# OBV指标配置
smoothing_window = 5
trend_line_periods = 20
accumulation_threshold = 0.6
distribution_threshold = -0.6
trend_break_confirmation = 3
volume_price_correlation_window = 15

[divergence_analysis]
# 背离分析配置
min_divergence_strength = 0.3
confirmation_threshold = 0.6
time_weight_factor = 0.8
volume_weight_factor = 0.2
max_time_gap = 20
pattern_reliability_threshold = 0.5

[signal_generation]
# 信号生成配置
min_actionable_strength = 0.5
stop_loss_atr_multiplier = 2.0
take_profit_atr_multiplier = 3.0
risk_reward_min_ratio = 1.5
signal_decay_half_life = 10

[adaptive_params]
# 自适应参数配置
volatility_sensitivity = 1.5
min_distance = 2
max_distance = 20
min_prominence_ratio = 0.005
max_prominence_ratio = 0.1
trending_threshold = 0.7
volatile_threshold = 2.0

[realtime]
# 实时分析配置
enable_forming_detection = true
completion_probability_threshold = 0.8
update_frequency = 1
max_forming_patterns = 5
early_warning_threshold = 0.7

[performance]
# 性能配置
cache_enabled = true
cache_max_size = 1000
parallel_processing = true
max_workers = 4
batch_size = 100
memory_limit_mb = 512

[logging]
# 日志配置
level = "INFO"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
enable_file_logging = true
log_file = "divergence_factor.log"
max_file_size_mb = 10
backup_count = 5

# 市场状态权重配置
[market_regimes.trending_bull]
macd_weight = 0.3
rsi_weight = 0.2
kdj_weight = 0.15
cci_weight = 0.2
obv_weight = 0.15
signal_sensitivity = 0.8
min_confirmations = 2

[market_regimes.trending_bear]
macd_weight = 0.3
rsi_weight = 0.2
kdj_weight = 0.15
cci_weight = 0.2
obv_weight = 0.15
signal_sensitivity = 0.8
min_confirmations = 2

[market_regimes.sideways]
macd_weight = 0.15
rsi_weight = 0.35
kdj_weight = 0.25
cci_weight = 0.15
obv_weight = 0.1
signal_sensitivity = 1.2
min_confirmations = 3

[market_regimes.high_volatility]
macd_weight = 0.2
rsi_weight = 0.25
kdj_weight = 0.1
cci_weight = 0.25
obv_weight = 0.2
signal_sensitivity = 0.6
min_confirmations = 3

# 快速配置模板
[quick_configs.conservative]
base_distance = 7
base_prominence_ratio = 0.03
min_strength_threshold = 0.5
min_divergence_strength = 0.5
confirmation_threshold = 0.8
min_actionable_strength = 0.7
stop_loss_atr_multiplier = 2.5
take_profit_atr_multiplier = 4.0

[quick_configs.balanced]
base_distance = 5
base_prominence_ratio = 0.02
min_strength_threshold = 0.3
min_divergence_strength = 0.3
confirmation_threshold = 0.6
min_actionable_strength = 0.5
stop_loss_atr_multiplier = 2.0
take_profit_atr_multiplier = 3.0

[quick_configs.aggressive]
base_distance = 3
base_prominence_ratio = 0.015
min_strength_threshold = 0.2
min_divergence_strength = 0.2
confirmation_threshold = 0.4
min_actionable_strength = 0.3
stop_loss_atr_multiplier = 1.5
take_profit_atr_multiplier = 2.5
