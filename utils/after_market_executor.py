#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
盘后策略执行器

提供统一的盘后策略执行入口函数，支持不同来源的执行调用。

作者: QuantFM Team
文件位置: utils/after_market_executor.py
"""

import sys
import logging


def execute_after_market_strategy(execution_source: str = "unknown") -> bool:
    """
    执行盘后策略

    统一的盘后策略执行入口函数，支持不同来源的调用。

    Args:
        execution_source: 执行来源标识 ("scheduled" 或 "forced")

    Returns:
        bool: 执行是否成功
    """
    from utils.logger import get_logger
    logger = get_logger("after_market_execution", level="info")
    logger.info(f"开始执行盘后策略 - 来源: {execution_source}")
    
    try:
        # 记录执行环境信息
        import os
        from datetime import datetime
        
        logger.info(f"执行时间: {datetime.now()}")
        logger.info(f"工作目录: {os.getcwd()}")
        logger.info(f"Python路径: {sys.path[:3]}...")  # 只显示前3个路径
        
        # 导入盘后策略调度器
        from processes.after_market_schedule import AfterMarketScheduler
        
        # 创建调度器实例
        scheduler = AfterMarketScheduler()
        
        # 记录调度器状态
        logger.info(f"调度器初始化完成")
        logger.info(f"策略状态: {hasattr(scheduler, 'dual_channel_strategy') and scheduler.dual_channel_strategy is not None}")
        
        # 执行策略
        success = scheduler.run()
        
        # 记录执行结果
        if success:
            logger.info(f"盘后策略执行成功 - 来源: {execution_source}")
        else:
            logger.error(f"盘后策略执行失败 - 来源: {execution_source}")
        
        # 查询并记录信号数量
        try:
            from data.db_manager import get_db_manager
            db = get_db_manager()
            
            # 查询主信号表
            primary_count = db.fetch_one("SELECT COUNT(*) FROM stock_primary_signals WHERE DATE(signal_time) = CURRENT_DATE")
            primary_count = primary_count[0] if primary_count else 0

            # 查询今日活跃信号
            active_count = db.fetch_one("SELECT COUNT(*) FROM stock_primary_signals WHERE is_active = true AND DATE(signal_time) = CURRENT_DATE")
            active_count = active_count[0] if active_count else 0

            logger.info(f"今日信号统计 - 总信号: {primary_count}, 活跃信号: {active_count}")
            
        except Exception as e:
            logger.warning(f"查询信号统计失败: {e}")
        
        return success
        
    except Exception as e:
        logger.error(f"执行盘后策略时发生异常 - 来源: {execution_source}, 错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
