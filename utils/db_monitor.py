#!/usr/bin/env python3
"""
数据库连接监控工具

用于监控PostgreSQL连接状态，及时发现和处理连接问题
"""

import psycopg2
import time
import logging
from typing import List, Dict, Any
from config.config_manager import get_config

logger = logging.getLogger(__name__)


class DatabaseMonitor:
    """数据库连接监控器"""
    
    def __init__(self):
        """初始化监控器"""
        self.config = get_config()
        self.db_config = self.config.get('database', {})
        
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取数据库连接统计信息"""
        try:
            conn = psycopg2.connect(
                host=self.db_config.get('host', 'localhost'),
                port=self.db_config.get('port', 5432),
                database=self.db_config.get('database', 'xystock'),
                user=self.db_config.get('user', 'postgres'),
                password=self.db_config.get('password', ''),
                connect_timeout=5
            )
            
            with conn.cursor() as cursor:
                # 获取连接统计
                cursor.execute("""
                    SELECT 
                        count(*) as total_connections,
                        count(*) FILTER (WHERE state = 'active') as active_connections,
                        count(*) FILTER (WHERE state = 'idle') as idle_connections,
                        count(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction,
                        count(*) FILTER (WHERE state = 'idle in transaction (aborted)') as idle_in_transaction_aborted
                    FROM pg_stat_activity
                    WHERE datname = %s
                """, (self.db_config.get('database', 'xystock'),))
                
                stats = cursor.fetchone()
                
                # 获取长时间运行的查询
                cursor.execute("""
                    SELECT 
                        pid, 
                        state,
                        query,
                        now() - query_start as duration,
                        now() - state_change as state_duration
                    FROM pg_stat_activity 
                    WHERE datname = %s
                    AND (now() - query_start) > interval '30 seconds'
                    ORDER BY query_start ASC
                """, (self.db_config.get('database', 'xystock'),))
                
                long_queries = cursor.fetchall()
                
            conn.close()
            
            return {
                'total_connections': stats[0],
                'active_connections': stats[1],
                'idle_connections': stats[2],
                'idle_in_transaction': stats[3],
                'idle_in_transaction_aborted': stats[4],
                'long_running_queries': [
                    {
                        'pid': q[0],
                        'state': q[1],
                        'query': q[2][:100] + '...' if len(q[2]) > 100 else q[2],
                        'duration': str(q[3]),
                        'state_duration': str(q[4])
                    }
                    for q in long_queries
                ]
            }
            
        except Exception as e:
            logger.error(f"获取数据库连接统计失败: {e}")
            return {}
    
    def cleanup_problematic_connections(self) -> int:
        """清理有问题的连接"""
        cleaned_count = 0
        
        try:
            conn = psycopg2.connect(
                host=self.db_config.get('host', 'localhost'),
                port=self.db_config.get('port', 5432),
                database=self.db_config.get('database', 'xystock'),
                user=self.db_config.get('user', 'postgres'),
                password=self.db_config.get('password', ''),
                connect_timeout=5
            )
            
            with conn.cursor() as cursor:
                # 查找需要清理的连接
                cursor.execute("""
                    SELECT pid, state, now() - state_change as duration
                    FROM pg_stat_activity 
                    WHERE datname = %s
                    AND pid != pg_backend_pid()
                    AND (
                        (state = 'idle in transaction' AND (now() - state_change) > interval '60 seconds')
                        OR (state = 'idle in transaction (aborted)' AND (now() - state_change) > interval '30 seconds')
                        OR ((now() - query_start) > interval '300 seconds' AND query LIKE 'INSERT INTO%')
                    )
                """, (self.db_config.get('database', 'xystock'),))
                
                problematic_connections = cursor.fetchall()
                
                # 终止这些连接
                for conn_info in problematic_connections:
                    pid = conn_info[0]
                    state = conn_info[1]
                    duration = conn_info[2]
                    
                    try:
                        cursor.execute("SELECT pg_terminate_backend(%s)", (pid,))
                        logger.info(f"已终止问题连接 PID: {pid}, 状态: {state}, 持续时间: {duration}")
                        cleaned_count += 1
                    except Exception as e:
                        logger.warning(f"终止连接 PID {pid} 失败: {e}")
            
            conn.close()
            
        except Exception as e:
            logger.error(f"清理问题连接失败: {e}")
        
        return cleaned_count
    
    def monitor_and_report(self) -> None:
        """监控并报告数据库状态"""
        stats = self.get_connection_stats()
        
        if not stats:
            logger.error("无法获取数据库连接统计")
            return
        
        # 报告基本统计
        logger.info(f"数据库连接统计: 总连接数={stats['total_connections']}, "
                   f"活跃={stats['active_connections']}, "
                   f"空闲={stats['idle_connections']}, "
                   f"事务中空闲={stats['idle_in_transaction']}")
        
        # 检查是否有问题
        if stats['idle_in_transaction'] > 0:
            logger.warning(f"发现 {stats['idle_in_transaction']} 个idle in transaction连接")
        
        if stats['long_running_queries']:
            logger.warning(f"发现 {len(stats['long_running_queries'])} 个长时间运行的查询")
            for query in stats['long_running_queries'][:3]:  # 只显示前3个
                logger.warning(f"长查询 PID {query['pid']}: {query['query']} (运行时间: {query['duration']})")
        
        # 如果有严重问题，自动清理
        if stats['idle_in_transaction'] > 5 or len(stats['long_running_queries']) > 10:
            logger.warning("检测到严重的连接问题，开始自动清理...")
            cleaned = self.cleanup_problematic_connections()
            logger.info(f"已清理 {cleaned} 个问题连接")


def main():
    """主函数，用于命令行调用"""
    import argparse
    
    parser = argparse.ArgumentParser(description='数据库连接监控工具')
    parser.add_argument('--cleanup', action='store_true', help='清理问题连接')
    parser.add_argument('--monitor', action='store_true', help='监控并报告状态')
    parser.add_argument('--continuous', action='store_true', help='持续监控模式')
    parser.add_argument('--interval', type=int, default=30, help='监控间隔（秒）')
    
    args = parser.parse_args()
    
    monitor = DatabaseMonitor()
    
    if args.cleanup:
        cleaned = monitor.cleanup_problematic_connections()
        print(f"已清理 {cleaned} 个问题连接")
    
    elif args.continuous:
        print(f"开始持续监控，间隔 {args.interval} 秒...")
        try:
            while True:
                monitor.monitor_and_report()
                time.sleep(args.interval)
        except KeyboardInterrupt:
            print("监控已停止")
    
    else:
        monitor.monitor_and_report()


if __name__ == '__main__':
    main()
