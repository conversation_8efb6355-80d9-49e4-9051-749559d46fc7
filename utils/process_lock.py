#!/usr/bin/env python3
"""
进程锁定机制

防止程序多开，确保系统中只有一个实例运行
"""

import os
import sys
import fcntl
import atexit
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class ProcessLock:
    """进程锁定器"""
    
    def __init__(self, lock_file: str = None, app_name: str = "xystock"):
        """
        初始化进程锁
        
        Args:
            lock_file: 锁文件路径，如果为None则使用默认路径
            app_name: 应用名称，用于生成默认锁文件名
        """
        self.app_name = app_name
        self.lock_file = lock_file or self._get_default_lock_file()
        self.lock_fd = None
        self.is_locked = False
        
    def _get_default_lock_file(self) -> str:
        """获取默认锁文件路径"""
        # 优先使用 /var/run，如果没有权限则使用 /tmp
        lock_dirs = [
            f"/var/run/{self.app_name}",
            f"/tmp/{self.app_name}",
            f"{os.path.expanduser('~')}/.{self.app_name}"
        ]
        
        for lock_dir in lock_dirs:
            try:
                Path(lock_dir).mkdir(parents=True, exist_ok=True)
                lock_file = f"{lock_dir}/{self.app_name}.pid"
                # 测试是否可写
                test_file = f"{lock_dir}/test_write"
                with open(test_file, 'w') as f:
                    f.write("test")
                os.unlink(test_file)
                return lock_file
            except (PermissionError, OSError):
                continue
        
        # 如果都失败了，使用当前目录
        return f"./{self.app_name}.pid"
    
    def acquire(self) -> bool:
        """
        获取进程锁
        
        Returns:
            bool: 是否成功获取锁
        """
        try:
            # 确保锁文件目录存在
            lock_dir = os.path.dirname(self.lock_file)
            if lock_dir:
                Path(lock_dir).mkdir(parents=True, exist_ok=True)
            
            # 打开锁文件
            self.lock_fd = open(self.lock_file, 'w')
            
            # 尝试获取排他锁（非阻塞）
            fcntl.flock(self.lock_fd.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
            
            # 写入当前进程ID
            self.lock_fd.write(str(os.getpid()))
            self.lock_fd.flush()
            
            self.is_locked = True
            
            # 注册退出时释放锁
            atexit.register(self.release)
            
            logger.info(f"成功获取进程锁: {self.lock_file}")
            return True
            
        except (IOError, OSError) as e:
            if self.lock_fd:
                self.lock_fd.close()
                self.lock_fd = None
            
            # 检查是否是因为已有进程在运行
            existing_pid = self._get_existing_pid()
            if existing_pid:
                logger.error(f"程序已在运行，PID: {existing_pid}")
                logger.error(f"如果确认程序未运行，请删除锁文件: {self.lock_file}")
            else:
                logger.error(f"无法获取进程锁: {e}")
            
            return False
    
    def release(self):
        """释放进程锁"""
        if self.is_locked and self.lock_fd:
            try:
                fcntl.flock(self.lock_fd.fileno(), fcntl.LOCK_UN)
                self.lock_fd.close()
                
                # 删除锁文件
                if os.path.exists(self.lock_file):
                    os.unlink(self.lock_file)
                
                logger.info(f"已释放进程锁: {self.lock_file}")
                
            except Exception as e:
                logger.warning(f"释放进程锁时出错: {e}")
            finally:
                self.lock_fd = None
                self.is_locked = False
    
    def _get_existing_pid(self) -> int:
        """获取已存在的进程ID"""
        try:
            if os.path.exists(self.lock_file):
                with open(self.lock_file, 'r') as f:
                    pid_str = f.read().strip()
                    if pid_str.isdigit():
                        pid = int(pid_str)
                        # 检查进程是否真的存在
                        if self._is_process_running(pid):
                            return pid
        except Exception:
            pass
        return None
    
    def _is_process_running(self, pid: int) -> bool:
        """检查进程是否正在运行"""
        try:
            # 发送信号0来检查进程是否存在
            os.kill(pid, 0)
            return True
        except (OSError, ProcessLookupError):
            return False
    
    def __enter__(self):
        """上下文管理器入口"""
        if not self.acquire():
            raise RuntimeError(f"无法获取进程锁，程序可能已在运行")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.release()


def ensure_single_instance(app_name: str = "xystock", lock_file: str = None) -> ProcessLock:
    """
    确保程序只有一个实例运行
    
    Args:
        app_name: 应用名称
        lock_file: 自定义锁文件路径
        
    Returns:
        ProcessLock: 进程锁对象
        
    Raises:
        SystemExit: 如果程序已在运行
    """
    lock = ProcessLock(lock_file, app_name)
    
    if not lock.acquire():
        print(f"❌ 错误: {app_name} 程序已在运行")
        print(f"锁文件: {lock.lock_file}")
        
        existing_pid = lock._get_existing_pid()
        if existing_pid:
            print(f"运行中的进程 PID: {existing_pid}")
            print(f"如需停止现有进程，请运行: kill {existing_pid}")
        
        print(f"如果确认程序未运行，请删除锁文件: rm {lock.lock_file}")
        sys.exit(1)
    
    return lock


def main():
    """命令行工具"""
    import argparse
    
    parser = argparse.ArgumentParser(description='进程锁定工具')
    parser.add_argument('--check', action='store_true', help='检查是否有实例在运行')
    parser.add_argument('--kill', action='store_true', help='终止运行中的实例')
    parser.add_argument('--clean', action='store_true', help='清理锁文件')
    parser.add_argument('--app-name', default='xystock', help='应用名称')
    
    args = parser.parse_args()
    
    lock = ProcessLock(app_name=args.app_name)
    
    if args.check:
        existing_pid = lock._get_existing_pid()
        if existing_pid:
            print(f"程序正在运行，PID: {existing_pid}")
            sys.exit(0)
        else:
            print("程序未运行")
            sys.exit(1)
    
    elif args.kill:
        existing_pid = lock._get_existing_pid()
        if existing_pid:
            try:
                os.kill(existing_pid, 15)  # SIGTERM
                print(f"已发送终止信号给进程 {existing_pid}")
            except Exception as e:
                print(f"终止进程失败: {e}")
                sys.exit(1)
        else:
            print("没有找到运行中的进程")
            sys.exit(1)
    
    elif args.clean:
        if os.path.exists(lock.lock_file):
            os.unlink(lock.lock_file)
            print(f"已清理锁文件: {lock.lock_file}")
        else:
            print("锁文件不存在")
    
    else:
        parser.print_help()


if __name__ == '__main__':
    main()
