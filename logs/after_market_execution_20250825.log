﻿2025-08-25 20:36:20 [INFO] [after_market_execution] - 测试执行器日志消息 1 - 这条消息应该只出现一次
2025-08-25 20:36:20 [INFO] [after_market_execution] - 测试执行器日志消息 2 - 这条消息应该只出现一次
2025-08-25 20:36:20 [WARNING] [after_market_execution] - 测试执行器警告消息 - 这条消息应该只出现一次
2025-08-25 20:36:37 [INFO] [after_market_execution] - 开始执行盘后策略 - 来源: forced
2025-08-25 20:36:37 [INFO] [after_market_execution] - 执行时间: 2025-08-25 20:36:37.358142
2025-08-25 20:36:37 [INFO] [after_market_execution] - 工作目录: /home/<USER>/Program/xystock
2025-08-25 20:36:37 [INFO] [after_market_execution] - Python路径: ['/home/<USER>/Program/xystock', '/usr/lib/python313.zip', '/usr/lib/python3.13']...
2025-08-25 20:36:38 [INFO] [after_market_execution] - 调度器初始化完成
2025-08-25 20:36:38 [INFO] [after_market_execution] - 策略状态: True
2025-08-25 22:00:36 [INFO] [after_market_execution] - 开始执行盘后策略 - 来源: forced
2025-08-25 22:00:36 [INFO] [after_market_execution] - 执行时间: 2025-08-25 22:00:36.948796
2025-08-25 22:00:36 [INFO] [after_market_execution] - 工作目录: /home/<USER>/Program/xystock
2025-08-25 22:00:36 [INFO] [after_market_execution] - Python路径: ['/home/<USER>/Program/xystock', '/usr/lib/python313.zip', '/usr/lib/python3.13']...
2025-08-25 22:00:37 [INFO] [after_market_execution] - 调度器初始化完成
2025-08-25 22:00:37 [INFO] [after_market_execution] - 策略状态: True
2025-08-25 22:34:52 [INFO] [after_market_execution] - 开始执行盘后策略 - 来源: forced
2025-08-25 22:34:52 [INFO] [after_market_execution] - 执行时间: 2025-08-25 22:34:52.065338
2025-08-25 22:34:52 [INFO] [after_market_execution] - 工作目录: /home/<USER>/Program/xystock
2025-08-25 22:34:52 [INFO] [after_market_execution] - Python路径: ['/home/<USER>/Program/xystock', '/usr/lib/python313.zip', '/usr/lib/python3.13']...
2025-08-25 22:34:52 [INFO] [after_market_execution] - 调度器初始化完成
2025-08-25 22:34:52 [INFO] [after_market_execution] - 策略状态: True
