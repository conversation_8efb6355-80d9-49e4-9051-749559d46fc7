nohup: 忽略输入
2025-08-25 12:37:44,942 - main - INFO - [启动] QuantFM 系统启动
2025-08-25 12:37:44,950 - process_manager - INFO - 进程管理器初始化完成
2025-08-25 12:37:44,950 - stockfm - INFO - StockFM 主程序初始化完成
2025-08-25 12:37:44,950 - stockfm - INFO - 启动 StockFM 系统
============================================================
QuantFM 系统启动
============================================================
检测到系统: Linux
[INFO]  Linux/Unix :  spawn ()

开始运行主函数...
[INFO] : INFO
[INFO] : logs/stockfm.log
[INFO] : DEBUG
[INFO] : logs/stockfm.log
2025-08-25 12:37:45 [INFO] [TradeCalendar] - 交易日历加载成功，共 8555 个交易日
2025-08-25 12:37:45 [INFO] [TradeCalendar] - 日期范围: 1990-12-19 到 2025-12-31
2025-08-25 12:37:45,712 - stockfm - INFO - 当前是交易日，且时间在 09:14:00 至 15:20:00 之间，立即启动 market_data_fetcher 进程
2025-08-25 12:37:45,712 - stockfm - INFO - 数据库连接将在market_data_fetcher启动时建立，为其他进程提供数据访问
2025-08-25 12:37:45,712 - stockfm - INFO - 启动市场数据获取进程
2025-08-25 12:37:45,714 - stockfm - INFO - 市场数据获取进程启动成功，PID: 551753
2025-08-25 12:37:45,714 - stockfm - INFO - 当前是交易日，且时间在 09:33:00 至 15:00:00 之间，立即启动 volume_ratio_analyzer 进程
2025-08-25 12:37:45,714 - stockfm - INFO - 启动成交量比值分析器进程
2025-08-25 12:37:45,714 - stockfm - INFO - 成交量比值分析器进程启动成功，PID: 551754
2025-08-25 12:37:45,715 - stockfm - INFO - 当前是交易日，且时间在 09:27:00 至 15:05:00 之间，立即启动 volume_surge_processor 进程
2025-08-25 12:37:45,715 - stockfm - INFO - [启动] 启动成交量激增处理器进程
2025-08-25 12:37:45,776 - market_data_fetcher_process - INFO - 市场数据获取进程启动
2025-08-25 12:37:45 [INFO] [VolumeRatioAnalyzer_551754] - 成交量比值分析器进程启动
2025-08-25 12:37:45 [INFO] [VolumeRatioAnalyzer_551754] - VolumeRatioAnalyzer功能暂未实现，进程将保持运行状态
2025-08-25 12:37:45,816 - data.db_manager - DEBUG - 预创建连接成功 (1/30)
2025-08-25 12:37:45,828 - data.db_manager - DEBUG - 预创建连接成功 (2/30)
2025-08-25 12:37:45,832 - data.db_manager - INFO - 健康检查线程已启动，检查间隔: 60秒
2025-08-25 12:37:45,832 - data.db_manager - INFO - 连接池初始化完成: 10.8.8.88:6668/xystock, 池大小: 2-30
2025-08-25 12:37:45,832 - data.db_manager - INFO - 默认数据库连接池已创建: 10.8.8.88:6668/xystock
2025-08-25 12:37:45,832 - data.db_manager - INFO - 统一数据库管理器初始化完成
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 进入午休休市暂停模式，将在 22.2 分钟后恢复运行
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 进入午休休市暂停模式，将在 22.2 分钟后恢复运行
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 进入午休休市暂停模式，将在 22.2 分钟后恢复运行
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 进入午休休市暂停模式，将在 22.2 分钟后恢复运行
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 12:37:45,932 - stockfm - INFO - [成功] 成交量激增处理器进程启动成功
2025-08-25 12:37:45,932 - stockfm - INFO - 当前是交易日，且时间在 09:26:00 至 15:00:00 之间，立即启动 intraday_stock_monitor 进程
2025-08-25 12:37:45,932 - stockfm - INFO - [启动] 启动盘中选股监控进程
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - 🚀 盘中选股监控进程初始化完成
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - 📊 启动盘中选股监控进程...
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - ✅ 使用数据库模式获取数据
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - ✅ 飞书通知器初始化成功 (使用webhook2)
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - ✅ 飞书通知器初始化成功
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - 📈 获取到 5 只活跃股票
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - 📋 已将 5 只股票分配到监控队列
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - 🔄 工作线程 0 开始运行
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - 🔄 工作线程 1 开始运行
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - 🔄 工作线程 2 开始运行
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - 🔄 工作线程 3 开始运行
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - 🔄 已启动 4 个工作线程
2025-08-25 12:37:45 [INFO] [IntradayStockMonitor] - 📊 IntradayStockMonitor进入午休休市暂停模式，将在 22.2 分钟后恢复运行
2025-08-25 12:37:46,175 - market_data_fetcher_process - INFO - 创建市场数据获取器
市场数据获取进程日志文件路径: logs/market_data_fetcher.log
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 成功加载配置文件: config/main.toml
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-08-25 12:37:46,178 - processes.market_data_fetcher - INFO - 配置参数: fetch_interval=2, batch_size=50, max_retries=5, retry_delay=0.3, timeout=10
2025-08-25 12:37:46,179 - processes.market_data_fetcher - INFO - [架构] 使用TimescaleDB时序数据库，服务器: 10.8.8.8:6668
2025-08-25 12:37:46,179 - processes.market_data_fetcher - INFO - [数据] 数据将直接写入TimescaleDB，支持高性能时序数据存储和查询
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-08-25 12:37:46,179 - processes.market_data_fetcher - INFO - 计算得出最优线程数: 16
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 开始加载股票列表...
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 正在从数据库加载股票列表...
2025-08-25 12:37:46,199 - data.db_manager - INFO - 健康检查线程已启动，检查间隔: 60秒
2025-08-25 12:37:46,199 - data.db_manager - INFO - 连接池初始化完成: 10.8.8.88:6668/xystock, 池大小: 2-30
2025-08-25 12:37:46,199 - data.db_manager - INFO - 默认数据库连接池已创建: 10.8.8.88:6668/xystock
2025-08-25 12:37:46,200 - data.db_manager - INFO - 统一数据库管理器初始化完成
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [初始化] 股票列表加载完成: 4394 只股票
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [初始化] 有效股票代码: 4394 个
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-08-25 12:37:46,233 - processes.market_data_fetcher - INFO - 初始化客户端池，池大小: 16, 超时时间: 10秒
2025-08-25 12:37:46,233 - processes.market_data_fetcher - INFO - [系统优化] Debian 系统优化已启用: {'socket_keepalive': True, 'socket_nodelay': True, 'socket_timeout': 8.0, 'connection_pool_warmup': True, 'server_health_check': True, 'adaptive_timeout': True, 'use_ipv4_only': True, 'tcp_user_timeout': 15000}
2025-08-25 12:37:46,233 - processes.market_data_fetcher - INFO - 初始化连接池，大小: 16
2025-08-25 12:37:46,233 - processes.market_data_fetcher - INFO - 开始创建 3 个初始连接，超时时间: 3秒
2025-08-25 12:37:46,663 - processes.market_data_fetcher - INFO - 连接池初始化成功，预创建连接数: 3/3
2025-08-25 12:37:46,664 - processes.market_data_fetcher - INFO - 后台线程开始创建额外的 5 个连接...
2025-08-25 12:37:46,664 - processes.market_data_fetcher - INFO - 客户端池初始化完成，当前连接数: 3, 最大连接数: 16
2025-08-25 12:37:46,665 - processes.market_data_fetcher - INFO - 创建持久化线程池，最大线程数: 16
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 正在初始化数据库管理器...
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [成功] 数据库管理器初始化成功
2025-08-25 12:37:46,667 - processes.market_data_fetcher - INFO - 市场数据获取器初始化完成
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 📦 使用数据库缓存机制
2025-08-25 12:37:46,667 - processes.market_data_fetcher - INFO - ✅ 序列化异步数据库写入线程池已启用（单线程，防死锁）
2025-08-25 12:37:46,667 - processes.market_data_fetcher - INFO - [性能优化] 性能优化配置已启用:
2025-08-25 12:37:46,667 - processes.market_data_fetcher - INFO -    - 缓存机制: 清理间隔 300秒
2025-08-25 12:37:46,668 - processes.market_data_fetcher - INFO -    - 批量写入: 阈值 5000, 间隔 0.8秒
2025-08-25 12:37:46,668 - processes.market_data_fetcher - INFO -    - 数据库优化: 批量大小 10000, 异步写入 True
2025-08-25 12:37:46,668 - processes.market_data_fetcher - INFO -    - 异步线程池: 已启用
2025-08-25 12:37:46,668 - market_data_fetcher_process - INFO - 市场数据获取器创建完成
2025-08-25 12:37:46,668 - market_data_fetcher_process - INFO - 启动市场数据获取器，强制启动模式
2025-08-25 12:37:46,668 - processes.market_data_fetcher - INFO - 市场数据获取器启动中...
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-08-25 12:37:46,668 - processes.market_data_fetcher - INFO - 开始加载股票列表...
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 开始加载股票列表...
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 正在从数据库加载股票列表...
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 12:37:46,695 - processes.market_data_fetcher - INFO - 成功加载 4394 只股票到 self.stock_list。
2025-08-25 12:37:46,695 - processes.market_data_fetcher - INFO - [简化] 使用简化架构：专注于tick数据获取和数据库存储
2025-08-25 12:37:46,695 - processes.market_data_fetcher - INFO - [线程] 正在启动 数据获取线程
2025-08-25 12:37:46,696 - processes.market_data_fetcher - INFO - [启动] 数据获取线程启动 - 底层优化版本
2025-08-25 12:37:46,696 - processes.market_data_fetcher - INFO - [线程] 数据获取线程 启动完成，线程ID: 140613638923968
2025-08-25 12:37:46,696 - processes.market_data_fetcher - INFO - 线程配置: 最大连续错误=5
2025-08-25 12:37:46,696 - processes.market_data_fetcher - INFO - [线程] 正在启动 定时任务线程
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 定时任务线程启动
2025-08-25 12:37:46,697 - processes.market_data_fetcher - INFO - [线程] 定时任务线程 启动完成，线程ID: 140613630531264
2025-08-25 12:37:46,697 - processes.market_data_fetcher - INFO - 市场数据获取器启动完成
2025-08-25 12:37:46,697 - processes.market_data_fetcher - INFO - [成功] TimescaleDB连接池已在初始化时建立
2025-08-25 12:37:46,697 - processes.market_data_fetcher - INFO - [连接池] TimescaleDB连接池状态: 可用连接 2/0
2025-08-25 12:37:46,698 - processes.market_data_fetcher - INFO -    - 借用次数: 0, 归还次数: 0
2025-08-25 12:37:46,698 - processes.market_data_fetcher - INFO -    - 连接池状态: 异常
2025-08-25 12:37:46,699 - market_data_fetcher_process - INFO - 市场数据获取器启动完成
2025-08-25 12:37:46,699 - market_data_fetcher_process - INFO - 进入主循环，按Ctrl+C退出
2025-08-25 12:37:47,903 - processes.market_data_fetcher - INFO - 后台线程完成创建额外连接，成功创建: 5，当前连接池大小: 8
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [IntradayStockMonitor] - 📋 已将 5 只股票分配到监控队列
2025-08-25 13:00:00 [INFO] [IntradayStockMonitor] - 🔄 股票列表已刷新: 5 只股票
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [IntradayStockMonitor] - 📊 性能统计 - 活跃股票: 5, 累积处理: 4246, 检测信号: 0, 发送信号: 0, 错误数: 0
2025-08-25 13:00:46,722 - processes.market_data_fetcher - INFO - [目标] 开始交易时间数据获取
2025-08-25 13:00:48,104 - processes.market_data_fetcher - WARNING - 已达到最大连接数 16，等待连接释放
2025-08-25 13:01:00 [INFO] [IntradayStockMonitor] - 📊 性能统计 - 活跃股票: 5, 累积处理: 4437, 检测信号: 0, 发送信号: 0, 错误数: 0
2025-08-25 13:02:00 [INFO] [IntradayStockMonitor] - 📊 性能统计 - 活跃股票: 5, 累积处理: 4626, 检测信号: 0, 发送信号: 0, 错误数: 0
2025-08-25 13:03:00 [INFO] [IntradayStockMonitor] - 📊 性能统计 - 活跃股票: 5, 累积处理: 4815, 检测信号: 0, 发送信号: 0, 错误数: 0
2025-08-25 13:04:00 [INFO] [IntradayStockMonitor] - 📊 性能统计 - 活跃股票: 5, 累积处理: 5003, 检测信号: 0, 发送信号: 0, 错误数: 0
2025-08-25 13:05:00 [INFO] [IntradayStockMonitor] - 📋 已将 5 只股票分配到监控队列
2025-08-25 13:05:00 [INFO] [IntradayStockMonitor] - 🔄 股票列表已刷新: 5 只股票
2025-08-25 13:05:00 [INFO] [IntradayStockMonitor] - 📊 性能统计 - 活跃股票: 5, 累积处理: 5188, 检测信号: 0, 发送信号: 0, 错误数: 0
2025-08-25 13:06:00 [INFO] [IntradayStockMonitor] - 📊 性能统计 - 活跃股票: 5, 累积处理: 5373, 检测信号: 0, 发送信号: 0, 错误数: 0
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] - [成功] 成功保存 4390/4390 条Tick数据
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] - [启动] 性能优化报告:
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] -    缓存: 大小=471, 命中率=0.0%
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] - 保存Tick数据完成，共 4390 条记录，耗时: 349.12秒
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] - 处理完成，时间: 13:00:48, 股票数: 4394
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 350646.45 毫秒，每秒处理股票数: 12.53
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] - [目标] 本次获取和保存数据总耗时: 350.65s (网络: 350.65s, 处理: -0.00s, 数据库: 350.65s)
2025-08-25 13:06:37 [WARNING] [MarketDataFetcher_551753_main] - [警告] 总耗时 350.65s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 13:06:37 [WARNING] [MarketDataFetcher_551753_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 13:06:37,370 - processes.market_data_fetcher - WARNING - 获取延迟: 349.37秒
2025-08-25 13:11:46,204 - data.db_manager - WARNING - 检测到连接泄漏: 连接 6 已使用 307.9 秒
2025-08-25 13:49:35,543 - data.db_manager - ERROR - 获取数据库连接失败: SSL connection has been closed unexpectedly

2025-08-25 13:49:35,544 - data.db_manager - ERROR - 批量插入数据到表 stock_tick_data 失败: SSL connection has been closed unexpectedly

2025-08-25 13:49:35,544 - data.db_manager - ERROR - SQL语句: INSERT INTO stock_tick_data (trade_time, stock_code, price, volume, amount, open, high, low, last_close, cur_vol, change, bid1, ask1, bid_vol1, ask_vol1, bid2, ask2, bid_vol2, ask_vol2, bid3, ask3, bid_vol3, ask_vol3, bid4, ask4, bid_vol4, ask_vol4, bid5, ask5, bid_vol5, ask_vol5) VALUES %s ON CONFLICT (trade_time, stock_code) DO NOTHING
2025-08-25 13:49:35,544 - data.db_manager - ERROR - 数据样本: [(Timestamp('2025-08-25 13:06:11'), '000860', 16.95, 221728, 368625632.0, 16.42, 16.97, 16.330000000000002, 16.39, 11, 0.05999999999999872, 16.93, 16.94, 399, 4, 16.92, 16.95, 189, 126, 16.91, 16.96, 263, 140, 16.9, 16.97, 247, 540, 16.89, 16.98, 52, 1178), (Timestamp('2025-08-25 13:06:18'), '000862', 5.67, 91267, 51927284.0, 5.68, 5.72, 5.65, 5.68, 35, -0.009999999999999787, 5.67, 5.68, 414, 1462, 5.66, 5.69, 3423, 1436, 5.65, 5.7, 2479, 4516, 5.64, 5.71, 1267, 2694, 5.63, 5.72, 1435, 3333)]
2025-08-25 13:49:35,549 - data.db_manager - ERROR - 详细错误: Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/data/db_manager.py", line 890, in insert_many
                unique_values = {col: data.get(col) for col in unique_cols}
    ...<2 lines>...
    
  File "/home/<USER>/Program/myenv/venv/lib/python3.13/site-packages/psycopg2/extras.py", line 1299, in execute_values
    cur.execute(b''.join(parts))
    ~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Program/myenv/venv/lib/python3.13/site-packages/psycopg2/extras.py", line 236, in execute
    return super().execute(query, vars)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
psycopg2.OperationalError: SSL connection has been closed unexpectedly


2025-08-25 13:49:35 [ERROR] [MarketDataFetcher_551753_main] - 数据库管理器插入失败
2025-08-25 13:49:35 [ERROR] [MarketDataFetcher_551753_main] - [失败] 缓冲区刷新失败: 4390 条数据
2025-08-25 13:49:35 [ERROR] [MarketDataFetcher_551753_main] - [失败] 保存Tick数据失败
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] - [启动] 性能优化报告:
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] -    缓存: 大小=472, 命中率=0.0%
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] - 保存Tick数据完成，共 4390 条记录，耗时: 2577.51秒
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] - 处理完成，时间: 13:06:38, 股票数: 4394
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 2578187.92 毫秒，每秒处理股票数: 1.70
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] - [目标] 本次获取和保存数据总耗时: 2578.19s (网络: 2578.19s, 处理: -0.00s, 数据库: 2578.19s)
2025-08-25 13:49:35 [WARNING] [MarketDataFetcher_551753_main] - [警告] 总耗时 2578.19s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 13:49:35 [WARNING] [MarketDataFetcher_551753_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 13:49:35,561 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,562 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,562 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,563 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,564 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,564 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,564 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,564 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,565 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,565 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,566 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,567 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,568 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,569 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,570 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,571 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,572 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,572 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,572 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,572 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,572 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,572 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,572 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:35,572 - processes.market_data_fetcher - WARNING - 获取延迟: 2577.57秒
2025-08-25 13:49:36,661 - data.db_manager - WARNING - 获取到无效连接，重新创建
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] - [成功] 成功保存 4390/4390 条Tick数据
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] - [启动] 性能优化报告:
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] -    缓存: 大小=472, 命中率=0.0%
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] - 保存Tick数据完成，共 4390 条记录，耗时: 17.50秒
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] - 处理完成，时间: 13:49:36, 股票数: 4394
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 18235.83 毫秒，每秒处理股票数: 240.95
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] - [目标] 本次获取和保存数据总耗时: 18.24s (网络: 18.24s, 处理: 0.00s, 数据库: 18.24s)
2025-08-25 13:49:53 [WARNING] [MarketDataFetcher_551753_main] - [警告] 总耗时 18.24s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 13:49:53 [WARNING] [MarketDataFetcher_551753_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,809 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,810 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,811 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,812 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,813 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,814 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,815 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,816 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,817 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,818 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,819 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,820 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,821 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,822 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,823 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,824 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,825 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,826 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,827 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,828 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,829 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,830 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,831 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,832 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,833 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,834 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,835 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,836 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,837 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,838 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,839 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,840 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,841 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,842 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,843 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,844 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,845 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,846 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,847 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,848 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,849 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,849 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,849 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,849 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,849 - processes.market_data_fetcher - WARNING - 获取延迟过大，跳过时间点
2025-08-25 13:49:53,849 - processes.market_data_fetcher - WARNING - 获取延迟: 17.85秒
2025-08-25 13:49:54,708 - data.db_manager - WARNING - 获取到无效连接，重新创建
