﻿2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 成功加载配置文件: config/main.toml
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 开始加载股票列表...
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 正在从数据库加载股票列表...
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [初始化] 股票列表加载完成: 4394 只股票
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [初始化] 有效股票代码: 4394 个
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 正在初始化数据库管理器...
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [成功] 数据库管理器初始化成功
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 📦 使用数据库缓存机制
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 开始加载股票列表...
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 正在从数据库加载股票列表...
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 12:37:46 [INFO] [MarketDataFetcher_551753_main] - 定时任务线程启动
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] - [成功] 成功保存 4390/4390 条Tick数据
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] - [启动] 性能优化报告:
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] -    缓存: 大小=471, 命中率=0.0%
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] - 保存Tick数据完成，共 4390 条记录，耗时: 349.12秒
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] - 处理完成，时间: 13:00:48, 股票数: 4394
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 350646.45 毫秒，每秒处理股票数: 12.53
2025-08-25 13:06:37 [INFO] [MarketDataFetcher_551753_main] - [目标] 本次获取和保存数据总耗时: 350.65s (网络: 350.65s, 处理: -0.00s, 数据库: 350.65s)
2025-08-25 13:06:37 [WARNING] [MarketDataFetcher_551753_main] - [警告] 总耗时 350.65s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 13:06:37 [WARNING] [MarketDataFetcher_551753_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 13:49:35 [ERROR] [MarketDataFetcher_551753_main] - 数据库管理器插入失败
2025-08-25 13:49:35 [ERROR] [MarketDataFetcher_551753_main] - [失败] 缓冲区刷新失败: 4390 条数据
2025-08-25 13:49:35 [ERROR] [MarketDataFetcher_551753_main] - [失败] 保存Tick数据失败
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] - [启动] 性能优化报告:
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] -    缓存: 大小=472, 命中率=0.0%
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] - 保存Tick数据完成，共 4390 条记录，耗时: 2577.51秒
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] - 处理完成，时间: 13:06:38, 股票数: 4394
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 2578187.92 毫秒，每秒处理股票数: 1.70
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_551753_main] - [目标] 本次获取和保存数据总耗时: 2578.19s (网络: 2578.19s, 处理: -0.00s, 数据库: 2578.19s)
2025-08-25 13:49:35 [WARNING] [MarketDataFetcher_551753_main] - [警告] 总耗时 2578.19s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 13:49:35 [WARNING] [MarketDataFetcher_551753_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] - [成功] 成功保存 4390/4390 条Tick数据
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] - [启动] 性能优化报告:
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] -    缓存: 大小=472, 命中率=0.0%
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] - 保存Tick数据完成，共 4390 条记录，耗时: 17.50秒
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] - 处理完成，时间: 13:49:36, 股票数: 4394
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 18235.83 毫秒，每秒处理股票数: 240.95
2025-08-25 13:49:53 [INFO] [MarketDataFetcher_551753_main] - [目标] 本次获取和保存数据总耗时: 18.24s (网络: 18.24s, 处理: 0.00s, 数据库: 18.24s)
2025-08-25 13:49:53 [WARNING] [MarketDataFetcher_551753_main] - [警告] 总耗时 18.24s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 13:49:53 [WARNING] [MarketDataFetcher_551753_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
