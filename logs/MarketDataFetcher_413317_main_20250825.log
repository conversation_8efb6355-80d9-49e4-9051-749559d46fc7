﻿2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - 成功加载配置文件: config/main.toml
2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - 开始加载股票列表...
2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - 正在从数据库加载股票列表...
2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - [初始化] 股票列表加载完成: 4394 只股票
2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - [初始化] 有效股票代码: 4394 个
2025-08-25 09:48:01 [INFO] [MarketDataFetcher_413317_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-08-25 09:48:02 [INFO] [MarketDataFetcher_413317_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-08-25 09:48:02 [INFO] [MarketDataFetcher_413317_main] - 正在初始化数据库管理器...
2025-08-25 09:48:02 [INFO] [MarketDataFetcher_413317_main] - [成功] 数据库管理器初始化成功
2025-08-25 09:48:02 [INFO] [MarketDataFetcher_413317_main] - 📦 使用数据库缓存机制
2025-08-25 09:48:02 [INFO] [MarketDataFetcher_413317_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-08-25 09:48:02 [INFO] [MarketDataFetcher_413317_main] - 开始加载股票列表...
2025-08-25 09:48:02 [INFO] [MarketDataFetcher_413317_main] - 正在从数据库加载股票列表...
2025-08-25 09:48:02 [INFO] [MarketDataFetcher_413317_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 09:48:02 [INFO] [MarketDataFetcher_413317_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 09:48:02 [INFO] [MarketDataFetcher_413317_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 09:48:02 [INFO] [MarketDataFetcher_413317_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 09:48:02 [INFO] [MarketDataFetcher_413317_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 09:48:02 [INFO] [MarketDataFetcher_413317_main] - 定时任务线程启动
2025-08-25 09:48:04 [WARNING] [MarketDataFetcher_413317_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'，使用标准化的当前时间
2025-08-25 09:48:04 [ERROR] [MarketDataFetcher_413317_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'
2025-08-25 09:48:04 [ERROR] [MarketDataFetcher_413317_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_trading_time_internal(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(raw_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_trading_time_internal(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_trading_time_internal(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(current_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_trading_time_internal(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_trading_time_internal(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(current_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_trading_time_internal(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'

2025-08-25 09:48:04 [INFO] [MarketDataFetcher_413317_main] - 处理完成，时间: 09:48:04, 股票数: 4394
2025-08-25 09:48:04 [INFO] [MarketDataFetcher_413317_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1761.17 毫秒，每秒处理股票数: 2494.93
2025-08-25 09:48:04 [INFO] [MarketDataFetcher_413317_main] - [目标] 本次获取和保存数据总耗时: 1.76s (网络: 1.76s, 处理: -0.00s, 数据库: 1.76s)
2025-08-25 09:48:04 [WARNING] [MarketDataFetcher_413317_main] - [警告] 总耗时 1.76s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 09:48:04 [WARNING] [MarketDataFetcher_413317_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'，使用标准化的当前时间
2025-08-25 09:48:04 [ERROR] [MarketDataFetcher_413317_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'
2025-08-25 09:48:04 [ERROR] [MarketDataFetcher_413317_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_trading_time_internal(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(raw_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_trading_time_internal(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_trading_time_internal(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(current_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_trading_time_internal(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_trading_time_internal(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(current_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_trading_time_internal(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_trading_time_internal'

2025-08-25 09:48:04 [INFO] [MarketDataFetcher_413317_main] - 处理完成，时间: 09:48:04, 股票数: 4394
2025-08-25 09:48:04 [INFO] [MarketDataFetcher_413317_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 774.85 毫秒，每秒处理股票数: 5670.75
2025-08-25 09:48:04 [INFO] [MarketDataFetcher_413317_main] - [目标] 本次获取和保存数据总耗时: 0.77s (网络: 0.77s, 处理: -0.00s, 数据库: 0.77s)
