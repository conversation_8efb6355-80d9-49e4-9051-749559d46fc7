﻿2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 成功加载配置文件: config/main.toml
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 开始加载股票列表...
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 正在从数据库加载股票列表...
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - [初始化] 股票列表加载完成: 4394 只股票
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - [初始化] 有效股票代码: 4394 个
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 正在初始化数据库管理器...
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - [成功] 数据库管理器初始化成功
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 📦 使用数据库缓存机制
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 开始加载股票列表...
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 正在从数据库加载股票列表...
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 14:48:42 [INFO] [MarketDataFetcher_14480_main] - 定时任务线程启动
2025-08-25 14:48:44 [INFO] [MarketDataFetcher_14480_main] - [调试] 处理前数据量: 4394
2025-08-25 14:48:44 [INFO] [MarketDataFetcher_14480_main] - [调试] 处理后数据量: 4394
2025-08-25 14:48:44 [INFO] [MarketDataFetcher_14480_main] - [调试] 开始验证 4394 条tick数据
2025-08-25 14:48:44 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 14:48:17'), 'stock_code': '000096', 'price': 12.32, 'volume': 97787, 'amount': 120233760.0, 'open': 12.26, 'high': 12.34, 'low': 12.22, 'last_close': 12.26, 'cur_vol': 1, 'change': 0.0, 'bid1': 12.31, 'ask1': 12.32, 'bid_vol1': 2233, 'ask_vol1': 1948, 'bid2': 12.3, 'ask2': 12.33, 'bid_vol2': 1234, 'ask_vol2': 2598, 'bid3': 12.290000000000001, 'ask3': 12.34, 'bid_vol3': 714, 'ask_vol3': 2008, 'bid4': 12.280000000000001, 'ask4': 12.35, 'bid_vol4': 1193, 'ask_vol4': 1634, 'bid5': 12.27, 'ask5': 12.36, 'bid_vol5': 695, 'ask_vol5': 1055}
2025-08-25 14:48:44 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 14:48:10'), 'stock_code': '000099', 'price': 24.330000000000002, 'volume': 393319, 'amount': 950489600.0, 'open': 23.98, 'high': 24.45, 'low': 23.75, 'last_close': 23.85, 'cur_vol': 10, 'change': 0.0, 'bid1': 24.330000000000002, 'ask1': 24.34, 'bid_vol1': 17, 'ask_vol1': 14, 'bid2': 24.32, 'ask2': 24.36, 'bid_vol2': 248, 'ask_vol2': 37, 'bid3': 24.310000000000002, 'ask3': 24.37, 'bid_vol3': 130, 'ask_vol3': 424, 'bid4': 24.3, 'ask4': 24.38, 'bid_vol4': 670, 'ask_vol4': 830, 'bid5': 24.29, 'ask5': 24.39, 'bid_vol5': 192, 'ask_vol5': 1032}
2025-08-25 14:48:44 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 14:48:22'), 'stock_code': '000100', 'price': 4.72, 'volume': 5422666, 'amount': 2569394688.0, 'open': 4.82, 'high': 4.86, 'low': 4.68, 'last_close': 4.78, 'cur_vol': 115, 'change': 0.0, 'bid1': 4.72, 'ask1': 4.73, 'bid_vol1': 60585, 'ask_vol1': 25427, 'bid2': 4.71, 'ask2': 4.74, 'bid_vol2': 20297, 'ask_vol2': 43536, 'bid3': 4.7, 'ask3': 4.75, 'bid_vol3': 34922, 'ask_vol3': 52392, 'bid4': 4.69, 'ask4': 4.76, 'bid_vol4': 49296, 'ask_vol4': 32807, 'bid5': 4.68, 'ask5': 4.7700000000000005, 'bid_vol5': 86883, 'ask_vol5': 15575}
2025-08-25 14:48:44 [INFO] [MarketDataFetcher_14480_main] - [调试] 验证完成: 原始=4394, 有效=4390, 无效=4
2025-08-25 14:48:44 [WARNING] [MarketDataFetcher_14480_main] - 过滤掉 4 条无效数据
2025-08-25 14:48:44 [INFO] [MarketDataFetcher_14480_main] - [调试] 准备插入 4390 条数据到stock_tick_data表
2025-08-25 14:48:44 [INFO] [MarketDataFetcher_14480_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 14:48:17'), 'stock_code': '000096', 'price': 12.32, 'volume': 97787, 'amount': 120233760.0, 'open': 12.26, 'high': 12.34, 'low': 12.22, 'last_close': 12.26, 'cur_vol': 1, 'change': 0.0, 'bid1': 12.31, 'ask1': 12.32, 'bid_vol1': 2233, 'ask_vol1': 1948, 'bid2': 12.3, 'ask2': 12.33, 'bid_vol2': 1234, 'ask_vol2': 2598, 'bid3': 12.290000000000001, 'ask3': 12.34, 'bid_vol3': 714, 'ask_vol3': 2008, 'bid4': 12.280000000000001, 'ask4': 12.35, 'bid_vol4': 1193, 'ask_vol4': 1634, 'bid5': 12.27, 'ask5': 12.36, 'bid_vol5': 695, 'ask_vol5': 1055}
2025-08-25 14:48:45 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据库插入成功: 4390 条数据
2025-08-25 14:48:45 [INFO] [MarketDataFetcher_14480_main] - [成功] 成功保存 4394/4394 条Tick数据
2025-08-25 14:48:45 [INFO] [MarketDataFetcher_14480_main] - [启动] 性能优化报告:
2025-08-25 14:48:45 [INFO] [MarketDataFetcher_14480_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 14:48:45 [INFO] [MarketDataFetcher_14480_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 14:48:45 [INFO] [MarketDataFetcher_14480_main] - 保存Tick数据完成，共 4394 条记录，耗时: 0.62秒
2025-08-25 14:48:45 [INFO] [MarketDataFetcher_14480_main] - 处理完成，时间: 14:48:44, 股票数: 4394
2025-08-25 14:48:45 [INFO] [MarketDataFetcher_14480_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 2528.88 毫秒，每秒处理股票数: 1737.53
2025-08-25 14:48:45 [INFO] [MarketDataFetcher_14480_main] - [目标] 本次获取和保存数据总耗时: 2.53s (网络: 2.53s, 处理: -0.00s, 数据库: 2.53s)
2025-08-25 14:48:45 [WARNING] [MarketDataFetcher_14480_main] - [警告] 总耗时 2.53s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 14:48:45 [WARNING] [MarketDataFetcher_14480_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - [调试] 处理前数据量: 4394
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - [调试] 处理后数据量: 2049
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - [成功] 成功保存 2049/2049 条Tick数据
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - [启动] 性能优化报告:
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - 保存Tick数据完成，共 2049 条记录，耗时: 0.10秒
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - 处理完成，时间: 14:48:46, 股票数: 4394
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 798.40 毫秒，每秒处理股票数: 5503.49
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - [目标] 本次获取和保存数据总耗时: 0.80s (网络: 0.80s, 处理: -0.00s, 数据库: 0.80s)
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - [调试] 处理前数据量: 4394
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - [调试] 处理后数据量: 1371
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - [调试] 开始验证 3420 条tick数据
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 14:48:09'), 'stock_code': '300229', 'price': 27.0, 'volume': 980781, 'amount': 2672621056.0, 'open': 27.71, 'high': 27.98, 'low': 26.400000000000002, 'last_close': 27.69, 'cur_vol': 63, 'change': 0.0, 'bid1': 27.0, 'ask1': 27.01, 'bid_vol1': 195, 'ask_vol1': 218, 'bid2': 26.990000000000002, 'ask2': 27.02, 'bid_vol2': 875, 'ask_vol2': 290, 'bid3': 26.98, 'ask3': 27.03, 'bid_vol3': 1293, 'ask_vol3': 112, 'bid4': 26.97, 'ask4': 27.04, 'bid_vol4': 396, 'ask_vol4': 87, 'bid5': 26.96, 'ask5': 27.05, 'bid_vol5': 704, 'ask_vol5': 313}
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 14:48:22'), 'stock_code': '300230', 'price': 5.32, 'volume': 229474, 'amount': 122011184.0, 'open': 5.3500000000000005, 'high': 5.36, 'low': 5.2700000000000005, 'last_close': 5.3500000000000005, 'cur_vol': 6, 'change': 0.009999999999999787, 'bid1': 5.3100000000000005, 'ask1': 5.32, 'bid_vol1': 1428, 'ask_vol1': 2659, 'bid2': 5.3, 'ask2': 5.33, 'bid_vol2': 2298, 'ask_vol2': 1456, 'bid3': 5.29, 'ask3': 5.34, 'bid_vol3': 5672, 'ask_vol3': 2278, 'bid4': 5.28, 'ask4': 5.3500000000000005, 'bid_vol4': 5943, 'ask_vol4': 5649, 'bid5': 5.2700000000000005, 'ask5': 5.36, 'bid_vol5': 3117, 'ask_vol5': 3703}
2025-08-25 14:48:46 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 14:48:17'), 'stock_code': '300231', 'price': 13.34, 'volume': 265183, 'amount': 354225344.0, 'open': 13.450000000000001, 'high': 13.5, 'low': 13.200000000000001, 'last_close': 13.43, 'cur_vol': 9, 'change': 0.0, 'bid1': 13.34, 'ask1': 13.35, 'bid_vol1': 454, 'ask_vol1': 134, 'bid2': 13.33, 'ask2': 13.36, 'bid_vol2': 834, 'ask_vol2': 791, 'bid3': 13.32, 'ask3': 13.370000000000001, 'bid_vol3': 76, 'ask_vol3': 1509, 'bid4': 13.31, 'ask4': 13.38, 'bid_vol4': 151, 'ask_vol4': 381, 'bid5': 13.3, 'ask5': 13.39, 'bid_vol5': 590, 'ask_vol5': 354}
2025-08-25 14:48:47 [INFO] [MarketDataFetcher_14480_main] - [调试] 验证完成: 原始=3420, 有效=3420, 无效=0
2025-08-25 14:48:47 [INFO] [MarketDataFetcher_14480_main] - [调试] 准备插入 3420 条数据到stock_tick_data表
2025-08-25 14:48:47 [INFO] [MarketDataFetcher_14480_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 14:48:09'), 'stock_code': '300229', 'price': 27.0, 'volume': 980781, 'amount': 2672621056.0, 'open': 27.71, 'high': 27.98, 'low': 26.400000000000002, 'last_close': 27.69, 'cur_vol': 63, 'change': 0.0, 'bid1': 27.0, 'ask1': 27.01, 'bid_vol1': 195, 'ask_vol1': 218, 'bid2': 26.990000000000002, 'ask2': 27.02, 'bid_vol2': 875, 'ask_vol2': 290, 'bid3': 26.98, 'ask3': 27.03, 'bid_vol3': 1293, 'ask_vol3': 112, 'bid4': 26.97, 'ask4': 27.04, 'bid_vol4': 396, 'ask_vol4': 87, 'bid5': 26.96, 'ask5': 27.05, 'bid_vol5': 704, 'ask_vol5': 313}
2025-08-25 14:51:35 [ERROR] [MarketDataFetcher_14480_main] - [调试] 数据库管理器插入失败: 3420 条数据
2025-08-25 14:51:35 [ERROR] [MarketDataFetcher_14480_main] - [失败] 缓冲区刷新失败: 3420 条数据
2025-08-25 14:51:35 [ERROR] [MarketDataFetcher_14480_main] - [失败] 保存Tick数据失败
2025-08-25 14:51:35 [INFO] [MarketDataFetcher_14480_main] - [启动] 性能优化报告:
2025-08-25 14:51:35 [INFO] [MarketDataFetcher_14480_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 14:51:35 [INFO] [MarketDataFetcher_14480_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 14:51:35 [INFO] [MarketDataFetcher_14480_main] - 保存Tick数据完成，共 1371 条记录，耗时: 168.37秒
2025-08-25 14:51:35 [INFO] [MarketDataFetcher_14480_main] - 处理完成，时间: 14:48:46, 股票数: 4394
2025-08-25 14:51:35 [INFO] [MarketDataFetcher_14480_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 169062.70 毫秒，每秒处理股票数: 25.99
2025-08-25 14:51:35 [INFO] [MarketDataFetcher_14480_main] - [目标] 本次获取和保存数据总耗时: 169.06s (网络: 169.06s, 处理: -0.00s, 数据库: 169.06s)
2025-08-25 14:51:35 [WARNING] [MarketDataFetcher_14480_main] - [警告] 总耗时 169.06s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 14:51:35 [WARNING] [MarketDataFetcher_14480_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 14:51:35 [INFO] [MarketDataFetcher_14480_main] - [调试] 处理前数据量: 4394
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - [调试] 处理后数据量: 4390
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - [调试] 开始验证 4390 条tick数据
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 14:51:10'), 'stock_code': '000688', 'price': 14.84, 'volume': 204477, 'amount': 302952416.0, 'open': 14.48, 'high': 15.07, 'low': 14.48, 'last_close': 14.41, 'cur_vol': 76, 'change': 0.0, 'bid1': 14.83, 'ask1': 14.84, 'bid_vol1': 293, 'ask_vol1': 396, 'bid2': 14.82, 'ask2': 14.85, 'bid_vol2': 533, 'ask_vol2': 1619, 'bid3': 14.81, 'ask3': 14.86, 'bid_vol3': 412, 'ask_vol3': 294, 'bid4': 14.8, 'ask4': 14.870000000000001, 'bid_vol4': 608, 'ask_vol4': 277, 'bid5': 14.790000000000001, 'ask5': 14.88, 'bid_vol5': 210, 'ask_vol5': 407}
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 14:51:16'), 'stock_code': '000690', 'price': 4.82, 'volume': 450201, 'amount': 217422048.0, 'open': 4.84, 'high': 4.8500000000000005, 'low': 4.8, 'last_close': 4.84, 'cur_vol': 1, 'change': -0.009999999999999787, 'bid1': 4.82, 'ask1': 4.83, 'bid_vol1': 4858, 'ask_vol1': 4036, 'bid2': 4.8100000000000005, 'ask2': 4.84, 'bid_vol2': 11709, 'ask_vol2': 17567, 'bid3': 4.8, 'ask3': 4.8500000000000005, 'bid_vol3': 17022, 'ask_vol3': 25179, 'bid4': 4.79, 'ask4': 4.86, 'bid_vol4': 2869, 'ask_vol4': 11317, 'bid5': 4.78, 'ask5': 4.87, 'bid_vol5': 3745, 'ask_vol5': 5462}
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 14:51:17'), 'stock_code': '000692', 'price': 3.63, 'volume': 110349, 'amount': 40293852.0, 'open': 3.66, 'high': 3.69, 'low': 3.62, 'last_close': 3.67, 'cur_vol': 35, 'change': -0.010000000000000231, 'bid1': 3.62, 'ask1': 3.63, 'bid_vol1': 2995, 'ask_vol1': 3865, 'bid2': 3.61, 'ask2': 3.64, 'bid_vol2': 1090, 'ask_vol2': 3025, 'bid3': 3.6, 'ask3': 3.65, 'bid_vol3': 1815, 'ask_vol3': 1522, 'bid4': 3.59, 'ask4': 3.66, 'bid_vol4': 386, 'ask_vol4': 2148, 'bid5': 3.58, 'ask5': 3.67, 'bid_vol5': 218, 'ask_vol5': 4742}
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - [调试] 验证完成: 原始=4390, 有效=4390, 无效=0
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - [调试] 准备插入 4390 条数据到stock_tick_data表
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 14:51:10'), 'stock_code': '000688', 'price': 14.84, 'volume': 204477, 'amount': 302952416.0, 'open': 14.48, 'high': 15.07, 'low': 14.48, 'last_close': 14.41, 'cur_vol': 76, 'change': 0.0, 'bid1': 14.83, 'ask1': 14.84, 'bid_vol1': 293, 'ask_vol1': 396, 'bid2': 14.82, 'ask2': 14.85, 'bid_vol2': 533, 'ask_vol2': 1619, 'bid3': 14.81, 'ask3': 14.86, 'bid_vol3': 412, 'ask_vol3': 294, 'bid4': 14.8, 'ask4': 14.870000000000001, 'bid_vol4': 608, 'ask_vol4': 277, 'bid5': 14.790000000000001, 'ask5': 14.88, 'bid_vol5': 210, 'ask_vol5': 407}
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据库插入成功: 4390 条数据
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - [成功] 成功保存 4390/4390 条Tick数据
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - [启动] 性能优化报告:
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - 保存Tick数据完成，共 4390 条记录，耗时: 0.66秒
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - 处理完成，时间: 14:51:35, 股票数: 4394
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1406.66 毫秒，每秒处理股票数: 3123.72
2025-08-25 14:51:36 [INFO] [MarketDataFetcher_14480_main] - [目标] 本次获取和保存数据总耗时: 1.41s (网络: 1.41s, 处理: 0.00s, 数据库: 1.41s)
2025-08-25 14:51:37 [INFO] [MarketDataFetcher_14480_main] - [调试] 处理前数据量: 4394
2025-08-25 14:51:37 [INFO] [MarketDataFetcher_14480_main] - [调试] 处理后数据量: 1484
2025-08-25 14:51:37 [INFO] [MarketDataFetcher_14480_main] - [成功] 成功保存 1484/1484 条Tick数据
2025-08-25 14:51:37 [INFO] [MarketDataFetcher_14480_main] - [启动] 性能优化报告:
2025-08-25 14:51:37 [INFO] [MarketDataFetcher_14480_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 14:51:37 [INFO] [MarketDataFetcher_14480_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 14:51:37 [INFO] [MarketDataFetcher_14480_main] - 保存Tick数据完成，共 1484 条记录，耗时: 0.09秒
2025-08-25 14:51:37 [INFO] [MarketDataFetcher_14480_main] - 处理完成，时间: 14:51:37, 股票数: 4394
2025-08-25 14:51:37 [INFO] [MarketDataFetcher_14480_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 665.09 毫秒，每秒处理股票数: 6606.62
2025-08-25 14:51:37 [INFO] [MarketDataFetcher_14480_main] - [目标] 本次获取和保存数据总耗时: 0.67s (网络: 0.67s, 处理: 0.00s, 数据库: 0.67s)
2025-08-25 14:51:38 [INFO] [MarketDataFetcher_14480_main] - [调试] 处理前数据量: 4394
2025-08-25 14:51:38 [INFO] [MarketDataFetcher_14480_main] - [调试] 处理后数据量: 2102
2025-08-25 14:51:38 [INFO] [MarketDataFetcher_14480_main] - [调试] 开始验证 3586 条tick数据
2025-08-25 14:51:38 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 14:51:14'), 'stock_code': '000096', 'price': 12.32, 'volume': 99735, 'amount': 122633032.0, 'open': 12.26, 'high': 12.34, 'low': 12.22, 'last_close': 12.26, 'cur_vol': 40, 'change': 0.0, 'bid1': 12.31, 'ask1': 12.32, 'bid_vol1': 1591, 'ask_vol1': 1790, 'bid2': 12.3, 'ask2': 12.33, 'bid_vol2': 1143, 'ask_vol2': 2318, 'bid3': 12.290000000000001, 'ask3': 12.34, 'bid_vol3': 945, 'ask_vol3': 1996, 'bid4': 12.280000000000001, 'ask4': 12.35, 'bid_vol4': 1135, 'ask_vol4': 1634, 'bid5': 12.27, 'ask5': 12.36, 'bid_vol5': 1185, 'ask_vol5': 1057}
2025-08-25 14:51:38 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 14:51:07'), 'stock_code': '000099', 'price': 24.34, 'volume': 398928, 'amount': 964148608.0, 'open': 23.98, 'high': 24.45, 'low': 23.75, 'last_close': 23.85, 'cur_vol': 51, 'change': -0.010000000000001563, 'bid1': 24.34, 'ask1': 24.35, 'bid_vol1': 686, 'ask_vol1': 38, 'bid2': 24.330000000000002, 'ask2': 24.36, 'bid_vol2': 198, 'ask_vol2': 584, 'bid3': 24.32, 'ask3': 24.37, 'bid_vol3': 261, 'ask_vol3': 742, 'bid4': 24.310000000000002, 'ask4': 24.38, 'bid_vol4': 88, 'ask_vol4': 969, 'bid5': 24.3, 'ask5': 24.39, 'bid_vol5': 445, 'ask_vol5': 1044}
2025-08-25 14:51:38 [INFO] [MarketDataFetcher_14480_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 14:51:18'), 'stock_code': '000100', 'price': 4.74, 'volume': 5480989, 'amount': 2596969472.0, 'open': 4.82, 'high': 4.86, 'low': 4.68, 'last_close': 4.78, 'cur_vol': 714, 'change': 0.0, 'bid1': 4.73, 'ask1': 4.74, 'bid_vol1': 12757, 'ask_vol1': 45695, 'bid2': 4.72, 'ask2': 4.75, 'bid_vol2': 55881, 'ask_vol2': 51441, 'bid3': 4.71, 'ask3': 4.76, 'bid_vol3': 19812, 'ask_vol3': 33819, 'bid4': 4.7, 'ask4': 4.7700000000000005, 'bid_vol4': 34898, 'ask_vol4': 15376, 'bid5': 4.69, 'ask5': 4.78, 'bid_vol5': 47543, 'ask_vol5': 28197}
2025-08-25 14:51:38 [INFO] [MarketDataFetcher_14480_main] - [调试] 验证完成: 原始=3586, 有效=3586, 无效=0
2025-08-25 14:51:38 [INFO] [MarketDataFetcher_14480_main] - [调试] 准备插入 3586 条数据到stock_tick_data表
2025-08-25 14:51:38 [INFO] [MarketDataFetcher_14480_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 14:51:14'), 'stock_code': '000096', 'price': 12.32, 'volume': 99735, 'amount': 122633032.0, 'open': 12.26, 'high': 12.34, 'low': 12.22, 'last_close': 12.26, 'cur_vol': 40, 'change': 0.0, 'bid1': 12.31, 'ask1': 12.32, 'bid_vol1': 1591, 'ask_vol1': 1790, 'bid2': 12.3, 'ask2': 12.33, 'bid_vol2': 1143, 'ask_vol2': 2318, 'bid3': 12.290000000000001, 'ask3': 12.34, 'bid_vol3': 945, 'ask_vol3': 1996, 'bid4': 12.280000000000001, 'ask4': 12.35, 'bid_vol4': 1135, 'ask_vol4': 1634, 'bid5': 12.27, 'ask5': 12.36, 'bid_vol5': 1185, 'ask_vol5': 1057}
