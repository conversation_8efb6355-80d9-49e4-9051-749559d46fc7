﻿2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - 成功加载配置文件: config/main.toml
2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - 开始加载股票列表...
2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - 正在从数据库加载股票列表...
2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - [初始化] 股票列表加载完成: 4394 只股票
2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - [初始化] 有效股票代码: 4394 个
2025-08-25 13:59:00 [INFO] [MarketDataFetcher_5629_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-08-25 13:59:01 [INFO] [MarketDataFetcher_5629_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-08-25 13:59:01 [INFO] [MarketDataFetcher_5629_main] - 正在初始化数据库管理器...
2025-08-25 13:59:01 [INFO] [MarketDataFetcher_5629_main] - [成功] 数据库管理器初始化成功
2025-08-25 13:59:01 [INFO] [MarketDataFetcher_5629_main] - 📦 使用数据库缓存机制
2025-08-25 13:59:01 [INFO] [MarketDataFetcher_5629_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-08-25 13:59:01 [INFO] [MarketDataFetcher_5629_main] - 开始加载股票列表...
2025-08-25 13:59:01 [INFO] [MarketDataFetcher_5629_main] - 正在从数据库加载股票列表...
2025-08-25 13:59:01 [INFO] [MarketDataFetcher_5629_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 13:59:01 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 13:59:01 [INFO] [MarketDataFetcher_5629_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 13:59:01 [INFO] [MarketDataFetcher_5629_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 13:59:01 [INFO] [MarketDataFetcher_5629_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 13:59:01 [INFO] [MarketDataFetcher_5629_main] - 定时任务线程启动
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - [调试] 处理前数据量: 4394
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - [调试] 处理后数据量: 4394
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - [调试] 开始验证 4394 条tick数据
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:52'), 'stock_code': '000096', 'price': 12.3, 'volume': 84245, 'amount': 103564688.0, 'open': 12.26, 'high': 12.34, 'low': 12.22, 'last_close': 12.26, 'cur_vol': 1, 'change': 0.0, 'bid1': 12.290000000000001, 'ask1': 12.3, 'bid_vol1': 72, 'ask_vol1': 1420, 'bid2': 12.280000000000001, 'ask2': 12.31, 'bid_vol2': 367, 'ask_vol2': 1197, 'bid3': 12.27, 'ask3': 12.32, 'bid_vol3': 603, 'ask_vol3': 951, 'bid4': 12.26, 'ask4': 12.33, 'bid_vol4': 2560, 'ask_vol4': 1112, 'bid5': 12.25, 'ask5': 12.34, 'bid_vol5': 979, 'ask_vol5': 1405}
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:58:45'), 'stock_code': '000099', 'price': 24.35, 'volume': 337176, 'amount': 813864960.0, 'open': 23.98, 'high': 24.45, 'low': 23.75, 'last_close': 23.85, 'cur_vol': 96, 'change': 0.0, 'bid1': 24.34, 'ask1': 24.35, 'bid_vol1': 30, 'ask_vol1': 30, 'bid2': 24.330000000000002, 'ask2': 24.36, 'bid_vol2': 258, 'ask_vol2': 133, 'bid3': 24.32, 'ask3': 24.37, 'bid_vol3': 57, 'ask_vol3': 212, 'bid4': 24.310000000000002, 'ask4': 24.38, 'bid_vol4': 116, 'ask_vol4': 735, 'bid5': 24.3, 'ask5': 24.39, 'bid_vol5': 289, 'ask_vol5': 713}
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:57'), 'stock_code': '000100', 'price': 4.7, 'volume': 4759197, 'amount': 2256056064.0, 'open': 4.82, 'high': 4.86, 'low': 4.68, 'last_close': 4.78, 'cur_vol': 5, 'change': 0.0, 'bid1': 4.7, 'ask1': 4.71, 'bid_vol1': 36868, 'ask_vol1': 19534, 'bid2': 4.69, 'ask2': 4.72, 'bid_vol2': 61575, 'ask_vol2': 24422, 'bid3': 4.68, 'ask3': 4.73, 'bid_vol3': 108786, 'ask_vol3': 16639, 'bid4': 4.67, 'ask4': 4.74, 'bid_vol4': 39143, 'ask_vol4': 19245, 'bid5': 4.66, 'ask5': 4.75, 'bid_vol5': 48175, 'ask_vol5': 35783}
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - [调试] 验证完成: 原始=4394, 有效=4390, 无效=4
2025-08-25 13:59:03 [WARNING] [MarketDataFetcher_5629_main] - 过滤掉 4 条无效数据
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - [调试] 准备插入 4390 条数据到stock_tick_data表
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:52'), 'stock_code': '000096', 'price': 12.3, 'volume': 84245, 'amount': 103564688.0, 'open': 12.26, 'high': 12.34, 'low': 12.22, 'last_close': 12.26, 'cur_vol': 1, 'change': 0.0, 'bid1': 12.290000000000001, 'ask1': 12.3, 'bid_vol1': 72, 'ask_vol1': 1420, 'bid2': 12.280000000000001, 'ask2': 12.31, 'bid_vol2': 367, 'ask_vol2': 1197, 'bid3': 12.27, 'ask3': 12.32, 'bid_vol3': 603, 'ask_vol3': 951, 'bid4': 12.26, 'ask4': 12.33, 'bid_vol4': 2560, 'ask_vol4': 1112, 'bid5': 12.25, 'ask5': 12.34, 'bid_vol5': 979, 'ask_vol5': 1405}
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据库插入成功: 4390 条数据
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - [成功] 成功保存 4394/4394 条Tick数据
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - [启动] 性能优化报告:
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - 保存Tick数据完成，共 4394 条记录，耗时: 0.61秒
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - 处理完成，时间: 13:59:03, 股票数: 4394
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 2462.40 毫秒，每秒处理股票数: 1784.44
2025-08-25 13:59:03 [INFO] [MarketDataFetcher_5629_main] - [目标] 本次获取和保存数据总耗时: 2.46s (网络: 2.46s, 处理: -0.00s, 数据库: 2.46s)
2025-08-25 13:59:03 [WARNING] [MarketDataFetcher_5629_main] - [警告] 总耗时 2.46s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 13:59:03 [WARNING] [MarketDataFetcher_5629_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 13:59:04 [INFO] [MarketDataFetcher_5629_main] - [调试] 处理前数据量: 4394
2025-08-25 13:59:04 [INFO] [MarketDataFetcher_5629_main] - [调试] 处理后数据量: 2544
2025-08-25 13:59:04 [INFO] [MarketDataFetcher_5629_main] - [成功] 成功保存 2544/2544 条Tick数据
2025-08-25 13:59:04 [INFO] [MarketDataFetcher_5629_main] - [启动] 性能优化报告:
2025-08-25 13:59:04 [INFO] [MarketDataFetcher_5629_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:59:04 [INFO] [MarketDataFetcher_5629_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:59:04 [INFO] [MarketDataFetcher_5629_main] - 保存Tick数据完成，共 2544 条记录，耗时: 0.10秒
2025-08-25 13:59:04 [INFO] [MarketDataFetcher_5629_main] - 处理完成，时间: 13:59:04, 股票数: 4394
2025-08-25 13:59:04 [INFO] [MarketDataFetcher_5629_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 859.79 毫秒，每秒处理股票数: 5110.54
2025-08-25 13:59:04 [INFO] [MarketDataFetcher_5629_main] - [目标] 本次获取和保存数据总耗时: 0.86s (网络: 0.86s, 处理: -0.00s, 数据库: 0.86s)
2025-08-25 13:59:05 [INFO] [MarketDataFetcher_5629_main] - [调试] 处理前数据量: 4394
2025-08-25 13:59:05 [INFO] [MarketDataFetcher_5629_main] - [调试] 处理后数据量: 892
2025-08-25 13:59:05 [INFO] [MarketDataFetcher_5629_main] - [调试] 开始验证 3436 条tick数据
2025-08-25 13:59:05 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:54'), 'stock_code': '000001', 'price': 12.44, 'volume': 2729606, 'amount': 3368909824.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 114, 'change': 0.009999999999999787, 'bid1': 12.43, 'ask1': 12.44, 'bid_vol1': 98, 'ask_vol1': 4341, 'bid2': 12.42, 'ask2': 12.450000000000001, 'bid_vol2': 1754, 'ask_vol2': 6722, 'bid3': 12.41, 'ask3': 12.46, 'bid_vol3': 7562, 'ask_vol3': 1924, 'bid4': 12.4, 'ask4': 12.47, 'bid_vol4': 9324, 'ask_vol4': 2096, 'bid5': 12.39, 'ask5': 12.48, 'bid_vol5': 4718, 'ask_vol5': 4516}
2025-08-25 13:59:05 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:58:57'), 'stock_code': '000002', 'price': 7.19, 'volume': 7155083, 'amount': 5068388864.0, 'open': 6.66, 'high': 7.22, 'low': 6.65, 'last_close': 6.5600000000000005, 'cur_vol': 397, 'change': -0.009999999999999787, 'bid1': 7.19, 'ask1': 7.2, 'bid_vol1': 705, 'ask_vol1': 15505, 'bid2': 7.18, 'ask2': 7.21, 'bid_vol2': 9430, 'ask_vol2': 17807, 'bid3': 7.17, 'ask3': 7.22, 'bid_vol3': 11905, 'ask_vol3': 125070, 'bid4': 7.16, 'ask4': 0.0, 'bid_vol4': 11358, 'ask_vol4': 0, 'bid5': 7.15, 'ask5': 0.0, 'bid_vol5': 22487, 'ask_vol5': 0}
2025-08-25 13:59:05 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:57'), 'stock_code': '000006', 'price': 7.22, 'volume': 510529, 'amount': 372688576.0, 'open': 7.29, 'high': 7.45, 'low': 7.15, 'last_close': 7.3, 'cur_vol': 7, 'change': 0.009999999999999787, 'bid1': 7.21, 'ask1': 7.22, 'bid_vol1': 2057, 'ask_vol1': 1028, 'bid2': 7.2, 'ask2': 7.23, 'bid_vol2': 5970, 'ask_vol2': 1397, 'bid3': 7.19, 'ask3': 7.24, 'bid_vol3': 3386, 'ask_vol3': 720, 'bid4': 7.18, 'ask4': 7.25, 'bid_vol4': 2874, 'ask_vol4': 535, 'bid5': 7.17, 'ask5': 7.26, 'bid_vol5': 1194, 'ask_vol5': 172}
2025-08-25 13:59:05 [INFO] [MarketDataFetcher_5629_main] - [调试] 验证完成: 原始=3436, 有效=3436, 无效=0
2025-08-25 13:59:05 [INFO] [MarketDataFetcher_5629_main] - [调试] 准备插入 3436 条数据到stock_tick_data表
2025-08-25 13:59:05 [INFO] [MarketDataFetcher_5629_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:54'), 'stock_code': '000001', 'price': 12.44, 'volume': 2729606, 'amount': 3368909824.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 114, 'change': 0.009999999999999787, 'bid1': 12.43, 'ask1': 12.44, 'bid_vol1': 98, 'ask_vol1': 4341, 'bid2': 12.42, 'ask2': 12.450000000000001, 'bid_vol2': 1754, 'ask_vol2': 6722, 'bid3': 12.41, 'ask3': 12.46, 'bid_vol3': 7562, 'ask_vol3': 1924, 'bid4': 12.4, 'ask4': 12.47, 'bid_vol4': 9324, 'ask_vol4': 2096, 'bid5': 12.39, 'ask5': 12.48, 'bid_vol5': 4718, 'ask_vol5': 4516}
2025-08-25 14:43:03 [ERROR] [MarketDataFetcher_5629_main] - [调试] 数据库管理器插入失败: 3436 条数据
2025-08-25 14:43:03 [ERROR] [MarketDataFetcher_5629_main] - [失败] 缓冲区刷新失败: 3436 条数据
2025-08-25 14:43:03 [ERROR] [MarketDataFetcher_5629_main] - [失败] 保存Tick数据失败
2025-08-25 14:43:03 [INFO] [MarketDataFetcher_5629_main] - [启动] 性能优化报告:
2025-08-25 14:43:03 [INFO] [MarketDataFetcher_5629_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 14:43:03 [INFO] [MarketDataFetcher_5629_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 14:43:03 [INFO] [MarketDataFetcher_5629_main] - 保存Tick数据完成，共 892 条记录，耗时: 2638.25秒
2025-08-25 14:43:03 [INFO] [MarketDataFetcher_5629_main] - 处理完成，时间: 13:59:05, 股票数: 4394
2025-08-25 14:43:03 [INFO] [MarketDataFetcher_5629_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 2639002.13 毫秒，每秒处理股票数: 1.67
2025-08-25 14:43:03 [INFO] [MarketDataFetcher_5629_main] - [目标] 本次获取和保存数据总耗时: 2639.00s (网络: 2639.00s, 处理: -0.00s, 数据库: 2639.00s)
2025-08-25 14:43:03 [WARNING] [MarketDataFetcher_5629_main] - [警告] 总耗时 2639.00s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 14:43:03 [WARNING] [MarketDataFetcher_5629_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - [调试] 处理前数据量: 4394
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - [调试] 处理后数据量: 4390
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - [调试] 开始验证 4390 条tick数据
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 14:42:54'), 'stock_code': '000001', 'price': 12.43, 'volume': 2914171, 'amount': 3598423040.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 425, 'change': -0.009999999999999787, 'bid1': 12.43, 'ask1': 12.44, 'bid_vol1': 1970, 'ask_vol1': 2512, 'bid2': 12.42, 'ask2': 12.450000000000001, 'bid_vol2': 4735, 'ask_vol2': 9948, 'bid3': 12.41, 'ask3': 12.46, 'bid_vol3': 7008, 'ask_vol3': 4390, 'bid4': 12.4, 'ask4': 12.47, 'bid_vol4': 12498, 'ask_vol4': 3974, 'bid5': 12.39, 'ask5': 12.48, 'bid_vol5': 3196, 'ask_vol5': 6447}
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 14:42:57'), 'stock_code': '000002', 'price': 7.18, 'volume': 7665101, 'amount': 5433913856.0, 'open': 6.66, 'high': 7.22, 'low': 6.65, 'last_close': 6.5600000000000005, 'cur_vol': 676, 'change': -0.010000000000000675, 'bid1': 7.17, 'ask1': 7.18, 'bid_vol1': 4949, 'ask_vol1': 6282, 'bid2': 7.16, 'ask2': 7.19, 'bid_vol2': 13038, 'ask_vol2': 11489, 'bid3': 7.15, 'ask3': 7.2, 'bid_vol3': 12825, 'ask_vol3': 21453, 'bid4': 7.140000000000001, 'ask4': 7.21, 'bid_vol4': 13643, 'ask_vol4': 24817, 'bid5': 7.13, 'ask5': 7.22, 'bid_vol5': 19797, 'ask_vol5': 152385}
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 14:42:57'), 'stock_code': '000006', 'price': 7.33, 'volume': 574708, 'amount': 419368896.0, 'open': 7.29, 'high': 7.45, 'low': 7.15, 'last_close': 7.3, 'cur_vol': 515, 'change': 0.11000000000000032, 'bid1': 7.32, 'ask1': 7.33, 'bid_vol1': 759, 'ask_vol1': 74, 'bid2': 7.3100000000000005, 'ask2': 7.34, 'bid_vol2': 1018, 'ask_vol2': 1477, 'bid3': 7.3, 'ask3': 7.3500000000000005, 'bid_vol3': 3675, 'ask_vol3': 2567, 'bid4': 7.29, 'ask4': 7.36, 'bid_vol4': 1293, 'ask_vol4': 870, 'bid5': 7.28, 'ask5': 7.37, 'bid_vol5': 1357, 'ask_vol5': 499}
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - [调试] 验证完成: 原始=4390, 有效=4390, 无效=0
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - [调试] 准备插入 4390 条数据到stock_tick_data表
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 14:42:54'), 'stock_code': '000001', 'price': 12.43, 'volume': 2914171, 'amount': 3598423040.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 425, 'change': -0.009999999999999787, 'bid1': 12.43, 'ask1': 12.44, 'bid_vol1': 1970, 'ask_vol1': 2512, 'bid2': 12.42, 'ask2': 12.450000000000001, 'bid_vol2': 4735, 'ask_vol2': 9948, 'bid3': 12.41, 'ask3': 12.46, 'bid_vol3': 7008, 'ask_vol3': 4390, 'bid4': 12.4, 'ask4': 12.47, 'bid_vol4': 12498, 'ask_vol4': 3974, 'bid5': 12.39, 'ask5': 12.48, 'bid_vol5': 3196, 'ask_vol5': 6447}
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据库插入成功: 4390 条数据
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - [成功] 成功保存 4390/4390 条Tick数据
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - [启动] 性能优化报告:
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - 保存Tick数据完成，共 4390 条记录，耗时: 0.67秒
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - 处理完成，时间: 14:43:04, 股票数: 4394
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1412.33 毫秒，每秒处理股票数: 3111.18
2025-08-25 14:43:04 [INFO] [MarketDataFetcher_5629_main] - [目标] 本次获取和保存数据总耗时: 1.41s (网络: 1.41s, 处理: -0.00s, 数据库: 1.41s)
2025-08-25 14:43:05 [INFO] [MarketDataFetcher_5629_main] - [调试] 处理前数据量: 4394
2025-08-25 14:43:05 [INFO] [MarketDataFetcher_5629_main] - [调试] 处理后数据量: 1693
2025-08-25 14:43:05 [INFO] [MarketDataFetcher_5629_main] - [成功] 成功保存 1693/1693 条Tick数据
2025-08-25 14:43:05 [INFO] [MarketDataFetcher_5629_main] - [启动] 性能优化报告:
2025-08-25 14:43:05 [INFO] [MarketDataFetcher_5629_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 14:43:05 [INFO] [MarketDataFetcher_5629_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 14:43:05 [INFO] [MarketDataFetcher_5629_main] - 保存Tick数据完成，共 1693 条记录，耗时: 0.09秒
2025-08-25 14:43:05 [INFO] [MarketDataFetcher_5629_main] - 处理完成，时间: 14:43:05, 股票数: 4394
2025-08-25 14:43:05 [INFO] [MarketDataFetcher_5629_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 822.30 毫秒，每秒处理股票数: 5343.57
2025-08-25 14:43:05 [INFO] [MarketDataFetcher_5629_main] - [目标] 本次获取和保存数据总耗时: 0.82s (网络: 0.82s, 处理: -0.00s, 数据库: 0.82s)
2025-08-25 14:43:06 [INFO] [MarketDataFetcher_5629_main] - [调试] 处理前数据量: 4394
2025-08-25 14:43:06 [INFO] [MarketDataFetcher_5629_main] - [调试] 处理后数据量: 365
2025-08-25 14:43:06 [INFO] [MarketDataFetcher_5629_main] - [调试] 开始验证 2058 条tick数据
2025-08-25 14:43:06 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 14:42:58'), 'stock_code': '002175', 'price': 4.8100000000000005, 'volume': 648190, 'amount': 312035776.0, 'open': 4.83, 'high': 4.87, 'low': 4.75, 'last_close': 4.82, 'cur_vol': 31, 'change': 0.010000000000000675, 'bid1': 4.8, 'ask1': 4.8100000000000005, 'bid_vol1': 3544, 'ask_vol1': 8222, 'bid2': 4.79, 'ask2': 4.82, 'bid_vol2': 4835, 'ask_vol2': 5409, 'bid3': 4.78, 'ask3': 4.83, 'bid_vol3': 3873, 'ask_vol3': 4826, 'bid4': 4.7700000000000005, 'ask4': 4.84, 'bid_vol4': 2775, 'ask_vol4': 4250, 'bid5': 4.76, 'ask5': 4.8500000000000005, 'bid_vol5': 7926, 'ask_vol5': 6310}
2025-08-25 14:43:06 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 14:42:56'), 'stock_code': '002176', 'price': 9.14, 'volume': 953667, 'amount': 870660480.0, 'open': 9.0, 'high': 9.27, 'low': 9.0, 'last_close': 9.0, 'cur_vol': 165, 'change': 0.0, 'bid1': 9.13, 'ask1': 9.14, 'bid_vol1': 4968, 'ask_vol1': 890, 'bid2': 9.120000000000001, 'ask2': 9.15, 'bid_vol2': 2445, 'ask_vol2': 3936, 'bid3': 9.11, 'ask3': 9.16, 'bid_vol3': 4443, 'ask_vol3': 4339, 'bid4': 9.1, 'ask4': 9.17, 'bid_vol4': 5712, 'ask_vol4': 2045, 'bid5': 9.09, 'ask5': 9.18, 'bid_vol5': 2742, 'ask_vol5': 2828}
2025-08-25 14:43:06 [INFO] [MarketDataFetcher_5629_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 14:42:56'), 'stock_code': '002177', 'price': 9.53, 'volume': 3726652, 'amount': 3663080704.0, 'open': 10.120000000000001, 'high': 10.4, 'low': 9.4, 'last_close': 10.0, 'cur_vol': 181, 'change': 0.0, 'bid1': 9.52, 'ask1': 9.53, 'bid_vol1': 1239, 'ask_vol1': 621, 'bid2': 9.51, 'ask2': 9.540000000000001, 'bid_vol2': 2231, 'ask_vol2': 1825, 'bid3': 9.5, 'ask3': 9.55, 'bid_vol3': 7112, 'ask_vol3': 2400, 'bid4': 9.49, 'ask4': 9.56, 'bid_vol4': 703, 'ask_vol4': 784, 'bid5': 9.48, 'ask5': 9.57, 'bid_vol5': 1386, 'ask_vol5': 273}
2025-08-25 14:43:06 [INFO] [MarketDataFetcher_5629_main] - [调试] 验证完成: 原始=2058, 有效=2058, 无效=0
2025-08-25 14:43:06 [INFO] [MarketDataFetcher_5629_main] - [调试] 准备插入 2058 条数据到stock_tick_data表
2025-08-25 14:43:06 [INFO] [MarketDataFetcher_5629_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 14:42:58'), 'stock_code': '002175', 'price': 4.8100000000000005, 'volume': 648190, 'amount': 312035776.0, 'open': 4.83, 'high': 4.87, 'low': 4.75, 'last_close': 4.82, 'cur_vol': 31, 'change': 0.010000000000000675, 'bid1': 4.8, 'ask1': 4.8100000000000005, 'bid_vol1': 3544, 'ask_vol1': 8222, 'bid2': 4.79, 'ask2': 4.82, 'bid_vol2': 4835, 'ask_vol2': 5409, 'bid3': 4.78, 'ask3': 4.83, 'bid_vol3': 3873, 'ask_vol3': 4826, 'bid4': 4.7700000000000005, 'ask4': 4.84, 'bid_vol4': 2775, 'ask_vol4': 4250, 'bid5': 4.76, 'ask5': 4.8500000000000005, 'bid_vol5': 7926, 'ask_vol5': 6310}
