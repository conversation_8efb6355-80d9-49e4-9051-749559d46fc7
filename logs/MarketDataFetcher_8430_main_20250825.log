﻿2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 成功加载配置文件: config/main.toml
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 开始加载股票列表...
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 正在从数据库加载股票列表...
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - [初始化] 股票列表加载完成: 4394 只股票
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - [初始化] 有效股票代码: 4394 个
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 正在初始化数据库管理器...
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - [成功] 数据库管理器初始化成功
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 📦 使用数据库缓存机制
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 开始加载股票列表...
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 正在从数据库加载股票列表...
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 14:12:13 [INFO] [MarketDataFetcher_8430_main] - 定时任务线程启动
2025-08-25 14:12:15 [INFO] [MarketDataFetcher_8430_main] - [调试] 处理前数据量: 4394
2025-08-25 14:12:15 [INFO] [MarketDataFetcher_8430_main] - [调试] 处理后数据量: 4394
2025-08-25 14:12:15 [INFO] [MarketDataFetcher_8430_main] - [调试] 开始验证 4394 条tick数据
2025-08-25 14:12:15 [INFO] [MarketDataFetcher_8430_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 14:11:59'), 'stock_code': '000001', 'price': 12.450000000000001, 'volume': 2771837, 'amount': 3421424384.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 141, 'change': 0.0, 'bid1': 12.44, 'ask1': 12.450000000000001, 'bid_vol1': 8225, 'ask_vol1': 7398, 'bid2': 12.43, 'ask2': 12.46, 'bid_vol2': 6187, 'ask_vol2': 3209, 'bid3': 12.42, 'ask3': 12.47, 'bid_vol3': 3669, 'ask_vol3': 2779, 'bid4': 12.41, 'ask4': 12.48, 'bid_vol4': 6495, 'ask_vol4': 5550, 'bid5': 12.4, 'ask5': 12.49, 'bid_vol5': 11215, 'ask_vol5': 5289}
2025-08-25 14:12:15 [INFO] [MarketDataFetcher_8430_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 14:12:02'), 'stock_code': '000002', 'price': 7.140000000000001, 'volume': 7348781, 'amount': 5207047680.0, 'open': 6.66, 'high': 7.22, 'low': 6.65, 'last_close': 6.5600000000000005, 'cur_vol': 363, 'change': 0.0, 'bid1': 7.140000000000001, 'ask1': 7.15, 'bid_vol1': 14555, 'ask_vol1': 4692, 'bid2': 7.13, 'ask2': 7.16, 'bid_vol2': 14829, 'ask_vol2': 7140, 'bid3': 7.12, 'ask3': 7.17, 'bid_vol3': 10235, 'ask_vol3': 2974, 'bid4': 7.11, 'ask4': 7.18, 'bid_vol4': 12329, 'ask_vol4': 6895, 'bid5': 7.1000000000000005, 'ask5': 7.19, 'bid_vol5': 24717, 'ask_vol5': 9703}
2025-08-25 14:12:15 [INFO] [MarketDataFetcher_8430_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 14:12:02'), 'stock_code': '000006', 'price': 7.23, 'volume': 526732, 'amount': 384368928.0, 'open': 7.29, 'high': 7.45, 'low': 7.15, 'last_close': 7.3, 'cur_vol': 8, 'change': 0.0, 'bid1': 7.22, 'ask1': 7.23, 'bid_vol1': 493, 'ask_vol1': 2370, 'bid2': 7.21, 'ask2': 7.24, 'bid_vol2': 1084, 'ask_vol2': 746, 'bid3': 7.2, 'ask3': 7.25, 'bid_vol3': 6140, 'ask_vol3': 712, 'bid4': 7.19, 'ask4': 7.26, 'bid_vol4': 5144, 'ask_vol4': 252, 'bid5': 7.18, 'ask5': 7.2700000000000005, 'bid_vol5': 2777, 'ask_vol5': 1227}
2025-08-25 14:12:15 [INFO] [MarketDataFetcher_8430_main] - [调试] 验证完成: 原始=4394, 有效=4390, 无效=4
2025-08-25 14:12:15 [WARNING] [MarketDataFetcher_8430_main] - 过滤掉 4 条无效数据
2025-08-25 14:12:15 [INFO] [MarketDataFetcher_8430_main] - [调试] 准备插入 4390 条数据到stock_tick_data表
2025-08-25 14:12:15 [INFO] [MarketDataFetcher_8430_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 14:11:59'), 'stock_code': '000001', 'price': 12.450000000000001, 'volume': 2771837, 'amount': 3421424384.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 141, 'change': 0.0, 'bid1': 12.44, 'ask1': 12.450000000000001, 'bid_vol1': 8225, 'ask_vol1': 7398, 'bid2': 12.43, 'ask2': 12.46, 'bid_vol2': 6187, 'ask_vol2': 3209, 'bid3': 12.42, 'ask3': 12.47, 'bid_vol3': 3669, 'ask_vol3': 2779, 'bid4': 12.41, 'ask4': 12.48, 'bid_vol4': 6495, 'ask_vol4': 5550, 'bid5': 12.4, 'ask5': 12.49, 'bid_vol5': 11215, 'ask_vol5': 5289}
