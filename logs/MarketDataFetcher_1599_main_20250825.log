﻿2025-08-25 13:55:24 [INFO] [MarketDataFetcher_1599_main] - 成功加载配置文件: config/main.toml
2025-08-25 13:55:24 [INFO] [MarketDataFetcher_1599_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-08-25 13:55:24 [INFO] [MarketDataFetcher_1599_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-08-25 13:55:24 [INFO] [MarketDataFetcher_1599_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-08-25 13:55:24 [INFO] [MarketDataFetcher_1599_main] - 开始加载股票列表...
2025-08-25 13:55:24 [INFO] [MarketDataFetcher_1599_main] - 正在从数据库加载股票列表...
2025-08-25 13:55:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 13:55:39 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 13:55:39 [INFO] [MarketDataFetcher_1599_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 13:55:39 [INFO] [MarketDataFetcher_1599_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 13:55:39 [INFO] [MarketDataFetcher_1599_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 13:55:39 [INFO] [MarketDataFetcher_1599_main] - [初始化] 股票列表加载完成: 4394 只股票
2025-08-25 13:55:39 [INFO] [MarketDataFetcher_1599_main] - [初始化] 有效股票代码: 4394 个
2025-08-25 13:55:39 [INFO] [MarketDataFetcher_1599_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-08-25 13:55:40 [INFO] [MarketDataFetcher_1599_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-08-25 13:55:40 [INFO] [MarketDataFetcher_1599_main] - 正在初始化数据库管理器...
2025-08-25 13:55:40 [INFO] [MarketDataFetcher_1599_main] - [成功] 数据库管理器初始化成功
2025-08-25 13:55:40 [INFO] [MarketDataFetcher_1599_main] - 📦 使用数据库缓存机制
2025-08-25 13:55:40 [INFO] [MarketDataFetcher_1599_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-08-25 13:55:40 [INFO] [MarketDataFetcher_1599_main] - 开始加载股票列表...
2025-08-25 13:55:40 [INFO] [MarketDataFetcher_1599_main] - 正在从数据库加载股票列表...
2025-08-25 13:55:40 [INFO] [MarketDataFetcher_1599_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 13:55:40 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 13:55:40 [INFO] [MarketDataFetcher_1599_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 13:55:40 [INFO] [MarketDataFetcher_1599_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 13:55:40 [INFO] [MarketDataFetcher_1599_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 13:55:40 [INFO] [MarketDataFetcher_1599_main] - 定时任务线程启动
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 4394
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 4394/4394 条Tick数据
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 4394 条记录，耗时: 0.13秒
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:55:42, 股票数: 4394
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1940.41 毫秒，每秒处理股票数: 2264.47
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.94s (网络: 1.94s, 处理: -0.00s, 数据库: 1.94s)
2025-08-25 13:55:42 [WARNING] [MarketDataFetcher_1599_main] - [警告] 总耗时 1.94s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 13:55:42 [WARNING] [MarketDataFetcher_1599_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1170
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 5564 条tick数据
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:55:21'), 'stock_code': '000523', 'price': 3.35, 'volume': 203789, 'amount': 68086840.0, 'open': 3.3200000000000003, 'high': 3.37, 'low': 3.31, 'last_close': 3.33, 'cur_vol': 1, 'change': 0.0, 'bid1': 3.34, 'ask1': 3.35, 'bid_vol1': 2023, 'ask_vol1': 12527, 'bid2': 3.33, 'ask2': 3.36, 'bid_vol2': 9181, 'ask_vol2': 16629, 'bid3': 3.3200000000000003, 'ask3': 3.37, 'bid_vol3': 9014, 'ask_vol3': 12557, 'bid4': 3.31, 'ask4': 3.38, 'bid_vol4': 6084, 'ask_vol4': 7862, 'bid5': 3.3000000000000003, 'ask5': 3.39, 'bid_vol5': 7221, 'ask_vol5': 6337}
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:55:15'), 'stock_code': '000524', 'price': 13.280000000000001, 'volume': 162775, 'amount': 215277056.0, 'open': 13.11, 'high': 13.39, 'low': 13.07, 'last_close': 13.1, 'cur_vol': 1, 'change': 0.0, 'bid1': 13.27, 'ask1': 13.280000000000001, 'bid_vol1': 64, 'ask_vol1': 38, 'bid2': 13.26, 'ask2': 13.290000000000001, 'bid_vol2': 220, 'ask_vol2': 134, 'bid3': 13.25, 'ask3': 13.3, 'bid_vol3': 492, 'ask_vol3': 544, 'bid4': 13.24, 'ask4': 13.31, 'bid_vol4': 756, 'ask_vol4': 349, 'bid5': 13.23, 'ask5': 13.32, 'bid_vol5': 347, 'ask_vol5': 110}
2025-08-25 13:55:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:55:19'), 'stock_code': '000525', 'price': 6.97, 'volume': 213727, 'amount': 149243008.0, 'open': 7.0200000000000005, 'high': 7.0200000000000005, 'low': 6.95, 'last_close': 7.0200000000000005, 'cur_vol': 30, 'change': 0.0, 'bid1': 6.97, 'ask1': 6.98, 'bid_vol1': 1570, 'ask_vol1': 4301, 'bid2': 6.96, 'ask2': 6.99, 'bid_vol2': 6446, 'ask_vol2': 2470, 'bid3': 6.95, 'ask3': 7.0, 'bid_vol3': 12114, 'ask_vol3': 5108, 'bid4': 6.94, 'ask4': 7.01, 'bid_vol4': 2563, 'ask_vol4': 5100, 'bid5': 6.93, 'ask5': 7.0200000000000005, 'bid_vol5': 3031, 'ask_vol5': 7719}
2025-08-25 13:55:43 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=5564, 有效=5560, 无效=4
2025-08-25 13:55:43 [WARNING] [MarketDataFetcher_1599_main] - 过滤掉 4 条无效数据
2025-08-25 13:55:43 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 5560 条数据到stock_tick_data表
2025-08-25 13:55:43 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:55:21'), 'stock_code': '000523', 'price': 3.35, 'volume': 203789, 'amount': 68086840.0, 'open': 3.3200000000000003, 'high': 3.37, 'low': 3.31, 'last_close': 3.33, 'cur_vol': 1, 'change': 0.0, 'bid1': 3.34, 'ask1': 3.35, 'bid_vol1': 2023, 'ask_vol1': 12527, 'bid2': 3.33, 'ask2': 3.36, 'bid_vol2': 9181, 'ask_vol2': 16629, 'bid3': 3.3200000000000003, 'ask3': 3.37, 'bid_vol3': 9014, 'ask_vol3': 12557, 'bid4': 3.31, 'ask4': 3.38, 'bid_vol4': 6084, 'ask_vol4': 7862, 'bid5': 3.3000000000000003, 'ask5': 3.39, 'bid_vol5': 7221, 'ask_vol5': 6337}
2025-08-25 13:55:43 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 5560 条数据
2025-08-25 13:55:43 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1170/1170 条Tick数据
2025-08-25 13:55:43 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:55:43 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:55:43 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:55:43 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1170 条记录，耗时: 0.72秒
2025-08-25 13:55:43 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:55:42, 股票数: 4394
2025-08-25 13:55:43 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1444.53 毫秒，每秒处理股票数: 3041.81
2025-08-25 13:55:43 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.44s (网络: 1.44s, 处理: -0.00s, 数据库: 1.44s)
2025-08-25 13:55:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:55:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2053
2025-08-25 13:55:44 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2053/2053 条Tick数据
2025-08-25 13:55:44 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:55:44 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:55:44 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:55:44 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2053 条记录，耗时: 0.12秒
2025-08-25 13:55:44 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:55:44, 股票数: 4394
2025-08-25 13:55:44 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 878.01 毫秒，每秒处理股票数: 5004.48
2025-08-25 13:55:44 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.88s (网络: 0.88s, 处理: 0.00s, 数据库: 0.88s)
2025-08-25 13:55:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:55:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1380
2025-08-25 13:55:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3433 条tick数据
2025-08-25 13:55:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:55:18'), 'stock_code': '002065', 'price': 10.790000000000001, 'volume': 2413434, 'amount': 2645306880.0, 'open': 10.92, 'high': 11.32, 'low': 10.65, 'last_close': 10.55, 'cur_vol': 1513, 'change': 0.010000000000001563, 'bid1': 10.790000000000001, 'ask1': 10.8, 'bid_vol1': 1673, 'ask_vol1': 2910, 'bid2': 10.78, 'ask2': 10.81, 'bid_vol2': 2072, 'ask_vol2': 1066, 'bid3': 10.77, 'ask3': 10.82, 'bid_vol3': 1223, 'ask_vol3': 1204, 'bid4': 10.76, 'ask4': 10.83, 'bid_vol4': 2168, 'ask_vol4': 1443, 'bid5': 10.75, 'ask5': 10.84, 'bid_vol5': 1653, 'ask_vol5': 592}
2025-08-25 13:55:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:55:19'), 'stock_code': '002073', 'price': 8.78, 'volume': 392567, 'amount': 347725952.0, 'open': 8.77, 'high': 8.950000000000001, 'low': 8.76, 'last_close': 8.77, 'cur_vol': 119, 'change': -0.010000000000001563, 'bid1': 8.78, 'ask1': 8.790000000000001, 'bid_vol1': 287, 'ask_vol1': 14, 'bid2': 8.77, 'ask2': 8.8, 'bid_vol2': 3172, 'ask_vol2': 210, 'bid3': 8.76, 'ask3': 8.81, 'bid_vol3': 4925, 'ask_vol3': 542, 'bid4': 8.75, 'ask4': 8.82, 'bid_vol4': 3599, 'ask_vol4': 420, 'bid5': 8.74, 'ask5': 8.83, 'bid_vol5': 971, 'ask_vol5': 239}
2025-08-25 13:55:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:55:06'), 'stock_code': '002074', 'price': 31.92, 'volume': 625317, 'amount': 2010067456.0, 'open': 31.91, 'high': 32.62, 'low': 31.75, 'last_close': 31.79, 'cur_vol': 55, 'change': 0.0, 'bid1': 31.91, 'ask1': 31.92, 'bid_vol1': 387, 'ask_vol1': 3, 'bid2': 31.900000000000002, 'ask2': 31.93, 'bid_vol2': 610, 'ask_vol2': 6, 'bid3': 31.89, 'ask3': 31.94, 'bid_vol3': 67, 'ask_vol3': 193, 'bid4': 31.88, 'ask4': 31.95, 'bid_vol4': 172, 'ask_vol4': 183, 'bid5': 31.87, 'ask5': 31.96, 'bid_vol5': 423, 'ask_vol5': 234}
2025-08-25 13:55:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3433, 有效=3433, 无效=0
2025-08-25 13:55:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3433 条数据到stock_tick_data表
2025-08-25 13:55:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:55:18'), 'stock_code': '002065', 'price': 10.790000000000001, 'volume': 2413434, 'amount': 2645306880.0, 'open': 10.92, 'high': 11.32, 'low': 10.65, 'last_close': 10.55, 'cur_vol': 1513, 'change': 0.010000000000001563, 'bid1': 10.790000000000001, 'ask1': 10.8, 'bid_vol1': 1673, 'ask_vol1': 2910, 'bid2': 10.78, 'ask2': 10.81, 'bid_vol2': 2072, 'ask_vol2': 1066, 'bid3': 10.77, 'ask3': 10.82, 'bid_vol3': 1223, 'ask_vol3': 1204, 'bid4': 10.76, 'ask4': 10.83, 'bid_vol4': 2168, 'ask_vol4': 1443, 'bid5': 10.75, 'ask5': 10.84, 'bid_vol5': 1653, 'ask_vol5': 592}
2025-08-25 13:55:47 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3433 条数据
2025-08-25 13:55:47 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1380/1380 条Tick数据
2025-08-25 13:55:47 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:55:47 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:55:47 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:55:47 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1380 条记录，耗时: 0.48秒
2025-08-25 13:55:47 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:55:46, 股票数: 4394
2025-08-25 13:55:47 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1208.33 毫秒，每秒处理股票数: 3636.43
2025-08-25 13:55:47 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.21s (网络: 1.21s, 处理: -0.00s, 数据库: 1.21s)
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1296
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1296 条tick数据
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:55:06'), 'stock_code': '300939', 'price': 34.4, 'volume': 28848, 'amount': 99721416.0, 'open': 34.86, 'high': 34.95, 'low': 34.26, 'last_close': 34.65, 'cur_vol': 56, 'change': 0.0, 'bid1': 34.39, 'ask1': 34.4, 'bid_vol1': 25, 'ask_vol1': 25, 'bid2': 34.38, 'ask2': 34.42, 'bid_vol2': 14, 'ask_vol2': 34, 'bid3': 34.37, 'ask3': 34.43, 'bid_vol3': 4, 'ask_vol3': 5, 'bid4': 34.36, 'ask4': 34.44, 'bid_vol4': 9, 'ask_vol4': 13, 'bid5': 34.35, 'ask5': 34.45, 'bid_vol5': 9, 'ask_vol5': 18}
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:55:19'), 'stock_code': '300942', 'price': 13.09, 'volume': 92974, 'amount': 122281432.0, 'open': 12.96, 'high': 13.450000000000001, 'low': 12.92, 'last_close': 12.9, 'cur_vol': 3, 'change': 0.0, 'bid1': 13.09, 'ask1': 13.1, 'bid_vol1': 31, 'ask_vol1': 275, 'bid2': 13.08, 'ask2': 13.120000000000001, 'bid_vol2': 1, 'ask_vol2': 64, 'bid3': 13.07, 'ask3': 13.13, 'bid_vol3': 1, 'ask_vol3': 13, 'bid4': 13.06, 'ask4': 13.14, 'bid_vol4': 49, 'ask_vol4': 70, 'bid5': 13.05, 'ask5': 13.15, 'bid_vol5': 210, 'ask_vol5': 64}
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:54:51'), 'stock_code': '300946', 'price': 59.4, 'volume': 36046, 'amount': 215671568.0, 'open': 59.18, 'high': 60.77, 'low': 59.03, 'last_close': 59.5, 'cur_vol': 2, 'change': -0.07000000000000028, 'bid1': 59.38, 'ask1': 59.4, 'bid_vol1': 3, 'ask_vol1': 2, 'bid2': 59.370000000000005, 'ask2': 59.49, 'bid_vol2': 5, 'ask_vol2': 85, 'bid3': 59.36, 'ask3': 59.5, 'bid_vol3': 34, 'ask_vol3': 61, 'bid4': 59.35, 'ask4': 59.51, 'bid_vol4': 42, 'ask_vol4': 129, 'bid5': 59.31, 'ask5': 59.54, 'bid_vol5': 5, 'ask_vol5': 1}
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1296, 有效=1296, 无效=0
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1296 条数据到stock_tick_data表
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:55:06'), 'stock_code': '300939', 'price': 34.4, 'volume': 28848, 'amount': 99721416.0, 'open': 34.86, 'high': 34.95, 'low': 34.26, 'last_close': 34.65, 'cur_vol': 56, 'change': 0.0, 'bid1': 34.39, 'ask1': 34.4, 'bid_vol1': 25, 'ask_vol1': 25, 'bid2': 34.38, 'ask2': 34.42, 'bid_vol2': 14, 'ask_vol2': 34, 'bid3': 34.37, 'ask3': 34.43, 'bid_vol3': 4, 'ask_vol3': 5, 'bid4': 34.36, 'ask4': 34.44, 'bid_vol4': 9, 'ask_vol4': 13, 'bid5': 34.35, 'ask5': 34.45, 'bid_vol5': 9, 'ask_vol5': 18}
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1296 条数据
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1296/1296 条Tick数据
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1296 条记录，耗时: 0.23秒
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:55:48, 股票数: 4394
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 967.80 毫秒，每秒处理股票数: 4540.21
2025-08-25 13:55:48 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.97s (网络: 0.97s, 处理: 0.00s, 数据库: 0.97s)
2025-08-25 13:55:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:55:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2111
2025-08-25 13:55:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2111 条tick数据
2025-08-25 13:55:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:55:18'), 'stock_code': '000860', 'price': 16.89, 'volume': 320769, 'amount': 536497952.0, 'open': 16.42, 'high': 17.11, 'low': 16.330000000000002, 'last_close': 16.39, 'cur_vol': 13, 'change': 0.0, 'bid1': 16.89, 'ask1': 16.9, 'bid_vol1': 366, 'ask_vol1': 525, 'bid2': 16.88, 'ask2': 16.91, 'bid_vol2': 473, 'ask_vol2': 230, 'bid3': 16.87, 'ask3': 16.92, 'bid_vol3': 145, 'ask_vol3': 435, 'bid4': 16.86, 'ask4': 16.93, 'bid_vol4': 401, 'ask_vol4': 359, 'bid5': 16.85, 'ask5': 16.94, 'bid_vol5': 1201, 'ask_vol5': 320}
2025-08-25 13:55:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:55:25'), 'stock_code': '000875', 'price': 5.2700000000000005, 'volume': 513921, 'amount': 272115296.0, 'open': 5.3, 'high': 5.32, 'low': 5.26, 'last_close': 5.3100000000000005, 'cur_vol': 11, 'change': 0.0, 'bid1': 5.2700000000000005, 'ask1': 5.28, 'bid_vol1': 3342, 'ask_vol1': 7579, 'bid2': 5.26, 'ask2': 5.29, 'bid_vol2': 21366, 'ask_vol2': 9369, 'bid3': 5.25, 'ask3': 5.3, 'bid_vol3': 15376, 'ask_vol3': 10092, 'bid4': 5.24, 'ask4': 5.3100000000000005, 'bid_vol4': 8291, 'ask_vol4': 10621, 'bid5': 5.23, 'ask5': 5.32, 'bid_vol5': 4374, 'ask_vol5': 18835}
2025-08-25 13:55:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:55:22'), 'stock_code': '000876', 'price': 10.040000000000001, 'volume': 383131, 'amount': 383128896.0, 'open': 9.99, 'high': 10.05, 'low': 9.94, 'last_close': 9.99, 'cur_vol': 6, 'change': 0.010000000000001563, 'bid1': 10.03, 'ask1': 10.040000000000001, 'bid_vol1': 3929, 'ask_vol1': 3415, 'bid2': 10.02, 'ask2': 10.05, 'bid_vol2': 1352, 'ask_vol2': 8104, 'bid3': 10.01, 'ask3': 10.06, 'bid_vol3': 2458, 'ask_vol3': 2442, 'bid4': 10.0, 'ask4': 10.07, 'bid_vol4': 2128, 'ask_vol4': 1516, 'bid5': 9.99, 'ask5': 10.08, 'bid_vol5': 2419, 'ask_vol5': 2796}
2025-08-25 13:55:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2111, 有效=2111, 无效=0
2025-08-25 13:55:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2111 条数据到stock_tick_data表
2025-08-25 13:55:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:55:18'), 'stock_code': '000860', 'price': 16.89, 'volume': 320769, 'amount': 536497952.0, 'open': 16.42, 'high': 17.11, 'low': 16.330000000000002, 'last_close': 16.39, 'cur_vol': 13, 'change': 0.0, 'bid1': 16.89, 'ask1': 16.9, 'bid_vol1': 366, 'ask_vol1': 525, 'bid2': 16.88, 'ask2': 16.91, 'bid_vol2': 473, 'ask_vol2': 230, 'bid3': 16.87, 'ask3': 16.92, 'bid_vol3': 145, 'ask_vol3': 435, 'bid4': 16.86, 'ask4': 16.93, 'bid_vol4': 401, 'ask_vol4': 359, 'bid5': 16.85, 'ask5': 16.94, 'bid_vol5': 1201, 'ask_vol5': 320}
2025-08-25 13:55:51 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2111 条数据
2025-08-25 13:55:51 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2111/2111 条Tick数据
2025-08-25 13:55:51 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:55:51 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:55:51 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:55:51 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2111 条记录，耗时: 0.33秒
2025-08-25 13:55:51 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:55:50, 股票数: 4394
2025-08-25 13:55:51 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1059.29 毫秒，每秒处理股票数: 4148.08
2025-08-25 13:55:51 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.06s (网络: 1.06s, 处理: -0.00s, 数据库: 1.06s)
2025-08-25 13:55:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:55:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1491
2025-08-25 13:55:52 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1491/1491 条Tick数据
2025-08-25 13:55:52 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:55:52 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:55:52 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:55:52 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1491 条记录，耗时: 0.09秒
2025-08-25 13:55:52 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:55:52, 股票数: 4394
2025-08-25 13:55:52 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 676.59 毫秒，每秒处理股票数: 6494.32
2025-08-25 13:55:52 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.68s (网络: 0.68s, 处理: -0.00s, 数据库: 0.68s)
2025-08-25 13:55:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:55:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1394
2025-08-25 13:55:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2885 条tick数据
2025-08-25 13:55:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:55:26'), 'stock_code': '002010', 'price': 6.23, 'volume': 416167, 'amount': 262134032.0, 'open': 6.32, 'high': 6.36, 'low': 6.22, 'last_close': 6.3, 'cur_vol': 1023, 'change': 0.0, 'bid1': 6.23, 'ask1': 6.24, 'bid_vol1': 5757, 'ask_vol1': 6745, 'bid2': 6.22, 'ask2': 6.25, 'bid_vol2': 6015, 'ask_vol2': 3586, 'bid3': 6.21, 'ask3': 6.26, 'bid_vol3': 4861, 'ask_vol3': 3030, 'bid4': 6.2, 'ask4': 6.2700000000000005, 'bid_vol4': 8170, 'ask_vol4': 2441, 'bid5': 6.19, 'ask5': 6.28, 'bid_vol5': 2994, 'ask_vol5': 3084}
2025-08-25 13:55:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:55:23'), 'stock_code': '002014', 'price': 11.61, 'volume': 35792, 'amount': 41413928.0, 'open': 11.51, 'high': 11.63, 'low': 11.47, 'last_close': 11.51, 'cur_vol': 164, 'change': 0.0, 'bid1': 11.61, 'ask1': 11.620000000000001, 'bid_vol1': 3, 'ask_vol1': 312, 'bid2': 11.6, 'ask2': 11.63, 'bid_vol2': 182, 'ask_vol2': 802, 'bid3': 11.59, 'ask3': 11.64, 'bid_vol3': 51, 'ask_vol3': 265, 'bid4': 11.58, 'ask4': 11.65, 'bid_vol4': 438, 'ask_vol4': 720, 'bid5': 11.57, 'ask5': 11.66, 'bid_vol5': 230, 'ask_vol5': 460}
2025-08-25 13:55:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:55:22'), 'stock_code': '002015', 'price': 13.07, 'volume': 1154016, 'amount': 1483790080.0, 'open': 12.34, 'high': 13.4, 'low': 12.280000000000001, 'last_close': 12.33, 'cur_vol': 8, 'change': 0.009999999999999787, 'bid1': 13.06, 'ask1': 13.07, 'bid_vol1': 25, 'ask_vol1': 182, 'bid2': 13.05, 'ask2': 13.08, 'bid_vol2': 287, 'ask_vol2': 562, 'bid3': 13.040000000000001, 'ask3': 13.09, 'bid_vol3': 186, 'ask_vol3': 172, 'bid4': 13.030000000000001, 'ask4': 13.1, 'bid_vol4': 305, 'ask_vol4': 615, 'bid5': 13.02, 'ask5': 13.11, 'bid_vol5': 943, 'ask_vol5': 702}
2025-08-25 13:55:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2885, 有效=2885, 无效=0
2025-08-25 13:55:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2885 条数据到stock_tick_data表
2025-08-25 13:55:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:55:26'), 'stock_code': '002010', 'price': 6.23, 'volume': 416167, 'amount': 262134032.0, 'open': 6.32, 'high': 6.36, 'low': 6.22, 'last_close': 6.3, 'cur_vol': 1023, 'change': 0.0, 'bid1': 6.23, 'ask1': 6.24, 'bid_vol1': 5757, 'ask_vol1': 6745, 'bid2': 6.22, 'ask2': 6.25, 'bid_vol2': 6015, 'ask_vol2': 3586, 'bid3': 6.21, 'ask3': 6.26, 'bid_vol3': 4861, 'ask_vol3': 3030, 'bid4': 6.2, 'ask4': 6.2700000000000005, 'bid_vol4': 8170, 'ask_vol4': 2441, 'bid5': 6.19, 'ask5': 6.28, 'bid_vol5': 2994, 'ask_vol5': 3084}
2025-08-25 13:55:55 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2885 条数据
2025-08-25 13:55:55 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1394/1394 条Tick数据
2025-08-25 13:55:55 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:55:55 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:55:55 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:55:55 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1394 条记录，耗时: 0.41秒
2025-08-25 13:55:55 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:55:54, 股票数: 4394
2025-08-25 13:55:55 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1114.85 毫秒，每秒处理股票数: 3941.35
2025-08-25 13:55:55 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.11s (网络: 1.11s, 处理: 0.00s, 数据库: 1.11s)
2025-08-25 13:55:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:55:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2400
2025-08-25 13:55:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2400 条tick数据
2025-08-25 13:55:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:55:20'), 'stock_code': '002232', 'price': 19.35, 'volume': 159318, 'amount': 304926912.0, 'open': 19.19, 'high': 19.400000000000002, 'low': 18.92, 'last_close': 19.080000000000002, 'cur_vol': 56, 'change': 0.0, 'bid1': 19.34, 'ask1': 19.35, 'bid_vol1': 109, 'ask_vol1': 193, 'bid2': 19.330000000000002, 'ask2': 19.36, 'bid_vol2': 657, 'ask_vol2': 184, 'bid3': 19.32, 'ask3': 19.37, 'bid_vol3': 385, 'ask_vol3': 342, 'bid4': 19.31, 'ask4': 19.38, 'bid_vol4': 331, 'ask_vol4': 478, 'bid5': 19.3, 'ask5': 19.39, 'bid_vol5': 328, 'ask_vol5': 581}
2025-08-25 13:55:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:55:27'), 'stock_code': '002234', 'price': 8.93, 'volume': 67941, 'amount': 60649856.0, 'open': 8.92, 'high': 8.99, 'low': 8.84, 'last_close': 8.93, 'cur_vol': 24, 'change': 0.009999999999999787, 'bid1': 8.92, 'ask1': 8.93, 'bid_vol1': 289, 'ask_vol1': 249, 'bid2': 8.91, 'ask2': 8.94, 'bid_vol2': 533, 'ask_vol2': 601, 'bid3': 8.9, 'ask3': 8.950000000000001, 'bid_vol3': 777, 'ask_vol3': 193, 'bid4': 8.89, 'ask4': 8.96, 'bid_vol4': 649, 'ask_vol4': 52, 'bid5': 8.88, 'ask5': 8.97, 'bid_vol5': 1834, 'ask_vol5': 255}
2025-08-25 13:55:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:55:21'), 'stock_code': '002236', 'price': 18.61, 'volume': 1173591, 'amount': 2207349248.0, 'open': 19.03, 'high': 19.13, 'low': 18.53, 'last_close': 18.66, 'cur_vol': 274, 'change': -0.010000000000001563, 'bid1': 18.61, 'ask1': 18.62, 'bid_vol1': 447, 'ask_vol1': 418, 'bid2': 18.6, 'ask2': 18.63, 'bid_vol2': 1962, 'ask_vol2': 338, 'bid3': 18.59, 'ask3': 18.64, 'bid_vol3': 777, 'ask_vol3': 885, 'bid4': 18.580000000000002, 'ask4': 18.650000000000002, 'bid_vol4': 1222, 'ask_vol4': 259, 'bid5': 18.57, 'ask5': 18.66, 'bid_vol5': 633, 'ask_vol5': 583}
2025-08-25 13:55:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2400, 有效=2400, 无效=0
2025-08-25 13:55:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2400 条数据到stock_tick_data表
2025-08-25 13:55:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:55:20'), 'stock_code': '002232', 'price': 19.35, 'volume': 159318, 'amount': 304926912.0, 'open': 19.19, 'high': 19.400000000000002, 'low': 18.92, 'last_close': 19.080000000000002, 'cur_vol': 56, 'change': 0.0, 'bid1': 19.34, 'ask1': 19.35, 'bid_vol1': 109, 'ask_vol1': 193, 'bid2': 19.330000000000002, 'ask2': 19.36, 'bid_vol2': 657, 'ask_vol2': 184, 'bid3': 19.32, 'ask3': 19.37, 'bid_vol3': 385, 'ask_vol3': 342, 'bid4': 19.31, 'ask4': 19.38, 'bid_vol4': 331, 'ask_vol4': 478, 'bid5': 19.3, 'ask5': 19.39, 'bid_vol5': 328, 'ask_vol5': 581}
2025-08-25 13:55:57 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2400 条数据
2025-08-25 13:55:57 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2400/2400 条Tick数据
2025-08-25 13:55:57 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:55:57 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:55:57 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:55:57 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2400 条记录，耗时: 0.37秒
2025-08-25 13:55:57 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:55:56, 股票数: 4394
2025-08-25 13:55:57 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1094.20 毫秒，每秒处理股票数: 4015.74
2025-08-25 13:55:57 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.09s (网络: 1.09s, 处理: -0.00s, 数据库: 1.09s)
2025-08-25 13:55:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:55:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1554
2025-08-25 13:55:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1554 条tick数据
2025-08-25 13:55:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:55:32'), 'stock_code': '000592', 'price': 3.34, 'volume': 1775605, 'amount': 595712768.0, 'open': 3.3000000000000003, 'high': 3.42, 'low': 3.24, 'last_close': 3.3200000000000003, 'cur_vol': 34, 'change': -0.010000000000000231, 'bid1': 3.34, 'ask1': 3.35, 'bid_vol1': 7134, 'ask_vol1': 2840, 'bid2': 3.33, 'ask2': 3.36, 'bid_vol2': 5357, 'ask_vol2': 8963, 'bid3': 3.3200000000000003, 'ask3': 3.37, 'bid_vol3': 10471, 'ask_vol3': 6107, 'bid4': 3.31, 'ask4': 3.38, 'bid_vol4': 7127, 'ask_vol4': 10307, 'bid5': 3.3000000000000003, 'ask5': 3.39, 'bid_vol5': 12955, 'ask_vol5': 17593}
2025-08-25 13:55:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:55:30'), 'stock_code': '000597', 'price': 5.8, 'volume': 190333, 'amount': 110298136.0, 'open': 5.76, 'high': 5.84, 'low': 5.75, 'last_close': 5.7700000000000005, 'cur_vol': 8, 'change': 0.0, 'bid1': 5.79, 'ask1': 5.8, 'bid_vol1': 875, 'ask_vol1': 2448, 'bid2': 5.78, 'ask2': 5.8100000000000005, 'bid_vol2': 5696, 'ask_vol2': 1696, 'bid3': 5.7700000000000005, 'ask3': 5.82, 'bid_vol3': 3215, 'ask_vol3': 1840, 'bid4': 5.76, 'ask4': 5.83, 'bid_vol4': 4541, 'ask_vol4': 5525, 'bid5': 5.75, 'ask5': 5.84, 'bid_vol5': 4180, 'ask_vol5': 6159}
2025-08-25 13:55:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:55:30'), 'stock_code': '000598', 'price': 6.95, 'volume': 298441, 'amount': 207807552.0, 'open': 6.96, 'high': 6.99, 'low': 6.93, 'last_close': 6.95, 'cur_vol': 13, 'change': 0.0, 'bid1': 6.95, 'ask1': 6.96, 'bid_vol1': 1893, 'ask_vol1': 4913, 'bid2': 6.94, 'ask2': 6.97, 'bid_vol2': 10449, 'ask_vol2': 1738, 'bid3': 6.93, 'ask3': 6.98, 'bid_vol3': 8679, 'ask_vol3': 3849, 'bid4': 6.92, 'ask4': 6.99, 'bid_vol4': 6691, 'ask_vol4': 4800, 'bid5': 6.91, 'ask5': 7.0, 'bid_vol5': 6553, 'ask_vol5': 5173}
2025-08-25 13:55:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1554, 有效=1554, 无效=0
2025-08-25 13:55:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1554 条数据到stock_tick_data表
2025-08-25 13:55:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:55:32'), 'stock_code': '000592', 'price': 3.34, 'volume': 1775605, 'amount': 595712768.0, 'open': 3.3000000000000003, 'high': 3.42, 'low': 3.24, 'last_close': 3.3200000000000003, 'cur_vol': 34, 'change': -0.010000000000000231, 'bid1': 3.34, 'ask1': 3.35, 'bid_vol1': 7134, 'ask_vol1': 2840, 'bid2': 3.33, 'ask2': 3.36, 'bid_vol2': 5357, 'ask_vol2': 8963, 'bid3': 3.3200000000000003, 'ask3': 3.37, 'bid_vol3': 10471, 'ask_vol3': 6107, 'bid4': 3.31, 'ask4': 3.38, 'bid_vol4': 7127, 'ask_vol4': 10307, 'bid5': 3.3000000000000003, 'ask5': 3.39, 'bid_vol5': 12955, 'ask_vol5': 17593}
2025-08-25 13:55:59 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1554 条数据
2025-08-25 13:55:59 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1554/1554 条Tick数据
2025-08-25 13:55:59 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:55:59 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:55:59 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:55:59 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1554 条记录，耗时: 0.26秒
2025-08-25 13:55:59 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:55:58, 股票数: 4394
2025-08-25 13:55:59 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1000.96 毫秒，每秒处理股票数: 4389.81
2025-08-25 13:55:59 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.00s (网络: 1.00s, 处理: 0.00s, 数据库: 1.00s)
2025-08-25 13:56:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1458
2025-08-25 13:56:00 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1458/1458 条Tick数据
2025-08-25 13:56:00 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:00 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:00 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:00 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1458 条记录，耗时: 0.09秒
2025-08-25 13:56:00 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:00, 股票数: 4394
2025-08-25 13:56:00 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 677.63 毫秒，每秒处理股票数: 6484.37
2025-08-25 13:56:00 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.68s (网络: 0.68s, 处理: 0.00s, 数据库: 0.68s)
2025-08-25 13:56:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2425
2025-08-25 13:56:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3883 条tick数据
2025-08-25 13:56:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:54:53'), 'stock_code': '300347', 'price': 68.28, 'volume': 114302, 'amount': 769586176.0, 'open': 66.6, 'high': 68.28, 'low': 66.2, 'last_close': 66.42, 'cur_vol': 3, 'change': 0.0, 'bid1': 68.26, 'ask1': 68.28, 'bid_vol1': 1, 'ask_vol1': 25, 'bid2': 68.25, 'ask2': 68.29, 'bid_vol2': 45, 'ask_vol2': 2, 'bid3': 68.23, 'ask3': 68.3, 'bid_vol3': 7, 'ask_vol3': 118, 'bid4': 68.22, 'ask4': 68.32000000000001, 'bid_vol4': 5, 'ask_vol4': 17, 'bid5': 68.2, 'ask5': 68.33, 'bid_vol5': 39, 'ask_vol5': 10}
2025-08-25 13:56:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:55:23'), 'stock_code': '300348', 'price': 18.53, 'volume': 956681, 'amount': 1754021760.0, 'open': 17.88, 'high': 18.68, 'low': 17.85, 'last_close': 17.88, 'cur_vol': 1, 'change': 0.0, 'bid1': 18.52, 'ask1': 18.53, 'bid_vol1': 370, 'ask_vol1': 39, 'bid2': 18.51, 'ask2': 18.54, 'bid_vol2': 244, 'ask_vol2': 63, 'bid3': 18.5, 'ask3': 18.55, 'bid_vol3': 1170, 'ask_vol3': 98, 'bid4': 18.490000000000002, 'ask4': 18.56, 'bid_vol4': 310, 'ask_vol4': 49, 'bid5': 18.48, 'ask5': 18.57, 'bid_vol5': 215, 'ask_vol5': 112}
2025-08-25 13:56:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:55:26'), 'stock_code': '300349', 'price': 12.85, 'volume': 72647, 'amount': 93757568.0, 'open': 12.98, 'high': 12.99, 'low': 12.790000000000001, 'last_close': 12.91, 'cur_vol': 2, 'change': 0.009999999999999787, 'bid1': 12.84, 'ask1': 12.85, 'bid_vol1': 383, 'ask_vol1': 414, 'bid2': 12.83, 'ask2': 12.86, 'bid_vol2': 140, 'ask_vol2': 35, 'bid3': 12.82, 'ask3': 12.870000000000001, 'bid_vol3': 19, 'ask_vol3': 70, 'bid4': 12.81, 'ask4': 12.88, 'bid_vol4': 168, 'ask_vol4': 96, 'bid5': 12.8, 'ask5': 12.89, 'bid_vol5': 732, 'ask_vol5': 301}
2025-08-25 13:56:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3883, 有效=3883, 无效=0
2025-08-25 13:56:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3883 条数据到stock_tick_data表
2025-08-25 13:56:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:54:53'), 'stock_code': '300347', 'price': 68.28, 'volume': 114302, 'amount': 769586176.0, 'open': 66.6, 'high': 68.28, 'low': 66.2, 'last_close': 66.42, 'cur_vol': 3, 'change': 0.0, 'bid1': 68.26, 'ask1': 68.28, 'bid_vol1': 1, 'ask_vol1': 25, 'bid2': 68.25, 'ask2': 68.29, 'bid_vol2': 45, 'ask_vol2': 2, 'bid3': 68.23, 'ask3': 68.3, 'bid_vol3': 7, 'ask_vol3': 118, 'bid4': 68.22, 'ask4': 68.32000000000001, 'bid_vol4': 5, 'ask_vol4': 17, 'bid5': 68.2, 'ask5': 68.33, 'bid_vol5': 39, 'ask_vol5': 10}
2025-08-25 13:56:03 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3883 条数据
2025-08-25 13:56:03 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2425/2425 条Tick数据
2025-08-25 13:56:03 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:03 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:03 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:03 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2425 条记录，耗时: 0.53秒
2025-08-25 13:56:03 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:02, 股票数: 4394
2025-08-25 13:56:03 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1317.80 毫秒，每秒处理股票数: 3334.35
2025-08-25 13:56:03 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.32s (网络: 1.32s, 处理: 0.00s, 数据库: 1.32s)
2025-08-25 13:56:04 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:04 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1586
2025-08-25 13:56:04 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1586/1586 条Tick数据
2025-08-25 13:56:04 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:04 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:04 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:04 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1586 条记录，耗时: 0.09秒
2025-08-25 13:56:04 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:04, 股票数: 4394
2025-08-25 13:56:04 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 855.05 毫秒，每秒处理股票数: 5138.90
2025-08-25 13:56:04 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.86s (网络: 0.86s, 处理: -0.00s, 数据库: 0.86s)
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1514
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3100 条tick数据
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:55:47'), 'stock_code': '002747', 'price': 24.51, 'volume': 406833, 'amount': 1002945728.0, 'open': 24.03, 'high': 24.98, 'low': 24.03, 'last_close': 24.150000000000002, 'cur_vol': 129, 'change': -0.019999999999999574, 'bid1': 24.51, 'ask1': 24.53, 'bid_vol1': 25, 'ask_vol1': 33, 'bid2': 24.5, 'ask2': 24.54, 'bid_vol2': 475, 'ask_vol2': 218, 'bid3': 24.490000000000002, 'ask3': 24.55, 'bid_vol3': 57, 'ask_vol3': 514, 'bid4': 24.48, 'ask4': 24.560000000000002, 'bid_vol4': 95, 'ask_vol4': 36, 'bid5': 24.47, 'ask5': 24.57, 'bid_vol5': 137, 'ask_vol5': 88}
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:55:57'), 'stock_code': '002753', 'price': 7.22, 'volume': 67972, 'amount': 49347852.0, 'open': 7.2700000000000005, 'high': 7.32, 'low': 7.19, 'last_close': 7.24, 'cur_vol': 2, 'change': -0.010000000000000675, 'bid1': 7.22, 'ask1': 7.23, 'bid_vol1': 723, 'ask_vol1': 149, 'bid2': 7.21, 'ask2': 7.24, 'bid_vol2': 106, 'ask_vol2': 331, 'bid3': 7.2, 'ask3': 7.25, 'bid_vol3': 1009, 'ask_vol3': 232, 'bid4': 7.19, 'ask4': 7.26, 'bid_vol4': 1520, 'ask_vol4': 400, 'bid5': 7.18, 'ask5': 7.2700000000000005, 'bid_vol5': 1064, 'ask_vol5': 71}
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:55:46'), 'stock_code': '002755', 'price': 25.64, 'volume': 72489, 'amount': 184832960.0, 'open': 25.3, 'high': 25.75, 'low': 25.11, 'last_close': 25.3, 'cur_vol': 94, 'change': 0.019999999999999574, 'bid1': 25.63, 'ask1': 25.650000000000002, 'bid_vol1': 5, 'ask_vol1': 154, 'bid2': 25.62, 'ask2': 25.66, 'bid_vol2': 7, 'ask_vol2': 79, 'bid3': 25.61, 'ask3': 25.68, 'bid_vol3': 42, 'ask_vol3': 34, 'bid4': 25.6, 'ask4': 25.69, 'bid_vol4': 52, 'ask_vol4': 57, 'bid5': 25.59, 'ask5': 25.7, 'bid_vol5': 19, 'ask_vol5': 155}
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3100, 有效=3100, 无效=0
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3100 条数据到stock_tick_data表
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:55:47'), 'stock_code': '002747', 'price': 24.51, 'volume': 406833, 'amount': 1002945728.0, 'open': 24.03, 'high': 24.98, 'low': 24.03, 'last_close': 24.150000000000002, 'cur_vol': 129, 'change': -0.019999999999999574, 'bid1': 24.51, 'ask1': 24.53, 'bid_vol1': 25, 'ask_vol1': 33, 'bid2': 24.5, 'ask2': 24.54, 'bid_vol2': 475, 'ask_vol2': 218, 'bid3': 24.490000000000002, 'ask3': 24.55, 'bid_vol3': 57, 'ask_vol3': 514, 'bid4': 24.48, 'ask4': 24.560000000000002, 'bid_vol4': 95, 'ask_vol4': 36, 'bid5': 24.47, 'ask5': 24.57, 'bid_vol5': 137, 'ask_vol5': 88}
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3100 条数据
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1514/1514 条Tick数据
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1514 条记录，耗时: 0.44秒
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:07, 股票数: 4394
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1804.71 毫秒，每秒处理股票数: 2434.74
2025-08-25 13:56:07 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.80s (网络: 1.80s, 处理: 0.00s, 数据库: 1.80s)
2025-08-25 13:56:07 [WARNING] [MarketDataFetcher_1599_main] - [警告] 总耗时 1.80s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2068
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2068 条tick数据
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:01'), 'stock_code': '002122', 'price': 4.07, 'volume': 800774, 'amount': 327725472.0, 'open': 4.0600000000000005, 'high': 4.12, 'low': 4.05, 'last_close': 4.07, 'cur_vol': 12, 'change': 0.0, 'bid1': 4.07, 'ask1': 4.08, 'bid_vol1': 6211, 'ask_vol1': 15293, 'bid2': 4.0600000000000005, 'ask2': 4.09, 'bid_vol2': 29621, 'ask_vol2': 11935, 'bid3': 4.05, 'ask3': 4.1, 'bid_vol3': 23790, 'ask_vol3': 12870, 'bid4': 4.04, 'ask4': 4.11, 'bid_vol4': 10586, 'ask_vol4': 22278, 'bid5': 4.03, 'ask5': 4.12, 'bid_vol5': 11513, 'ask_vol5': 87568}
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:55:53'), 'stock_code': '002123', 'price': 16.69, 'volume': 1280523, 'amount': 2139542272.0, 'open': 16.75, 'high': 17.35, 'low': 16.38, 'last_close': 16.61, 'cur_vol': 140, 'change': 0.0, 'bid1': 16.69, 'ask1': 16.7, 'bid_vol1': 473, 'ask_vol1': 818, 'bid2': 16.68, 'ask2': 16.71, 'bid_vol2': 393, 'ask_vol2': 272, 'bid3': 16.67, 'ask3': 16.72, 'bid_vol3': 285, 'ask_vol3': 408, 'bid4': 16.66, 'ask4': 16.73, 'bid_vol4': 550, 'ask_vol4': 591, 'bid5': 16.65, 'ask5': 16.740000000000002, 'bid_vol5': 397, 'ask_vol5': 685}
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:55:55'), 'stock_code': '002125', 'price': 13.83, 'volume': 329515, 'amount': 457649152.0, 'open': 13.75, 'high': 14.08, 'low': 13.700000000000001, 'last_close': 13.700000000000001, 'cur_vol': 8, 'change': 0.0, 'bid1': 13.82, 'ask1': 13.83, 'bid_vol1': 119, 'ask_vol1': 360, 'bid2': 13.81, 'ask2': 13.84, 'bid_vol2': 151, 'ask_vol2': 187, 'bid3': 13.8, 'ask3': 13.85, 'bid_vol3': 708, 'ask_vol3': 588, 'bid4': 13.790000000000001, 'ask4': 13.86, 'bid_vol4': 1234, 'ask_vol4': 267, 'bid5': 13.780000000000001, 'ask5': 13.870000000000001, 'bid_vol5': 658, 'ask_vol5': 235}
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2068, 有效=2068, 无效=0
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2068 条数据到stock_tick_data表
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:01'), 'stock_code': '002122', 'price': 4.07, 'volume': 800774, 'amount': 327725472.0, 'open': 4.0600000000000005, 'high': 4.12, 'low': 4.05, 'last_close': 4.07, 'cur_vol': 12, 'change': 0.0, 'bid1': 4.07, 'ask1': 4.08, 'bid_vol1': 6211, 'ask_vol1': 15293, 'bid2': 4.0600000000000005, 'ask2': 4.09, 'bid_vol2': 29621, 'ask_vol2': 11935, 'bid3': 4.05, 'ask3': 4.1, 'bid_vol3': 23790, 'ask_vol3': 12870, 'bid4': 4.04, 'ask4': 4.11, 'bid_vol4': 10586, 'ask_vol4': 22278, 'bid5': 4.03, 'ask5': 4.12, 'bid_vol5': 11513, 'ask_vol5': 87568}
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2068 条数据
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2068/2068 条Tick数据
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2068 条记录，耗时: 0.33秒
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:09, 股票数: 4394
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1724.52 毫秒，每秒处理股票数: 2547.95
2025-08-25 13:56:09 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.72s (网络: 1.72s, 处理: -0.00s, 数据库: 1.72s)
2025-08-25 13:56:09 [WARNING] [MarketDataFetcher_1599_main] - [警告] 总耗时 1.72s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 13:56:10 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:10 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1418
2025-08-25 13:56:10 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1418/1418 条Tick数据
2025-08-25 13:56:10 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:10 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:10 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:10 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1418 条记录，耗时: 0.09秒
2025-08-25 13:56:10 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:10, 股票数: 4394
2025-08-25 13:56:10 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 859.71 毫秒，每秒处理股票数: 5111.03
2025-08-25 13:56:10 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.86s (网络: 0.86s, 处理: -0.00s, 数据库: 0.86s)
2025-08-25 13:56:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1299
2025-08-25 13:56:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2717 条tick数据
2025-08-25 13:56:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:55:57'), 'stock_code': '002867', 'price': 13.24, 'volume': 62636, 'amount': 82824064.0, 'open': 13.24, 'high': 13.290000000000001, 'low': 13.18, 'last_close': 13.200000000000001, 'cur_vol': 2, 'change': 0.0, 'bid1': 13.23, 'ask1': 13.24, 'bid_vol1': 64, 'ask_vol1': 540, 'bid2': 13.22, 'ask2': 13.25, 'bid_vol2': 1101, 'ask_vol2': 319, 'bid3': 13.21, 'ask3': 13.26, 'bid_vol3': 1668, 'ask_vol3': 277, 'bid4': 13.200000000000001, 'ask4': 13.27, 'bid_vol4': 1325, 'ask_vol4': 312, 'bid5': 13.19, 'ask5': 13.280000000000001, 'bid_vol5': 1106, 'ask_vol5': 871}
2025-08-25 13:56:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:55:44'), 'stock_code': '002870', 'price': 34.31, 'volume': 24543, 'amount': 84647904.0, 'open': 34.62, 'high': 34.980000000000004, 'low': 34.22, 'last_close': 34.62, 'cur_vol': 8, 'change': 0.0, 'bid1': 34.31, 'ask1': 34.32, 'bid_vol1': 16, 'ask_vol1': 90, 'bid2': 34.300000000000004, 'ask2': 34.34, 'bid_vol2': 43, 'ask_vol2': 12, 'bid3': 34.28, 'ask3': 34.35, 'bid_vol3': 66, 'ask_vol3': 8, 'bid4': 34.27, 'ask4': 34.36, 'bid_vol4': 19, 'ask_vol4': 2, 'bid5': 34.26, 'ask5': 34.37, 'bid_vol5': 44, 'ask_vol5': 24}
2025-08-25 13:56:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:55:54'), 'stock_code': '002871', 'price': 17.89, 'volume': 333078, 'amount': 610103872.0, 'open': 19.5, 'high': 19.580000000000002, 'low': 17.5, 'last_close': 18.43, 'cur_vol': 7, 'change': 0.030000000000001137, 'bid1': 17.87, 'ask1': 17.89, 'bid_vol1': 1, 'ask_vol1': 32, 'bid2': 17.86, 'ask2': 17.900000000000002, 'bid_vol2': 303, 'ask_vol2': 5, 'bid3': 17.84, 'ask3': 17.91, 'bid_vol3': 10, 'ask_vol3': 96, 'bid4': 17.830000000000002, 'ask4': 17.92, 'bid_vol4': 36, 'ask_vol4': 24, 'bid5': 17.82, 'ask5': 17.93, 'bid_vol5': 173, 'ask_vol5': 117}
2025-08-25 13:56:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2717, 有效=2717, 无效=0
2025-08-25 13:56:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2717 条数据到stock_tick_data表
2025-08-25 13:56:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:55:57'), 'stock_code': '002867', 'price': 13.24, 'volume': 62636, 'amount': 82824064.0, 'open': 13.24, 'high': 13.290000000000001, 'low': 13.18, 'last_close': 13.200000000000001, 'cur_vol': 2, 'change': 0.0, 'bid1': 13.23, 'ask1': 13.24, 'bid_vol1': 64, 'ask_vol1': 540, 'bid2': 13.22, 'ask2': 13.25, 'bid_vol2': 1101, 'ask_vol2': 319, 'bid3': 13.21, 'ask3': 13.26, 'bid_vol3': 1668, 'ask_vol3': 277, 'bid4': 13.200000000000001, 'ask4': 13.27, 'bid_vol4': 1325, 'ask_vol4': 312, 'bid5': 13.19, 'ask5': 13.280000000000001, 'bid_vol5': 1106, 'ask_vol5': 871}
2025-08-25 13:56:13 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2717 条数据
2025-08-25 13:56:13 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1299/1299 条Tick数据
2025-08-25 13:56:13 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:13 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:13 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:13 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1299 条记录，耗时: 0.41秒
2025-08-25 13:56:13 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:12, 股票数: 4394
2025-08-25 13:56:13 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1121.69 毫秒，每秒处理股票数: 3917.30
2025-08-25 13:56:13 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.12s (网络: 1.12s, 处理: 0.00s, 数据库: 1.12s)
2025-08-25 13:56:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2122
2025-08-25 13:56:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2122 条tick数据
2025-08-25 13:56:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:01'), 'stock_code': '300513', 'price': 9.540000000000001, 'volume': 114760, 'amount': 109227600.0, 'open': 9.58, 'high': 9.65, 'low': 9.43, 'last_close': 9.57, 'cur_vol': 344, 'change': 0.0, 'bid1': 9.53, 'ask1': 9.540000000000001, 'bid_vol1': 236, 'ask_vol1': 152, 'bid2': 9.52, 'ask2': 9.55, 'bid_vol2': 272, 'ask_vol2': 673, 'bid3': 9.51, 'ask3': 9.56, 'bid_vol3': 15, 'ask_vol3': 389, 'bid4': 9.5, 'ask4': 9.57, 'bid_vol4': 69, 'ask_vol4': 385, 'bid5': 9.49, 'ask5': 9.58, 'bid_vol5': 3, 'ask_vol5': 422}
2025-08-25 13:56:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:55:58'), 'stock_code': '300514', 'price': 14.290000000000001, 'volume': 39852, 'amount': 57262824.0, 'open': 14.450000000000001, 'high': 14.59, 'low': 14.22, 'last_close': 14.450000000000001, 'cur_vol': 14, 'change': 0.0, 'bid1': 14.290000000000001, 'ask1': 14.3, 'bid_vol1': 12, 'ask_vol1': 131, 'bid2': 14.280000000000001, 'ask2': 14.31, 'bid_vol2': 105, 'ask_vol2': 31, 'bid3': 14.27, 'ask3': 14.33, 'bid_vol3': 246, 'ask_vol3': 31, 'bid4': 14.26, 'ask4': 14.34, 'bid_vol4': 15, 'ask_vol4': 66, 'bid5': 14.25, 'ask5': 14.35, 'bid_vol5': 36, 'ask_vol5': 473}
2025-08-25 13:56:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:55:42'), 'stock_code': '300516', 'price': 41.21, 'volume': 35358, 'amount': 145039072.0, 'open': 40.59, 'high': 41.59, 'low': 40.59, 'last_close': 40.57, 'cur_vol': 2, 'change': -0.00999999999999801, 'bid1': 41.21, 'ask1': 41.22, 'bid_vol1': 18, 'ask_vol1': 4, 'bid2': 41.2, 'ask2': 41.230000000000004, 'bid_vol2': 42, 'ask_vol2': 23, 'bid3': 41.19, 'ask3': 41.24, 'bid_vol3': 17, 'ask_vol3': 181, 'bid4': 41.17, 'ask4': 41.25, 'bid_vol4': 8, 'ask_vol4': 21, 'bid5': 41.15, 'ask5': 41.26, 'bid_vol5': 3, 'ask_vol5': 10}
2025-08-25 13:56:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2122, 有效=2122, 无效=0
2025-08-25 13:56:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2122 条数据到stock_tick_data表
2025-08-25 13:56:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:01'), 'stock_code': '300513', 'price': 9.540000000000001, 'volume': 114760, 'amount': 109227600.0, 'open': 9.58, 'high': 9.65, 'low': 9.43, 'last_close': 9.57, 'cur_vol': 344, 'change': 0.0, 'bid1': 9.53, 'ask1': 9.540000000000001, 'bid_vol1': 236, 'ask_vol1': 152, 'bid2': 9.52, 'ask2': 9.55, 'bid_vol2': 272, 'ask_vol2': 673, 'bid3': 9.51, 'ask3': 9.56, 'bid_vol3': 15, 'ask_vol3': 389, 'bid4': 9.5, 'ask4': 9.57, 'bid_vol4': 69, 'ask_vol4': 385, 'bid5': 9.49, 'ask5': 9.58, 'bid_vol5': 3, 'ask_vol5': 422}
2025-08-25 13:56:15 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2122 条数据
2025-08-25 13:56:15 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2122/2122 条Tick数据
2025-08-25 13:56:15 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:15 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:15 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:15 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2122 条记录，耗时: 0.37秒
2025-08-25 13:56:15 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:14, 股票数: 4394
2025-08-25 13:56:15 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1172.17 毫秒，每秒处理股票数: 3748.60
2025-08-25 13:56:15 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.17s (网络: 1.17s, 处理: 0.00s, 数据库: 1.17s)
2025-08-25 13:56:16 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:16 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1410
2025-08-25 13:56:16 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1410/1410 条Tick数据
2025-08-25 13:56:16 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:16 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:16 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:16 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1410 条记录，耗时: 0.09秒
2025-08-25 13:56:16 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:16, 股票数: 4394
2025-08-25 13:56:16 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 868.65 毫秒，每秒处理股票数: 5058.43
2025-08-25 13:56:16 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.87s (网络: 0.87s, 处理: 0.00s, 数据库: 0.87s)
2025-08-25 13:56:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1326
2025-08-25 13:56:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2736 条tick数据
2025-08-25 13:56:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:55:59'), 'stock_code': '002346', 'price': 16.29, 'volume': 54804, 'amount': 89536264.0, 'open': 16.32, 'high': 16.48, 'low': 16.25, 'last_close': 16.3, 'cur_vol': 11, 'change': 0.0, 'bid1': 16.28, 'ask1': 16.29, 'bid_vol1': 42, 'ask_vol1': 16, 'bid2': 16.27, 'ask2': 16.3, 'bid_vol2': 105, 'ask_vol2': 25, 'bid3': 16.26, 'ask3': 16.31, 'bid_vol3': 377, 'ask_vol3': 94, 'bid4': 16.25, 'ask4': 16.32, 'bid_vol4': 858, 'ask_vol4': 15, 'bid5': 16.240000000000002, 'ask5': 16.330000000000002, 'bid_vol5': 119, 'ask_vol5': 38}
2025-08-25 13:56:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:04'), 'stock_code': '002347', 'price': 7.3500000000000005, 'volume': 174751, 'amount': 129379688.0, 'open': 7.43, 'high': 7.49, 'low': 7.32, 'last_close': 7.38, 'cur_vol': 65, 'change': 0.010000000000000675, 'bid1': 7.34, 'ask1': 7.3500000000000005, 'bid_vol1': 1264, 'ask_vol1': 967, 'bid2': 7.33, 'ask2': 7.36, 'bid_vol2': 2348, 'ask_vol2': 59, 'bid3': 7.32, 'ask3': 7.37, 'bid_vol3': 3985, 'ask_vol3': 263, 'bid4': 7.3100000000000005, 'ask4': 7.38, 'bid_vol4': 3130, 'ask_vol4': 313, 'bid5': 7.3, 'ask5': 7.390000000000001, 'bid_vol5': 4393, 'ask_vol5': 377}
2025-08-25 13:56:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:00'), 'stock_code': '002351', 'price': 14.15, 'volume': 218592, 'amount': 311797184.0, 'open': 14.5, 'high': 14.51, 'low': 14.120000000000001, 'last_close': 14.35, 'cur_vol': 20, 'change': -0.009999999999999787, 'bid1': 14.15, 'ask1': 14.16, 'bid_vol1': 331, 'ask_vol1': 229, 'bid2': 14.14, 'ask2': 14.17, 'bid_vol2': 333, 'ask_vol2': 240, 'bid3': 14.13, 'ask3': 14.18, 'bid_vol3': 430, 'ask_vol3': 363, 'bid4': 14.120000000000001, 'ask4': 14.19, 'bid_vol4': 1390, 'ask_vol4': 495, 'bid5': 14.11, 'ask5': 14.200000000000001, 'bid_vol5': 1904, 'ask_vol5': 1062}
2025-08-25 13:56:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2736, 有效=2736, 无效=0
2025-08-25 13:56:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2736 条数据到stock_tick_data表
2025-08-25 13:56:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:55:59'), 'stock_code': '002346', 'price': 16.29, 'volume': 54804, 'amount': 89536264.0, 'open': 16.32, 'high': 16.48, 'low': 16.25, 'last_close': 16.3, 'cur_vol': 11, 'change': 0.0, 'bid1': 16.28, 'ask1': 16.29, 'bid_vol1': 42, 'ask_vol1': 16, 'bid2': 16.27, 'ask2': 16.3, 'bid_vol2': 105, 'ask_vol2': 25, 'bid3': 16.26, 'ask3': 16.31, 'bid_vol3': 377, 'ask_vol3': 94, 'bid4': 16.25, 'ask4': 16.32, 'bid_vol4': 858, 'ask_vol4': 15, 'bid5': 16.240000000000002, 'ask5': 16.330000000000002, 'bid_vol5': 119, 'ask_vol5': 38}
2025-08-25 13:56:19 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2736 条数据
2025-08-25 13:56:19 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1326/1326 条Tick数据
2025-08-25 13:56:19 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:19 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:19 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:19 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1326 条记录，耗时: 0.41秒
2025-08-25 13:56:19 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:18, 股票数: 4394
2025-08-25 13:56:19 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1185.20 毫秒，每秒处理股票数: 3707.38
2025-08-25 13:56:19 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.19s (网络: 1.19s, 处理: -0.00s, 数据库: 1.19s)
2025-08-25 13:56:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2081
2025-08-25 13:56:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2081 条tick数据
2025-08-25 13:56:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:55:57'), 'stock_code': '600754', 'price': 23.48, 'volume': 120683, 'amount': 283045120.0, 'open': 23.42, 'high': 23.66, 'low': 23.29, 'last_close': 23.41, 'cur_vol': 5, 'change': 0.0, 'bid1': 23.48, 'ask1': 23.490000000000002, 'bid_vol1': 309, 'ask_vol1': 117, 'bid2': 23.47, 'ask2': 23.5, 'bid_vol2': 29, 'ask_vol2': 105, 'bid3': 23.46, 'ask3': 23.51, 'bid_vol3': 43, 'ask_vol3': 183, 'bid4': 23.45, 'ask4': 23.52, 'bid_vol4': 70, 'ask_vol4': 152, 'bid5': 23.44, 'ask5': 23.53, 'bid_vol5': 40, 'ask_vol5': 58}
2025-08-25 13:56:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:06'), 'stock_code': '600755', 'price': 6.42, 'volume': 305406, 'amount': 195802368.0, 'open': 6.36, 'high': 6.45, 'low': 6.34, 'last_close': 6.3500000000000005, 'cur_vol': 1, 'change': 0.009999999999999787, 'bid1': 6.41, 'ask1': 6.42, 'bid_vol1': 2117, 'ask_vol1': 2835, 'bid2': 6.4, 'ask2': 6.43, 'bid_vol2': 3181, 'ask_vol2': 4033, 'bid3': 6.390000000000001, 'ask3': 6.44, 'bid_vol3': 3828, 'ask_vol3': 6973, 'bid4': 6.38, 'ask4': 6.45, 'bid_vol4': 5448, 'ask_vol4': 10039, 'bid5': 6.37, 'ask5': 6.46, 'bid_vol5': 2417, 'ask_vol5': 12248}
2025-08-25 13:56:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:05'), 'stock_code': '600757', 'price': 9.43, 'volume': 96779, 'amount': 91380616.0, 'open': 9.450000000000001, 'high': 9.48, 'low': 9.4, 'last_close': 9.43, 'cur_vol': 1, 'change': 0.0, 'bid1': 9.42, 'ask1': 9.43, 'bid_vol1': 387, 'ask_vol1': 208, 'bid2': 9.41, 'ask2': 9.44, 'bid_vol2': 833, 'ask_vol2': 153, 'bid3': 9.4, 'ask3': 9.450000000000001, 'bid_vol3': 2328, 'ask_vol3': 293, 'bid4': 9.39, 'ask4': 9.46, 'bid_vol4': 658, 'ask_vol4': 815, 'bid5': 9.38, 'ask5': 9.47, 'bid_vol5': 1043, 'ask_vol5': 1539}
2025-08-25 13:56:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2081, 有效=2081, 无效=0
2025-08-25 13:56:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2081 条数据到stock_tick_data表
2025-08-25 13:56:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:55:57'), 'stock_code': '600754', 'price': 23.48, 'volume': 120683, 'amount': 283045120.0, 'open': 23.42, 'high': 23.66, 'low': 23.29, 'last_close': 23.41, 'cur_vol': 5, 'change': 0.0, 'bid1': 23.48, 'ask1': 23.490000000000002, 'bid_vol1': 309, 'ask_vol1': 117, 'bid2': 23.47, 'ask2': 23.5, 'bid_vol2': 29, 'ask_vol2': 105, 'bid3': 23.46, 'ask3': 23.51, 'bid_vol3': 43, 'ask_vol3': 183, 'bid4': 23.45, 'ask4': 23.52, 'bid_vol4': 70, 'ask_vol4': 152, 'bid5': 23.44, 'ask5': 23.53, 'bid_vol5': 40, 'ask_vol5': 58}
2025-08-25 13:56:21 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2081 条数据
2025-08-25 13:56:21 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2081/2081 条Tick数据
2025-08-25 13:56:21 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:21 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:21 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:21 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2081 条记录，耗时: 0.34秒
2025-08-25 13:56:21 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:20, 股票数: 4394
2025-08-25 13:56:21 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1114.96 毫秒，每秒处理股票数: 3940.95
2025-08-25 13:56:21 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.12s (网络: 1.12s, 处理: -0.00s, 数据库: 1.12s)
2025-08-25 13:56:22 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:22 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1422
2025-08-25 13:56:22 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1422/1422 条Tick数据
2025-08-25 13:56:22 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:22 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:22 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:22 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1422 条记录，耗时: 0.11秒
2025-08-25 13:56:22 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:22, 股票数: 4394
2025-08-25 13:56:22 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 821.17 毫秒，每秒处理股票数: 5350.90
2025-08-25 13:56:22 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.82s (网络: 0.82s, 处理: -0.00s, 数据库: 0.82s)
2025-08-25 13:56:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1303
2025-08-25 13:56:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2725 条tick数据
2025-08-25 13:56:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:08'), 'stock_code': '002455', 'price': 7.13, 'volume': 345346, 'amount': 246065216.0, 'open': 7.1000000000000005, 'high': 7.32, 'low': 7.03, 'last_close': 7.04, 'cur_vol': 159, 'change': 0.0, 'bid1': 7.13, 'ask1': 7.140000000000001, 'bid_vol1': 268, 'ask_vol1': 4075, 'bid2': 7.12, 'ask2': 7.15, 'bid_vol2': 5455, 'ask_vol2': 539, 'bid3': 7.11, 'ask3': 7.16, 'bid_vol3': 2421, 'ask_vol3': 306, 'bid4': 7.1000000000000005, 'ask4': 7.17, 'bid_vol4': 2022, 'ask_vol4': 420, 'bid5': 7.09, 'ask5': 7.18, 'bid_vol5': 1094, 'ask_vol5': 1082}
2025-08-25 13:56:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:04'), 'stock_code': '002456', 'price': 13.3, 'volume': 2992558, 'amount': 3994962688.0, 'open': 13.200000000000001, 'high': 13.700000000000001, 'low': 13.09, 'last_close': 13.200000000000001, 'cur_vol': 127, 'change': 0.0, 'bid1': 13.290000000000001, 'ask1': 13.3, 'bid_vol1': 641, 'ask_vol1': 3346, 'bid2': 13.280000000000001, 'ask2': 13.31, 'bid_vol2': 1916, 'ask_vol2': 918, 'bid3': 13.27, 'ask3': 13.32, 'bid_vol3': 1782, 'ask_vol3': 1060, 'bid4': 13.26, 'ask4': 13.33, 'bid_vol4': 1406, 'ask_vol4': 698, 'bid5': 13.25, 'ask5': 13.34, 'bid_vol5': 1710, 'ask_vol5': 288}
2025-08-25 13:56:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:07'), 'stock_code': '002458', 'price': 9.03, 'volume': 108239, 'amount': 97674672.0, 'open': 9.03, 'high': 9.07, 'low': 8.96, 'last_close': 9.03, 'cur_vol': 9, 'change': 0.0, 'bid1': 9.02, 'ask1': 9.03, 'bid_vol1': 320, 'ask_vol1': 8, 'bid2': 9.01, 'ask2': 9.040000000000001, 'bid_vol2': 566, 'ask_vol2': 366, 'bid3': 9.0, 'ask3': 9.05, 'bid_vol3': 1553, 'ask_vol3': 1608, 'bid4': 8.99, 'ask4': 9.06, 'bid_vol4': 1191, 'ask_vol4': 2774, 'bid5': 8.98, 'ask5': 9.07, 'bid_vol5': 1184, 'ask_vol5': 1744}
2025-08-25 13:56:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2725, 有效=2725, 无效=0
2025-08-25 13:56:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2725 条数据到stock_tick_data表
2025-08-25 13:56:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:08'), 'stock_code': '002455', 'price': 7.13, 'volume': 345346, 'amount': 246065216.0, 'open': 7.1000000000000005, 'high': 7.32, 'low': 7.03, 'last_close': 7.04, 'cur_vol': 159, 'change': 0.0, 'bid1': 7.13, 'ask1': 7.140000000000001, 'bid_vol1': 268, 'ask_vol1': 4075, 'bid2': 7.12, 'ask2': 7.15, 'bid_vol2': 5455, 'ask_vol2': 539, 'bid3': 7.11, 'ask3': 7.16, 'bid_vol3': 2421, 'ask_vol3': 306, 'bid4': 7.1000000000000005, 'ask4': 7.17, 'bid_vol4': 2022, 'ask_vol4': 420, 'bid5': 7.09, 'ask5': 7.18, 'bid_vol5': 1094, 'ask_vol5': 1082}
2025-08-25 13:56:25 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2725 条数据
2025-08-25 13:56:25 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1303/1303 条Tick数据
2025-08-25 13:56:25 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:25 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:25 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:25 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1303 条记录，耗时: 0.41秒
2025-08-25 13:56:25 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:24, 股票数: 4394
2025-08-25 13:56:25 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1196.64 毫秒，每秒处理股票数: 3671.96
2025-08-25 13:56:25 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.20s (网络: 1.20s, 处理: 0.00s, 数据库: 1.20s)
2025-08-25 13:56:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2065
2025-08-25 13:56:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2065 条tick数据
2025-08-25 13:56:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:04'), 'stock_code': '000860', 'price': 16.91, 'volume': 322205, 'amount': 538924928.0, 'open': 16.42, 'high': 17.11, 'low': 16.330000000000002, 'last_close': 16.39, 'cur_vol': 2, 'change': 0.0, 'bid1': 16.91, 'ask1': 16.92, 'bid_vol1': 116, 'ask_vol1': 465, 'bid2': 16.9, 'ask2': 16.93, 'bid_vol2': 176, 'ask_vol2': 360, 'bid3': 16.89, 'ask3': 16.94, 'bid_vol3': 246, 'ask_vol3': 320, 'bid4': 16.88, 'ask4': 16.95, 'bid_vol4': 939, 'ask_vol4': 251, 'bid5': 16.87, 'ask5': 16.96, 'bid_vol5': 145, 'ask_vol5': 276}
2025-08-25 13:56:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:12'), 'stock_code': '000863', 'price': 3.62, 'volume': 468668, 'amount': 169127536.0, 'open': 3.5500000000000003, 'high': 3.65, 'low': 3.5500000000000003, 'last_close': 3.5500000000000003, 'cur_vol': 1, 'change': 0.0, 'bid1': 3.61, 'ask1': 3.62, 'bid_vol1': 3863, 'ask_vol1': 7479, 'bid2': 3.6, 'ask2': 3.63, 'bid_vol2': 13013, 'ask_vol2': 6953, 'bid3': 3.59, 'ask3': 3.64, 'bid_vol3': 6035, 'ask_vol3': 7919, 'bid4': 3.58, 'ask4': 3.65, 'bid_vol4': 7146, 'ask_vol4': 11537, 'bid5': 3.5700000000000003, 'ask5': 3.66, 'bid_vol5': 3237, 'ask_vol5': 8513}
2025-08-25 13:56:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:10'), 'stock_code': '000868', 'price': 5.93, 'volume': 161197, 'amount': 95666656.0, 'open': 5.96, 'high': 5.99, 'low': 5.9, 'last_close': 5.96, 'cur_vol': 15, 'change': 0.009999999999999787, 'bid1': 5.92, 'ask1': 5.93, 'bid_vol1': 253, 'ask_vol1': 736, 'bid2': 5.91, 'ask2': 5.94, 'bid_vol2': 3679, 'ask_vol2': 661, 'bid3': 5.9, 'ask3': 5.95, 'bid_vol3': 5485, 'ask_vol3': 1390, 'bid4': 5.89, 'ask4': 5.96, 'bid_vol4': 1478, 'ask_vol4': 1973, 'bid5': 5.88, 'ask5': 5.97, 'bid_vol5': 2253, 'ask_vol5': 2471}
2025-08-25 13:56:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2065, 有效=2065, 无效=0
2025-08-25 13:56:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2065 条数据到stock_tick_data表
2025-08-25 13:56:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:04'), 'stock_code': '000860', 'price': 16.91, 'volume': 322205, 'amount': 538924928.0, 'open': 16.42, 'high': 17.11, 'low': 16.330000000000002, 'last_close': 16.39, 'cur_vol': 2, 'change': 0.0, 'bid1': 16.91, 'ask1': 16.92, 'bid_vol1': 116, 'ask_vol1': 465, 'bid2': 16.9, 'ask2': 16.93, 'bid_vol2': 176, 'ask_vol2': 360, 'bid3': 16.89, 'ask3': 16.94, 'bid_vol3': 246, 'ask_vol3': 320, 'bid4': 16.88, 'ask4': 16.95, 'bid_vol4': 939, 'ask_vol4': 251, 'bid5': 16.87, 'ask5': 16.96, 'bid_vol5': 145, 'ask_vol5': 276}
2025-08-25 13:56:27 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2065 条数据
2025-08-25 13:56:27 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2065/2065 条Tick数据
2025-08-25 13:56:27 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:27 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:27 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:27 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2065 条记录，耗时: 0.33秒
2025-08-25 13:56:27 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:26, 股票数: 4394
2025-08-25 13:56:27 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1150.96 毫秒，每秒处理股票数: 3817.67
2025-08-25 13:56:27 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.15s (网络: 1.15s, 处理: -0.00s, 数据库: 1.15s)
2025-08-25 13:56:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1450
2025-08-25 13:56:28 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1450/1450 条Tick数据
2025-08-25 13:56:28 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:28 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:28 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:28 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1450 条记录，耗时: 0.09秒
2025-08-25 13:56:28 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:28, 股票数: 4394
2025-08-25 13:56:28 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 863.40 毫秒，每秒处理股票数: 5089.18
2025-08-25 13:56:28 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.86s (网络: 0.86s, 处理: 0.00s, 数据库: 0.86s)
2025-08-25 13:56:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1128
2025-08-25 13:56:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2578 条tick数据
2025-08-25 13:56:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:11'), 'stock_code': '002400', 'price': 8.35, 'volume': 1147803, 'amount': 968387328.0, 'open': 8.35, 'high': 8.59, 'low': 8.3, 'last_close': 8.34, 'cur_vol': 5, 'change': -0.009999999999999787, 'bid1': 8.35, 'ask1': 8.36, 'bid_vol1': 2280, 'ask_vol1': 2175, 'bid2': 8.34, 'ask2': 8.370000000000001, 'bid_vol2': 2559, 'ask_vol2': 2050, 'bid3': 8.33, 'ask3': 8.38, 'bid_vol3': 5473, 'ask_vol3': 3423, 'bid4': 8.32, 'ask4': 8.39, 'bid_vol4': 3869, 'ask_vol4': 4064, 'bid5': 8.31, 'ask5': 8.4, 'bid_vol5': 5752, 'ask_vol5': 3347}
2025-08-25 13:56:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:04'), 'stock_code': '002401', 'price': 19.62, 'volume': 157925, 'amount': 311179968.0, 'open': 19.67, 'high': 19.85, 'low': 19.580000000000002, 'last_close': 19.68, 'cur_vol': 8, 'change': 0.0, 'bid1': 19.62, 'ask1': 19.63, 'bid_vol1': 26, 'ask_vol1': 42, 'bid2': 19.61, 'ask2': 19.64, 'bid_vol2': 438, 'ask_vol2': 112, 'bid3': 19.6, 'ask3': 19.650000000000002, 'bid_vol3': 687, 'ask_vol3': 122, 'bid4': 19.59, 'ask4': 19.66, 'bid_vol4': 387, 'ask_vol4': 43, 'bid5': 19.580000000000002, 'ask5': 19.67, 'bid_vol5': 984, 'ask_vol5': 22}
2025-08-25 13:56:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:55:52'), 'stock_code': '002402', 'price': 39.75, 'volume': 704464, 'amount': 2783848192.0, 'open': 39.03, 'high': 39.75, 'low': 38.5, 'last_close': 36.14, 'cur_vol': 13, 'change': 0.0, 'bid1': 39.75, 'ask1': 0.0, 'bid_vol1': 108011, 'ask_vol1': 0, 'bid2': 39.74, 'ask2': 0.0, 'bid_vol2': 2617, 'ask_vol2': 0, 'bid3': 39.730000000000004, 'ask3': 0.0, 'bid_vol3': 641, 'ask_vol3': 0, 'bid4': 39.72, 'ask4': 0.0, 'bid_vol4': 380, 'ask_vol4': 0, 'bid5': 39.71, 'ask5': 0.0, 'bid_vol5': 288, 'ask_vol5': 0}
2025-08-25 13:56:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2578, 有效=2578, 无效=0
2025-08-25 13:56:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2578 条数据到stock_tick_data表
2025-08-25 13:56:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:11'), 'stock_code': '002400', 'price': 8.35, 'volume': 1147803, 'amount': 968387328.0, 'open': 8.35, 'high': 8.59, 'low': 8.3, 'last_close': 8.34, 'cur_vol': 5, 'change': -0.009999999999999787, 'bid1': 8.35, 'ask1': 8.36, 'bid_vol1': 2280, 'ask_vol1': 2175, 'bid2': 8.34, 'ask2': 8.370000000000001, 'bid_vol2': 2559, 'ask_vol2': 2050, 'bid3': 8.33, 'ask3': 8.38, 'bid_vol3': 5473, 'ask_vol3': 3423, 'bid4': 8.32, 'ask4': 8.39, 'bid_vol4': 3869, 'ask_vol4': 4064, 'bid5': 8.31, 'ask5': 8.4, 'bid_vol5': 5752, 'ask_vol5': 3347}
2025-08-25 13:56:31 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2578 条数据
2025-08-25 13:56:31 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1128/1128 条Tick数据
2025-08-25 13:56:31 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:31 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:31 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:31 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1128 条记录，耗时: 0.37秒
2025-08-25 13:56:31 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:30, 股票数: 4394
2025-08-25 13:56:31 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1147.93 毫秒，每秒处理股票数: 3827.76
2025-08-25 13:56:31 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.15s (网络: 1.15s, 处理: 0.00s, 数据库: 1.15s)
2025-08-25 13:56:32 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:32 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2009
2025-08-25 13:56:32 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2009/2009 条Tick数据
2025-08-25 13:56:32 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:32 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:32 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:32 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2009 条记录，耗时: 0.10秒
2025-08-25 13:56:32 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:32, 股票数: 4394
2025-08-25 13:56:32 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 855.79 毫秒，每秒处理股票数: 5134.43
2025-08-25 13:56:32 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.86s (网络: 0.86s, 处理: 0.00s, 数据库: 0.86s)
2025-08-25 13:56:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1400
2025-08-25 13:56:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3409 条tick数据
2025-08-25 13:56:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:02'), 'stock_code': '300837', 'price': 25.35, 'volume': 8166, 'amount': 20843066.0, 'open': 25.560000000000002, 'high': 25.76, 'low': 25.25, 'last_close': 25.55, 'cur_vol': 8, 'change': 0.010000000000001563, 'bid1': 25.35, 'ask1': 25.36, 'bid_vol1': 1, 'ask_vol1': 6, 'bid2': 25.34, 'ask2': 25.37, 'bid_vol2': 1, 'ask_vol2': 2, 'bid3': 25.330000000000002, 'ask3': 25.38, 'bid_vol3': 14, 'ask_vol3': 10, 'bid4': 25.32, 'ask4': 25.400000000000002, 'bid_vol4': 18, 'ask_vol4': 35, 'bid5': 25.310000000000002, 'ask5': 25.41, 'bid_vol5': 7, 'ask_vol5': 33}
2025-08-25 13:56:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:03'), 'stock_code': '300840', 'price': 24.04, 'volume': 216711, 'amount': 521914976.0, 'open': 23.68, 'high': 24.55, 'low': 23.490000000000002, 'last_close': 23.68, 'cur_vol': 2, 'change': 0.0, 'bid1': 24.04, 'ask1': 24.060000000000002, 'bid_vol1': 50, 'ask_vol1': 1, 'bid2': 24.03, 'ask2': 24.07, 'bid_vol2': 146, 'ask_vol2': 32, 'bid3': 24.02, 'ask3': 24.080000000000002, 'bid_vol3': 19, 'ask_vol3': 19, 'bid4': 24.01, 'ask4': 24.09, 'bid_vol4': 45, 'ask_vol4': 206, 'bid5': 24.0, 'ask5': 24.1, 'bid_vol5': 152, 'ask_vol5': 302}
2025-08-25 13:56:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:55:27'), 'stock_code': '300841', 'price': 84.55, 'volume': 37635, 'amount': 320841120.0, 'open': 84.77, 'high': 86.49, 'low': 84.0, 'last_close': 84.77, 'cur_vol': 1, 'change': 0.0, 'bid1': 84.52, 'ask1': 84.55, 'bid_vol1': 5, 'ask_vol1': 11, 'bid2': 84.51, 'ask2': 84.56, 'bid_vol2': 20, 'ask_vol2': 35, 'bid3': 84.5, 'ask3': 84.60000000000001, 'bid_vol3': 10, 'ask_vol3': 80, 'bid4': 84.49, 'ask4': 84.64, 'bid_vol4': 2, 'ask_vol4': 12, 'bid5': 84.48, 'ask5': 84.65, 'bid_vol5': 10, 'ask_vol5': 75}
2025-08-25 13:56:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3409, 有效=3409, 无效=0
2025-08-25 13:56:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3409 条数据到stock_tick_data表
2025-08-25 13:56:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:02'), 'stock_code': '300837', 'price': 25.35, 'volume': 8166, 'amount': 20843066.0, 'open': 25.560000000000002, 'high': 25.76, 'low': 25.25, 'last_close': 25.55, 'cur_vol': 8, 'change': 0.010000000000001563, 'bid1': 25.35, 'ask1': 25.36, 'bid_vol1': 1, 'ask_vol1': 6, 'bid2': 25.34, 'ask2': 25.37, 'bid_vol2': 1, 'ask_vol2': 2, 'bid3': 25.330000000000002, 'ask3': 25.38, 'bid_vol3': 14, 'ask_vol3': 10, 'bid4': 25.32, 'ask4': 25.400000000000002, 'bid_vol4': 18, 'ask_vol4': 35, 'bid5': 25.310000000000002, 'ask5': 25.41, 'bid_vol5': 7, 'ask_vol5': 33}
2025-08-25 13:56:35 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3409 条数据
2025-08-25 13:56:35 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1400/1400 条Tick数据
2025-08-25 13:56:35 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:35 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:35 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:35 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1400 条记录，耗时: 0.47秒
2025-08-25 13:56:35 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:34, 股票数: 4394
2025-08-25 13:56:35 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1232.43 毫秒，每秒处理股票数: 3565.30
2025-08-25 13:56:35 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.23s (网络: 1.23s, 处理: 0.00s, 数据库: 1.23s)
2025-08-25 13:56:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1278
2025-08-25 13:56:36 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1278/1278 条Tick数据
2025-08-25 13:56:36 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:36 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:36 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:36 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1278 条记录，耗时: 0.09秒
2025-08-25 13:56:36 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:36, 股票数: 4394
2025-08-25 13:56:36 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 846.79 毫秒，每秒处理股票数: 5188.99
2025-08-25 13:56:36 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.85s (网络: 0.85s, 处理: -0.00s, 数据库: 0.85s)
2025-08-25 13:56:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1951
2025-08-25 13:56:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3229 条tick数据
2025-08-25 13:56:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:55:17'), 'stock_code': '302132', 'price': 104.3, 'volume': 375199, 'amount': 3879840256.0, 'open': 99.5, 'high': 108.93, 'low': 98.61, 'last_close': 99.36, 'cur_vol': 9, 'change': -0.29000000000000625, 'bid1': 104.3, 'ask1': 104.32000000000001, 'bid_vol1': 9, 'ask_vol1': 1, 'bid2': 104.2, 'ask2': 104.59, 'bid_vol2': 2, 'ask_vol2': 17, 'bid3': 104.18, 'ask3': 104.60000000000001, 'bid_vol3': 2, 'ask_vol3': 249, 'bid4': 104.15, 'ask4': 104.65, 'bid_vol4': 3, 'ask_vol4': 2, 'bid5': 104.11, 'ask5': 104.66, 'bid_vol5': 19, 'ask_vol5': 1}
2025-08-25 13:56:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:12'), 'stock_code': '600000', 'price': 13.91, 'volume': 429801, 'amount': 595557760.0, 'open': 13.9, 'high': 13.96, 'low': 13.76, 'last_close': 13.94, 'cur_vol': 4, 'change': 0.0, 'bid1': 13.9, 'ask1': 13.91, 'bid_vol1': 6243, 'ask_vol1': 151, 'bid2': 13.89, 'ask2': 13.92, 'bid_vol2': 322, 'ask_vol2': 990, 'bid3': 13.88, 'ask3': 13.93, 'bid_vol3': 3835, 'ask_vol3': 2397, 'bid4': 13.870000000000001, 'ask4': 13.94, 'bid_vol4': 23, 'ask_vol4': 1246, 'bid5': 13.86, 'ask5': 13.950000000000001, 'bid_vol5': 376, 'ask_vol5': 3765}
2025-08-25 13:56:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:15'), 'stock_code': '600004', 'price': 9.85, 'volume': 274812, 'amount': 271851552.0, 'open': 9.99, 'high': 9.99, 'low': 9.82, 'last_close': 9.93, 'cur_vol': 26, 'change': -0.009999999999999787, 'bid1': 9.85, 'ask1': 9.86, 'bid_vol1': 1280, 'ask_vol1': 361, 'bid2': 9.84, 'ask2': 9.870000000000001, 'bid_vol2': 999, 'ask_vol2': 1042, 'bid3': 9.83, 'ask3': 9.88, 'bid_vol3': 1089, 'ask_vol3': 1346, 'bid4': 9.82, 'ask4': 9.89, 'bid_vol4': 3292, 'ask_vol4': 2201, 'bid5': 9.81, 'ask5': 9.9, 'bid_vol5': 3346, 'ask_vol5': 3198}
2025-08-25 13:56:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3229, 有效=3229, 无效=0
2025-08-25 13:56:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3229 条数据到stock_tick_data表
2025-08-25 13:56:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:55:17'), 'stock_code': '302132', 'price': 104.3, 'volume': 375199, 'amount': 3879840256.0, 'open': 99.5, 'high': 108.93, 'low': 98.61, 'last_close': 99.36, 'cur_vol': 9, 'change': -0.29000000000000625, 'bid1': 104.3, 'ask1': 104.32000000000001, 'bid_vol1': 9, 'ask_vol1': 1, 'bid2': 104.2, 'ask2': 104.59, 'bid_vol2': 2, 'ask_vol2': 17, 'bid3': 104.18, 'ask3': 104.60000000000001, 'bid_vol3': 2, 'ask_vol3': 249, 'bid4': 104.15, 'ask4': 104.65, 'bid_vol4': 3, 'ask_vol4': 2, 'bid5': 104.11, 'ask5': 104.66, 'bid_vol5': 19, 'ask_vol5': 1}
2025-08-25 13:56:39 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3229 条数据
2025-08-25 13:56:39 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1951/1951 条Tick数据
2025-08-25 13:56:39 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:39 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:39 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:39 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1951 条记录，耗时: 0.48秒
2025-08-25 13:56:39 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:38, 股票数: 4394
2025-08-25 13:56:39 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1253.20 毫秒，每秒处理股票数: 3506.24
2025-08-25 13:56:39 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.25s (网络: 1.25s, 处理: -0.00s, 数据库: 1.25s)
2025-08-25 13:56:40 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:40 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1390
2025-08-25 13:56:40 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1390/1390 条Tick数据
2025-08-25 13:56:40 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:40 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:40 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:40 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1390 条记录，耗时: 0.09秒
2025-08-25 13:56:40 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:40, 股票数: 4394
2025-08-25 13:56:40 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 863.61 毫秒，每秒处理股票数: 5087.97
2025-08-25 13:56:40 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.86s (网络: 0.86s, 处理: -0.00s, 数据库: 0.86s)
2025-08-25 13:56:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1189
2025-08-25 13:56:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2579 条tick数据
2025-08-25 13:56:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:08'), 'stock_code': '600967', 'price': 25.82, 'volume': 858970, 'amount': 2209919744.0, 'open': 25.62, 'high': 26.14, 'low': 25.0, 'last_close': 25.62, 'cur_vol': 93, 'change': -0.010000000000001563, 'bid1': 25.810000000000002, 'ask1': 25.82, 'bid_vol1': 313, 'ask_vol1': 38, 'bid2': 25.8, 'ask2': 25.830000000000002, 'bid_vol2': 1204, 'ask_vol2': 46, 'bid3': 25.79, 'ask3': 25.84, 'bid_vol3': 236, 'ask_vol3': 210, 'bid4': 25.78, 'ask4': 25.85, 'bid_vol4': 674, 'ask_vol4': 314, 'bid5': 25.77, 'ask5': 25.86, 'bid_vol5': 140, 'ask_vol5': 104}
2025-08-25 13:56:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:18'), 'stock_code': '600970', 'price': 9.25, 'volume': 268831, 'amount': 248525728.0, 'open': 9.22, 'high': 9.33, 'low': 9.19, 'last_close': 9.200000000000001, 'cur_vol': 30, 'change': 0.009999999999999787, 'bid1': 9.24, 'ask1': 9.25, 'bid_vol1': 1352, 'ask_vol1': 148, 'bid2': 9.23, 'ask2': 9.26, 'bid_vol2': 2123, 'ask_vol2': 506, 'bid3': 9.22, 'ask3': 9.27, 'bid_vol3': 2475, 'ask_vol3': 895, 'bid4': 9.21, 'ask4': 9.28, 'bid_vol4': 2636, 'ask_vol4': 1753, 'bid5': 9.200000000000001, 'ask5': 9.290000000000001, 'bid_vol5': 4867, 'ask_vol5': 1163}
2025-08-25 13:56:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:20'), 'stock_code': '600973', 'price': 5.7, 'volume': 247262, 'amount': 141154272.0, 'open': 5.75, 'high': 5.76, 'low': 5.65, 'last_close': 5.72, 'cur_vol': 48, 'change': 0.0, 'bid1': 5.69, 'ask1': 5.7, 'bid_vol1': 1182, 'ask_vol1': 795, 'bid2': 5.68, 'ask2': 5.71, 'bid_vol2': 824, 'ask_vol2': 1437, 'bid3': 5.67, 'ask3': 5.72, 'bid_vol3': 1695, 'ask_vol3': 3731, 'bid4': 5.66, 'ask4': 5.73, 'bid_vol4': 3373, 'ask_vol4': 4191, 'bid5': 5.65, 'ask5': 5.74, 'bid_vol5': 4021, 'ask_vol5': 3292}
2025-08-25 13:56:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2579, 有效=2579, 无效=0
2025-08-25 13:56:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2579 条数据到stock_tick_data表
2025-08-25 13:56:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:08'), 'stock_code': '600967', 'price': 25.82, 'volume': 858970, 'amount': 2209919744.0, 'open': 25.62, 'high': 26.14, 'low': 25.0, 'last_close': 25.62, 'cur_vol': 93, 'change': -0.010000000000001563, 'bid1': 25.810000000000002, 'ask1': 25.82, 'bid_vol1': 313, 'ask_vol1': 38, 'bid2': 25.8, 'ask2': 25.830000000000002, 'bid_vol2': 1204, 'ask_vol2': 46, 'bid3': 25.79, 'ask3': 25.84, 'bid_vol3': 236, 'ask_vol3': 210, 'bid4': 25.78, 'ask4': 25.85, 'bid_vol4': 674, 'ask_vol4': 314, 'bid5': 25.77, 'ask5': 25.86, 'bid_vol5': 140, 'ask_vol5': 104}
2025-08-25 13:56:43 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2579 条数据
2025-08-25 13:56:43 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1189/1189 条Tick数据
2025-08-25 13:56:43 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:43 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:43 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:43 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1189 条记录，耗时: 0.40秒
2025-08-25 13:56:43 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:42, 股票数: 4394
2025-08-25 13:56:43 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1164.20 毫秒，每秒处理股票数: 3774.26
2025-08-25 13:56:43 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.16s (网络: 1.16s, 处理: -0.00s, 数据库: 1.16s)
2025-08-25 13:56:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2063
2025-08-25 13:56:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2063 条tick数据
2025-08-25 13:56:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:14'), 'stock_code': '300109', 'price': 18.34, 'volume': 163719, 'amount': 302765856.0, 'open': 18.38, 'high': 18.8, 'low': 18.21, 'last_close': 18.42, 'cur_vol': 92, 'change': 0.00999999999999801, 'bid1': 18.34, 'ask1': 18.35, 'bid_vol1': 7, 'ask_vol1': 89, 'bid2': 18.330000000000002, 'ask2': 18.36, 'bid_vol2': 45, 'ask_vol2': 366, 'bid3': 18.32, 'ask3': 18.37, 'bid_vol3': 69, 'ask_vol3': 110, 'bid4': 18.31, 'ask4': 18.38, 'bid_vol4': 143, 'ask_vol4': 61, 'bid5': 18.3, 'ask5': 18.39, 'bid_vol5': 474, 'ask_vol5': 10}
2025-08-25 13:56:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:23'), 'stock_code': '300110', 'price': 3.5700000000000003, 'volume': 198660, 'amount': 71341152.0, 'open': 3.56, 'high': 3.63, 'low': 3.54, 'last_close': 3.5700000000000003, 'cur_vol': 1, 'change': -0.009999999999999787, 'bid1': 3.5700000000000003, 'ask1': 3.58, 'bid_vol1': 4714, 'ask_vol1': 3163, 'bid2': 3.56, 'ask2': 3.59, 'bid_vol2': 7764, 'ask_vol2': 1863, 'bid3': 3.5500000000000003, 'ask3': 3.6, 'bid_vol3': 3547, 'ask_vol3': 3032, 'bid4': 3.54, 'ask4': 3.61, 'bid_vol4': 3029, 'ask_vol4': 4506, 'bid5': 3.5300000000000002, 'ask5': 3.62, 'bid_vol5': 2518, 'ask_vol5': 6368}
2025-08-25 13:56:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:08'), 'stock_code': '300113', 'price': 27.900000000000002, 'volume': 853949, 'amount': 2332709120.0, 'open': 27.87, 'high': 28.12, 'low': 26.740000000000002, 'last_close': 27.240000000000002, 'cur_vol': 271, 'change': 0.020000000000003126, 'bid1': 27.86, 'ask1': 27.89, 'bid_vol1': 22, 'ask_vol1': 9, 'bid2': 27.85, 'ask2': 27.900000000000002, 'bid_vol2': 15, 'ask_vol2': 17, 'bid3': 27.82, 'ask3': 27.91, 'bid_vol3': 3, 'ask_vol3': 3, 'bid4': 27.810000000000002, 'ask4': 27.92, 'bid_vol4': 19, 'ask_vol4': 23, 'bid5': 27.8, 'ask5': 27.93, 'bid_vol5': 102, 'ask_vol5': 174}
2025-08-25 13:56:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2063, 有效=2063, 无效=0
2025-08-25 13:56:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2063 条数据到stock_tick_data表
2025-08-25 13:56:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:14'), 'stock_code': '300109', 'price': 18.34, 'volume': 163719, 'amount': 302765856.0, 'open': 18.38, 'high': 18.8, 'low': 18.21, 'last_close': 18.42, 'cur_vol': 92, 'change': 0.00999999999999801, 'bid1': 18.34, 'ask1': 18.35, 'bid_vol1': 7, 'ask_vol1': 89, 'bid2': 18.330000000000002, 'ask2': 18.36, 'bid_vol2': 45, 'ask_vol2': 366, 'bid3': 18.32, 'ask3': 18.37, 'bid_vol3': 69, 'ask_vol3': 110, 'bid4': 18.31, 'ask4': 18.38, 'bid_vol4': 143, 'ask_vol4': 61, 'bid5': 18.3, 'ask5': 18.39, 'bid_vol5': 474, 'ask_vol5': 10}
2025-08-25 13:56:45 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2063 条数据
2025-08-25 13:56:45 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2063/2063 条Tick数据
2025-08-25 13:56:45 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:45 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:45 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:45 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2063 条记录，耗时: 0.36秒
2025-08-25 13:56:45 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:44, 股票数: 4394
2025-08-25 13:56:45 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1122.00 毫秒，每秒处理股票数: 3916.21
2025-08-25 13:56:45 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.12s (网络: 1.12s, 处理: 0.00s, 数据库: 1.12s)
2025-08-25 13:56:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1383
2025-08-25 13:56:46 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1383/1383 条Tick数据
2025-08-25 13:56:46 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:46 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:46 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:46 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1383 条记录，耗时: 0.09秒
2025-08-25 13:56:46 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:46, 股票数: 4394
2025-08-25 13:56:46 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 873.75 毫秒，每秒处理股票数: 5028.89
2025-08-25 13:56:46 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.87s (网络: 0.87s, 处理: -0.00s, 数据库: 0.87s)
2025-08-25 13:56:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1196
2025-08-25 13:56:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2579 条tick数据
2025-08-25 13:56:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:14'), 'stock_code': '603238', 'price': 20.330000000000002, 'volume': 28916, 'amount': 59002740.0, 'open': 20.19, 'high': 20.740000000000002, 'low': 20.19, 'last_close': 20.240000000000002, 'cur_vol': 21, 'change': 0.010000000000001563, 'bid1': 20.330000000000002, 'ask1': 20.34, 'bid_vol1': 4, 'ask_vol1': 306, 'bid2': 20.32, 'ask2': 20.35, 'bid_vol2': 2, 'ask_vol2': 343, 'bid3': 20.31, 'ask3': 20.36, 'bid_vol3': 74, 'ask_vol3': 33, 'bid4': 20.3, 'ask4': 20.37, 'bid_vol4': 75, 'ask_vol4': 2, 'bid5': 20.28, 'ask5': 20.39, 'bid_vol5': 19, 'ask_vol5': 13}
2025-08-25 13:56:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:55:55'), 'stock_code': '603257', 'price': 53.7, 'volume': 28941, 'amount': 156372816.0, 'open': 53.67, 'high': 54.46, 'low': 53.57, 'last_close': 53.660000000000004, 'cur_vol': 1, 'change': 0.0, 'bid1': 53.7, 'ask1': 53.71, 'bid_vol1': 38, 'ask_vol1': 15, 'bid2': 53.69, 'ask2': 53.72, 'bid_vol2': 1, 'ask_vol2': 185, 'bid3': 53.68, 'ask3': 53.730000000000004, 'bid_vol3': 5, 'ask_vol3': 8, 'bid4': 53.660000000000004, 'ask4': 53.76, 'bid_vol4': 46, 'ask_vol4': 28, 'bid5': 53.65, 'ask5': 53.77, 'bid_vol5': 36, 'ask_vol5': 8}
2025-08-25 13:56:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:13'), 'stock_code': '603258', 'price': 23.28, 'volume': 50381, 'amount': 116912632.0, 'open': 23.3, 'high': 23.43, 'low': 22.990000000000002, 'last_close': 23.29, 'cur_vol': 7, 'change': 0.0, 'bid1': 23.26, 'ask1': 23.27, 'bid_vol1': 5, 'ask_vol1': 1, 'bid2': 23.25, 'ask2': 23.28, 'bid_vol2': 108, 'ask_vol2': 20, 'bid3': 23.23, 'ask3': 23.29, 'bid_vol3': 33, 'ask_vol3': 24, 'bid4': 23.21, 'ask4': 23.3, 'bid_vol4': 16, 'ask_vol4': 194, 'bid5': 23.2, 'ask5': 23.31, 'bid_vol5': 58, 'ask_vol5': 1065}
2025-08-25 13:56:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2579, 有效=2579, 无效=0
2025-08-25 13:56:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2579 条数据到stock_tick_data表
2025-08-25 13:56:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:14'), 'stock_code': '603238', 'price': 20.330000000000002, 'volume': 28916, 'amount': 59002740.0, 'open': 20.19, 'high': 20.740000000000002, 'low': 20.19, 'last_close': 20.240000000000002, 'cur_vol': 21, 'change': 0.010000000000001563, 'bid1': 20.330000000000002, 'ask1': 20.34, 'bid_vol1': 4, 'ask_vol1': 306, 'bid2': 20.32, 'ask2': 20.35, 'bid_vol2': 2, 'ask_vol2': 343, 'bid3': 20.31, 'ask3': 20.36, 'bid_vol3': 74, 'ask_vol3': 33, 'bid4': 20.3, 'ask4': 20.37, 'bid_vol4': 75, 'ask_vol4': 2, 'bid5': 20.28, 'ask5': 20.39, 'bid_vol5': 19, 'ask_vol5': 13}
2025-08-25 13:56:49 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2579 条数据
2025-08-25 13:56:49 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1196/1196 条Tick数据
2025-08-25 13:56:49 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:49 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:49 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:49 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1196 条记录，耗时: 0.38秒
2025-08-25 13:56:49 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:48, 股票数: 4394
2025-08-25 13:56:49 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1144.21 毫秒，每秒处理股票数: 3840.19
2025-08-25 13:56:49 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.14s (网络: 1.14s, 处理: 0.00s, 数据库: 1.14s)
2025-08-25 13:56:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1990
2025-08-25 13:56:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1990 条tick数据
2025-08-25 13:56:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:23'), 'stock_code': '002454', 'price': 9.35, 'volume': 113328, 'amount': 106025184.0, 'open': 9.44, 'high': 9.450000000000001, 'low': 9.31, 'last_close': 9.42, 'cur_vol': 7, 'change': 0.0, 'bid1': 9.34, 'ask1': 9.35, 'bid_vol1': 282, 'ask_vol1': 80, 'bid2': 9.33, 'ask2': 9.36, 'bid_vol2': 200, 'ask_vol2': 229, 'bid3': 9.32, 'ask3': 9.370000000000001, 'bid_vol3': 787, 'ask_vol3': 357, 'bid4': 9.31, 'ask4': 9.38, 'bid_vol4': 2771, 'ask_vol4': 417, 'bid5': 9.3, 'ask5': 9.39, 'bid_vol5': 2043, 'ask_vol5': 203}
2025-08-25 13:56:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:24'), 'stock_code': '002455', 'price': 7.140000000000001, 'volume': 345959, 'amount': 246502736.0, 'open': 7.1000000000000005, 'high': 7.32, 'low': 7.03, 'last_close': 7.04, 'cur_vol': 22, 'change': 0.0, 'bid1': 7.13, 'ask1': 7.140000000000001, 'bid_vol1': 720, 'ask_vol1': 3757, 'bid2': 7.12, 'ask2': 7.15, 'bid_vol2': 5298, 'ask_vol2': 494, 'bid3': 7.11, 'ask3': 7.16, 'bid_vol3': 2427, 'ask_vol3': 319, 'bid4': 7.1000000000000005, 'ask4': 7.17, 'bid_vol4': 1987, 'ask_vol4': 420, 'bid5': 7.09, 'ask5': 7.18, 'bid_vol5': 1098, 'ask_vol5': 1102}
2025-08-25 13:56:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:20'), 'stock_code': '002456', 'price': 13.3, 'volume': 2993951, 'amount': 3996814848.0, 'open': 13.200000000000001, 'high': 13.700000000000001, 'low': 13.09, 'last_close': 13.200000000000001, 'cur_vol': 142, 'change': 0.009999999999999787, 'bid1': 13.290000000000001, 'ask1': 13.3, 'bid_vol1': 1081, 'ask_vol1': 3242, 'bid2': 13.280000000000001, 'ask2': 13.31, 'bid_vol2': 2020, 'ask_vol2': 921, 'bid3': 13.27, 'ask3': 13.32, 'bid_vol3': 1710, 'ask_vol3': 1344, 'bid4': 13.26, 'ask4': 13.33, 'bid_vol4': 1439, 'ask_vol4': 748, 'bid5': 13.25, 'ask5': 13.34, 'bid_vol5': 1834, 'ask_vol5': 288}
2025-08-25 13:56:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1990, 有效=1990, 无效=0
2025-08-25 13:56:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1990 条数据到stock_tick_data表
2025-08-25 13:56:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:23'), 'stock_code': '002454', 'price': 9.35, 'volume': 113328, 'amount': 106025184.0, 'open': 9.44, 'high': 9.450000000000001, 'low': 9.31, 'last_close': 9.42, 'cur_vol': 7, 'change': 0.0, 'bid1': 9.34, 'ask1': 9.35, 'bid_vol1': 282, 'ask_vol1': 80, 'bid2': 9.33, 'ask2': 9.36, 'bid_vol2': 200, 'ask_vol2': 229, 'bid3': 9.32, 'ask3': 9.370000000000001, 'bid_vol3': 787, 'ask_vol3': 357, 'bid4': 9.31, 'ask4': 9.38, 'bid_vol4': 2771, 'ask_vol4': 417, 'bid5': 9.3, 'ask5': 9.39, 'bid_vol5': 2043, 'ask_vol5': 203}
2025-08-25 13:56:51 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1990 条数据
2025-08-25 13:56:51 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1990/1990 条Tick数据
2025-08-25 13:56:51 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:51 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:51 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:51 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1990 条记录，耗时: 0.32秒
2025-08-25 13:56:51 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:50, 股票数: 4394
2025-08-25 13:56:51 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1088.43 毫秒，每秒处理股票数: 4037.02
2025-08-25 13:56:51 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.09s (网络: 1.09s, 处理: 0.00s, 数据库: 1.09s)
2025-08-25 13:56:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1370
2025-08-25 13:56:52 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1370/1370 条Tick数据
2025-08-25 13:56:52 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:52 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:52 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:52 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1370 条记录，耗时: 0.09秒
2025-08-25 13:56:52 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:52, 股票数: 4394
2025-08-25 13:56:52 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 853.57 毫秒，每秒处理股票数: 5147.78
2025-08-25 13:56:52 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.85s (网络: 0.85s, 处理: 0.00s, 数据库: 0.85s)
2025-08-25 13:56:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1302
2025-08-25 13:56:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2672 条tick数据
2025-08-25 13:56:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:26'), 'stock_code': '002455', 'price': 7.140000000000001, 'volume': 346052, 'amount': 246569072.0, 'open': 7.1000000000000005, 'high': 7.32, 'low': 7.03, 'last_close': 7.04, 'cur_vol': 93, 'change': 0.0, 'bid1': 7.13, 'ask1': 7.140000000000001, 'bid_vol1': 776, 'ask_vol1': 3735, 'bid2': 7.12, 'ask2': 7.15, 'bid_vol2': 5243, 'ask_vol2': 494, 'bid3': 7.11, 'ask3': 7.16, 'bid_vol3': 2413, 'ask_vol3': 319, 'bid4': 7.1000000000000005, 'ask4': 7.17, 'bid_vol4': 1987, 'ask_vol4': 420, 'bid5': 7.09, 'ask5': 7.18, 'bid_vol5': 1098, 'ask_vol5': 1102}
2025-08-25 13:56:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:22'), 'stock_code': '002456', 'price': 13.3, 'volume': 2993978, 'amount': 3996850688.0, 'open': 13.200000000000001, 'high': 13.700000000000001, 'low': 13.09, 'last_close': 13.200000000000001, 'cur_vol': 27, 'change': 0.0, 'bid1': 13.290000000000001, 'ask1': 13.3, 'bid_vol1': 1081, 'ask_vol1': 3313, 'bid2': 13.280000000000001, 'ask2': 13.31, 'bid_vol2': 2090, 'ask_vol2': 931, 'bid3': 13.27, 'ask3': 13.32, 'bid_vol3': 1703, 'ask_vol3': 1344, 'bid4': 13.26, 'ask4': 13.33, 'bid_vol4': 1439, 'ask_vol4': 748, 'bid5': 13.25, 'ask5': 13.34, 'bid_vol5': 1809, 'ask_vol5': 288}
2025-08-25 13:56:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:23'), 'stock_code': '002459', 'price': 12.56, 'volume': 824420, 'amount': 1030804800.0, 'open': 12.3, 'high': 12.870000000000001, 'low': 12.25, 'last_close': 12.27, 'cur_vol': 18, 'change': 0.0, 'bid1': 12.55, 'ask1': 12.56, 'bid_vol1': 342, 'ask_vol1': 238, 'bid2': 12.540000000000001, 'ask2': 12.57, 'bid_vol2': 350, 'ask_vol2': 171, 'bid3': 12.530000000000001, 'ask3': 12.58, 'bid_vol3': 7, 'ask_vol3': 243, 'bid4': 12.52, 'ask4': 12.59, 'bid_vol4': 26, 'ask_vol4': 211, 'bid5': 12.51, 'ask5': 12.6, 'bid_vol5': 99, 'ask_vol5': 720}
2025-08-25 13:56:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2672, 有效=2672, 无效=0
2025-08-25 13:56:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2672 条数据到stock_tick_data表
2025-08-25 13:56:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:26'), 'stock_code': '002455', 'price': 7.140000000000001, 'volume': 346052, 'amount': 246569072.0, 'open': 7.1000000000000005, 'high': 7.32, 'low': 7.03, 'last_close': 7.04, 'cur_vol': 93, 'change': 0.0, 'bid1': 7.13, 'ask1': 7.140000000000001, 'bid_vol1': 776, 'ask_vol1': 3735, 'bid2': 7.12, 'ask2': 7.15, 'bid_vol2': 5243, 'ask_vol2': 494, 'bid3': 7.11, 'ask3': 7.16, 'bid_vol3': 2413, 'ask_vol3': 319, 'bid4': 7.1000000000000005, 'ask4': 7.17, 'bid_vol4': 1987, 'ask_vol4': 420, 'bid5': 7.09, 'ask5': 7.18, 'bid_vol5': 1098, 'ask_vol5': 1102}
2025-08-25 13:56:55 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2672 条数据
2025-08-25 13:56:55 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1302/1302 条Tick数据
2025-08-25 13:56:55 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:55 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:55 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:55 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1302 条记录，耗时: 0.40秒
2025-08-25 13:56:55 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:54, 股票数: 4394
2025-08-25 13:56:55 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1175.77 毫秒，每秒处理股票数: 3737.12
2025-08-25 13:56:55 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.18s (网络: 1.18s, 处理: 0.00s, 数据库: 1.18s)
2025-08-25 13:56:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2053
2025-08-25 13:56:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2053 条tick数据
2025-08-25 13:56:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:27'), 'stock_code': '300169', 'price': 8.45, 'volume': 287913, 'amount': 245118864.0, 'open': 8.47, 'high': 8.64, 'low': 8.43, 'last_close': 8.5, 'cur_vol': 15, 'change': 0.0, 'bid1': 8.45, 'ask1': 8.46, 'bid_vol1': 13, 'ask_vol1': 227, 'bid2': 8.44, 'ask2': 8.47, 'bid_vol2': 1205, 'ask_vol2': 257, 'bid3': 8.43, 'ask3': 8.48, 'bid_vol3': 3031, 'ask_vol3': 197, 'bid4': 8.42, 'ask4': 8.49, 'bid_vol4': 1592, 'ask_vol4': 182, 'bid5': 8.41, 'ask5': 8.5, 'bid_vol5': 1546, 'ask_vol5': 393}
2025-08-25 13:56:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:20'), 'stock_code': '300170', 'price': 20.62, 'volume': 1294606, 'amount': 2676891648.0, 'open': 20.25, 'high': 21.5, 'low': 20.04, 'last_close': 19.97, 'cur_vol': 35, 'change': -0.00999999999999801, 'bid1': 20.62, 'ask1': 20.63, 'bid_vol1': 33, 'ask_vol1': 71, 'bid2': 20.61, 'ask2': 20.64, 'bid_vol2': 115, 'ask_vol2': 204, 'bid3': 20.6, 'ask3': 20.650000000000002, 'bid_vol3': 443, 'ask_vol3': 130, 'bid4': 20.580000000000002, 'ask4': 20.66, 'bid_vol4': 23, 'ask_vol4': 738, 'bid5': 20.57, 'ask5': 20.67, 'bid_vol5': 53, 'ask_vol5': 10}
2025-08-25 13:56:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:23'), 'stock_code': '300171', 'price': 15.22, 'volume': 132473, 'amount': 203792944.0, 'open': 15.35, 'high': 15.59, 'low': 15.19, 'last_close': 15.39, 'cur_vol': 10, 'change': -0.009999999999999787, 'bid1': 15.22, 'ask1': 15.23, 'bid_vol1': 197, 'ask_vol1': 62, 'bid2': 15.21, 'ask2': 15.24, 'bid_vol2': 170, 'ask_vol2': 906, 'bid3': 15.200000000000001, 'ask3': 15.25, 'bid_vol3': 1221, 'ask_vol3': 22, 'bid4': 15.19, 'ask4': 15.26, 'bid_vol4': 1326, 'ask_vol4': 115, 'bid5': 15.18, 'ask5': 15.27, 'bid_vol5': 883, 'ask_vol5': 404}
2025-08-25 13:56:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2053, 有效=2053, 无效=0
2025-08-25 13:56:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2053 条数据到stock_tick_data表
2025-08-25 13:56:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:27'), 'stock_code': '300169', 'price': 8.45, 'volume': 287913, 'amount': 245118864.0, 'open': 8.47, 'high': 8.64, 'low': 8.43, 'last_close': 8.5, 'cur_vol': 15, 'change': 0.0, 'bid1': 8.45, 'ask1': 8.46, 'bid_vol1': 13, 'ask_vol1': 227, 'bid2': 8.44, 'ask2': 8.47, 'bid_vol2': 1205, 'ask_vol2': 257, 'bid3': 8.43, 'ask3': 8.48, 'bid_vol3': 3031, 'ask_vol3': 197, 'bid4': 8.42, 'ask4': 8.49, 'bid_vol4': 1592, 'ask_vol4': 182, 'bid5': 8.41, 'ask5': 8.5, 'bid_vol5': 1546, 'ask_vol5': 393}
2025-08-25 13:56:57 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2053 条数据
2025-08-25 13:56:57 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2053/2053 条Tick数据
2025-08-25 13:56:57 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:57 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:57 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:57 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2053 条记录，耗时: 0.37秒
2025-08-25 13:56:57 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:56, 股票数: 4394
2025-08-25 13:56:57 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1153.81 毫秒，每秒处理股票数: 3808.27
2025-08-25 13:56:57 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.15s (网络: 1.15s, 处理: -0.00s, 数据库: 1.15s)
2025-08-25 13:56:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:56:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1502
2025-08-25 13:56:58 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1502/1502 条Tick数据
2025-08-25 13:56:58 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:56:58 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:56:58 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:56:58 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1502 条记录，耗时: 0.09秒
2025-08-25 13:56:58 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:56:58, 股票数: 4394
2025-08-25 13:56:58 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 880.43 毫秒，每秒处理股票数: 4990.77
2025-08-25 13:56:58 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.88s (网络: 0.88s, 处理: 0.00s, 数据库: 0.88s)
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 854
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2356 条tick数据
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:14'), 'stock_code': '002812', 'price': 32.03, 'volume': 161321, 'amount': 519782624.0, 'open': 32.01, 'high': 32.6, 'low': 31.92, 'last_close': 31.89, 'cur_vol': 1, 'change': 0.00999999999999801, 'bid1': 32.02, 'ask1': 32.03, 'bid_vol1': 16, 'ask_vol1': 52, 'bid2': 32.01, 'ask2': 32.04, 'bid_vol2': 212, 'ask_vol2': 230, 'bid3': 32.0, 'ask3': 32.05, 'bid_vol3': 372, 'ask_vol3': 61, 'bid4': 31.990000000000002, 'ask4': 32.06, 'bid_vol4': 47, 'ask_vol4': 43, 'bid5': 31.98, 'ask5': 32.07, 'bid_vol5': 97, 'ask_vol5': 5}
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:25'), 'stock_code': '002815', 'price': 14.73, 'volume': 677717, 'amount': 1011554560.0, 'open': 15.13, 'high': 15.36, 'low': 14.68, 'last_close': 15.36, 'cur_vol': 12, 'change': 0.0, 'bid1': 14.73, 'ask1': 14.74, 'bid_vol1': 224, 'ask_vol1': 68, 'bid2': 14.72, 'ask2': 14.75, 'bid_vol2': 1042, 'ask_vol2': 99, 'bid3': 14.71, 'ask3': 14.76, 'bid_vol3': 1483, 'ask_vol3': 431, 'bid4': 14.700000000000001, 'ask4': 14.77, 'bid_vol4': 5510, 'ask_vol4': 347, 'bid5': 14.69, 'ask5': 14.780000000000001, 'bid_vol5': 895, 'ask_vol5': 456}
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:29'), 'stock_code': '002817', 'price': 8.03, 'volume': 67430, 'amount': 54305152.0, 'open': 8.01, 'high': 8.15, 'low': 7.97, 'last_close': 7.98, 'cur_vol': 3, 'change': 0.0, 'bid1': 8.02, 'ask1': 8.03, 'bid_vol1': 68, 'ask_vol1': 7, 'bid2': 8.01, 'ask2': 8.040000000000001, 'bid_vol2': 359, 'ask_vol2': 172, 'bid3': 8.0, 'ask3': 8.05, 'bid_vol3': 213, 'ask_vol3': 85, 'bid4': 7.99, 'ask4': 8.06, 'bid_vol4': 330, 'ask_vol4': 81, 'bid5': 7.98, 'ask5': 8.07, 'bid_vol5': 263, 'ask_vol5': 297}
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2356, 有效=2356, 无效=0
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2356 条数据到stock_tick_data表
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:14'), 'stock_code': '002812', 'price': 32.03, 'volume': 161321, 'amount': 519782624.0, 'open': 32.01, 'high': 32.6, 'low': 31.92, 'last_close': 31.89, 'cur_vol': 1, 'change': 0.00999999999999801, 'bid1': 32.02, 'ask1': 32.03, 'bid_vol1': 16, 'ask_vol1': 52, 'bid2': 32.01, 'ask2': 32.04, 'bid_vol2': 212, 'ask_vol2': 230, 'bid3': 32.0, 'ask3': 32.05, 'bid_vol3': 372, 'ask_vol3': 61, 'bid4': 31.990000000000002, 'ask4': 32.06, 'bid_vol4': 47, 'ask_vol4': 43, 'bid5': 31.98, 'ask5': 32.07, 'bid_vol5': 97, 'ask_vol5': 5}
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2356 条数据
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 854/854 条Tick数据
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 854 条记录，耗时: 0.36秒
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:00, 股票数: 4394
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 970.55 毫秒，每秒处理股票数: 4527.32
2025-08-25 13:57:00 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.97s (网络: 0.97s, 处理: -0.00s, 数据库: 0.97s)
2025-08-25 13:57:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2118
2025-08-25 13:57:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2118 条tick数据
2025-08-25 13:57:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:54'), 'stock_code': '300169', 'price': 8.45, 'volume': 288029, 'amount': 245216784.0, 'open': 8.47, 'high': 8.64, 'low': 8.43, 'last_close': 8.5, 'cur_vol': 98, 'change': 0.0, 'bid1': 8.44, 'ask1': 8.45, 'bid_vol1': 1096, 'ask_vol1': 229, 'bid2': 8.43, 'ask2': 8.46, 'bid_vol2': 3026, 'ask_vol2': 207, 'bid3': 8.42, 'ask3': 8.47, 'bid_vol3': 1592, 'ask_vol3': 257, 'bid4': 8.41, 'ask4': 8.48, 'bid_vol4': 1546, 'ask_vol4': 197, 'bid5': 8.4, 'ask5': 8.49, 'bid_vol5': 3050, 'ask_vol5': 182}
2025-08-25 13:57:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:47'), 'stock_code': '300170', 'price': 20.61, 'volume': 1294811, 'amount': 2677314304.0, 'open': 20.25, 'high': 21.5, 'low': 20.04, 'last_close': 19.97, 'cur_vol': 165, 'change': -0.010000000000001563, 'bid1': 20.6, 'ask1': 20.61, 'bid_vol1': 407, 'ask_vol1': 96, 'bid2': 20.580000000000002, 'ask2': 20.62, 'bid_vol2': 23, 'ask_vol2': 701, 'bid3': 20.57, 'ask3': 20.63, 'bid_vol3': 53, 'ask_vol3': 86, 'bid4': 20.56, 'ask4': 20.64, 'bid_vol4': 186, 'ask_vol4': 244, 'bid5': 20.55, 'ask5': 20.650000000000002, 'bid_vol5': 211, 'ask_vol5': 130}
2025-08-25 13:57:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:50'), 'stock_code': '300171', 'price': 15.22, 'volume': 132526, 'amount': 203873616.0, 'open': 15.35, 'high': 15.59, 'low': 15.19, 'last_close': 15.39, 'cur_vol': 39, 'change': 0.0, 'bid1': 15.22, 'ask1': 15.23, 'bid_vol1': 147, 'ask_vol1': 69, 'bid2': 15.21, 'ask2': 15.24, 'bid_vol2': 170, 'ask_vol2': 900, 'bid3': 15.200000000000001, 'ask3': 15.25, 'bid_vol3': 1221, 'ask_vol3': 20, 'bid4': 15.19, 'ask4': 15.26, 'bid_vol4': 1326, 'ask_vol4': 115, 'bid5': 15.18, 'ask5': 15.27, 'bid_vol5': 883, 'ask_vol5': 404}
2025-08-25 13:57:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2118, 有效=2118, 无效=0
2025-08-25 13:57:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2118 条数据到stock_tick_data表
2025-08-25 13:57:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:54'), 'stock_code': '300169', 'price': 8.45, 'volume': 288029, 'amount': 245216784.0, 'open': 8.47, 'high': 8.64, 'low': 8.43, 'last_close': 8.5, 'cur_vol': 98, 'change': 0.0, 'bid1': 8.44, 'ask1': 8.45, 'bid_vol1': 1096, 'ask_vol1': 229, 'bid2': 8.43, 'ask2': 8.46, 'bid_vol2': 3026, 'ask_vol2': 207, 'bid3': 8.42, 'ask3': 8.47, 'bid_vol3': 1592, 'ask_vol3': 257, 'bid4': 8.41, 'ask4': 8.48, 'bid_vol4': 1546, 'ask_vol4': 197, 'bid5': 8.4, 'ask5': 8.49, 'bid_vol5': 3050, 'ask_vol5': 182}
2025-08-25 13:57:03 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2118 条数据
2025-08-25 13:57:03 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2118/2118 条Tick数据
2025-08-25 13:57:03 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:03 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:03 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:03 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2118 条记录，耗时: 0.36秒
2025-08-25 13:57:03 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:02, 股票数: 4394
2025-08-25 13:57:03 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1158.65 毫秒，每秒处理股票数: 3792.33
2025-08-25 13:57:03 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.16s (网络: 1.16s, 处理: -0.00s, 数据库: 1.16s)
2025-08-25 13:57:04 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:04 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1687
2025-08-25 13:57:04 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1687 条tick数据
2025-08-25 13:57:04 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:58'), 'stock_code': '600207', 'price': 5.09, 'volume': 219533, 'amount': 112058608.0, 'open': 5.13, 'high': 5.18, 'low': 5.04, 'last_close': 5.13, 'cur_vol': 124, 'change': 0.0, 'bid1': 5.08, 'ask1': 5.09, 'bid_vol1': 970, 'ask_vol1': 119, 'bid2': 5.07, 'ask2': 5.1000000000000005, 'bid_vol2': 821, 'ask_vol2': 1417, 'bid3': 5.0600000000000005, 'ask3': 5.11, 'bid_vol3': 1443, 'ask_vol3': 1461, 'bid4': 5.05, 'ask4': 5.12, 'bid_vol4': 3461, 'ask_vol4': 1016, 'bid5': 5.04, 'ask5': 5.13, 'bid_vol5': 3639, 'ask_vol5': 2815}
2025-08-25 13:57:04 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:58'), 'stock_code': '600208', 'price': 4.89, 'volume': 2234412, 'amount': 1093583872.0, 'open': 4.86, 'high': 4.98, 'low': 4.8, 'last_close': 4.84, 'cur_vol': 530, 'change': 0.0, 'bid1': 4.88, 'ask1': 4.89, 'bid_vol1': 1556, 'ask_vol1': 3774, 'bid2': 4.87, 'ask2': 4.9, 'bid_vol2': 4777, 'ask_vol2': 10668, 'bid3': 4.86, 'ask3': 4.91, 'bid_vol3': 9930, 'ask_vol3': 6603, 'bid4': 4.8500000000000005, 'ask4': 4.92, 'bid_vol4': 19506, 'ask_vol4': 10091, 'bid5': 4.84, 'ask5': 4.93, 'bid_vol5': 12187, 'ask_vol5': 17393}
2025-08-25 13:57:04 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:57'), 'stock_code': '600210', 'price': 6.5, 'volume': 225391, 'amount': 146371392.0, 'open': 6.5, 'high': 6.5200000000000005, 'low': 6.46, 'last_close': 6.48, 'cur_vol': 99, 'change': 0.0, 'bid1': 6.49, 'ask1': 6.5, 'bid_vol1': 1464, 'ask_vol1': 2797, 'bid2': 6.48, 'ask2': 6.51, 'bid_vol2': 10119, 'ask_vol2': 3071, 'bid3': 6.47, 'ask3': 6.5200000000000005, 'bid_vol3': 11473, 'ask_vol3': 6719, 'bid4': 6.46, 'ask4': 6.53, 'bid_vol4': 6786, 'ask_vol4': 6777, 'bid5': 6.45, 'ask5': 6.54, 'bid_vol5': 3498, 'ask_vol5': 3034}
2025-08-25 13:57:04 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1687, 有效=1687, 无效=0
2025-08-25 13:57:04 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1687 条数据到stock_tick_data表
2025-08-25 13:57:04 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:58'), 'stock_code': '600207', 'price': 5.09, 'volume': 219533, 'amount': 112058608.0, 'open': 5.13, 'high': 5.18, 'low': 5.04, 'last_close': 5.13, 'cur_vol': 124, 'change': 0.0, 'bid1': 5.08, 'ask1': 5.09, 'bid_vol1': 970, 'ask_vol1': 119, 'bid2': 5.07, 'ask2': 5.1000000000000005, 'bid_vol2': 821, 'ask_vol2': 1417, 'bid3': 5.0600000000000005, 'ask3': 5.11, 'bid_vol3': 1443, 'ask_vol3': 1461, 'bid4': 5.05, 'ask4': 5.12, 'bid_vol4': 3461, 'ask_vol4': 1016, 'bid5': 5.04, 'ask5': 5.13, 'bid_vol5': 3639, 'ask_vol5': 2815}
2025-08-25 13:57:05 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1687 条数据
2025-08-25 13:57:05 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1687/1687 条Tick数据
2025-08-25 13:57:05 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:05 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:05 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:05 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1687 条记录，耗时: 0.31秒
2025-08-25 13:57:05 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:04, 股票数: 4394
2025-08-25 13:57:05 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1136.19 毫秒，每秒处理股票数: 3867.31
2025-08-25 13:57:05 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.14s (网络: 1.14s, 处理: -0.00s, 数据库: 1.14s)
2025-08-25 13:57:06 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:06 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1272
2025-08-25 13:57:06 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1272/1272 条Tick数据
2025-08-25 13:57:06 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:06 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:06 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:06 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1272 条记录，耗时: 0.09秒
2025-08-25 13:57:06 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:06, 股票数: 4394
2025-08-25 13:57:06 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 890.40 毫秒，每秒处理股票数: 4934.86
2025-08-25 13:57:06 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.89s (网络: 0.89s, 处理: -0.00s, 数据库: 0.89s)
2025-08-25 13:57:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2154
2025-08-25 13:57:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3426 条tick数据
2025-08-25 13:57:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:37'), 'stock_code': '301099', 'price': 39.86, 'volume': 49519, 'amount': 199763328.0, 'open': 40.68, 'high': 41.11, 'low': 39.7, 'last_close': 40.1, 'cur_vol': 23, 'change': 0.01999999999999602, 'bid1': 39.87, 'ask1': 39.88, 'bid_vol1': 2, 'ask_vol1': 13, 'bid2': 39.86, 'ask2': 39.89, 'bid_vol2': 12, 'ask_vol2': 13, 'bid3': 39.85, 'ask3': 39.9, 'bid_vol3': 4, 'ask_vol3': 9, 'bid4': 39.84, 'ask4': 39.94, 'bid_vol4': 2, 'ask_vol4': 18, 'bid5': 39.83, 'ask5': 39.95, 'bid_vol5': 5, 'ask_vol5': 48}
2025-08-25 13:57:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:49'), 'stock_code': '301100', 'price': 19.95, 'volume': 16578, 'amount': 33241368.0, 'open': 20.2, 'high': 20.26, 'low': 19.86, 'last_close': 20.1, 'cur_vol': 1, 'change': 0.00999999999999801, 'bid1': 19.95, 'ask1': 19.96, 'bid_vol1': 1, 'ask_vol1': 7, 'bid2': 19.94, 'ask2': 19.98, 'bid_vol2': 5, 'ask_vol2': 156, 'bid3': 19.93, 'ask3': 20.0, 'bid_vol3': 81, 'ask_vol3': 31, 'bid4': 19.91, 'ask4': 20.01, 'bid_vol4': 26, 'ask_vol4': 86, 'bid5': 19.900000000000002, 'ask5': 20.02, 'bid_vol5': 62, 'ask_vol5': 70}
2025-08-25 13:57:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:32'), 'stock_code': '301101', 'price': 49.660000000000004, 'volume': 81476, 'amount': 403624352.0, 'open': 48.79, 'high': 50.81, 'low': 48.5, 'last_close': 49.0, 'cur_vol': 7, 'change': 0.010000000000005116, 'bid1': 49.65, 'ask1': 49.660000000000004, 'bid_vol1': 14, 'ask_vol1': 163, 'bid2': 49.64, 'ask2': 49.7, 'bid_vol2': 25, 'ask_vol2': 6, 'bid3': 49.63, 'ask3': 49.71, 'bid_vol3': 11, 'ask_vol3': 221, 'bid4': 49.620000000000005, 'ask4': 49.730000000000004, 'bid_vol4': 5, 'ask_vol4': 10, 'bid5': 49.6, 'ask5': 49.75, 'bid_vol5': 32, 'ask_vol5': 112}
2025-08-25 13:57:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3426, 有效=3426, 无效=0
2025-08-25 13:57:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3426 条数据到stock_tick_data表
2025-08-25 13:57:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:37'), 'stock_code': '301099', 'price': 39.86, 'volume': 49519, 'amount': 199763328.0, 'open': 40.68, 'high': 41.11, 'low': 39.7, 'last_close': 40.1, 'cur_vol': 23, 'change': 0.01999999999999602, 'bid1': 39.87, 'ask1': 39.88, 'bid_vol1': 2, 'ask_vol1': 13, 'bid2': 39.86, 'ask2': 39.89, 'bid_vol2': 12, 'ask_vol2': 13, 'bid3': 39.85, 'ask3': 39.9, 'bid_vol3': 4, 'ask_vol3': 9, 'bid4': 39.84, 'ask4': 39.94, 'bid_vol4': 2, 'ask_vol4': 18, 'bid5': 39.83, 'ask5': 39.95, 'bid_vol5': 5, 'ask_vol5': 48}
2025-08-25 13:57:09 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3426 条数据
2025-08-25 13:57:09 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2154/2154 条Tick数据
2025-08-25 13:57:09 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:09 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:09 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:09 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2154 条记录，耗时: 0.49秒
2025-08-25 13:57:09 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:08, 股票数: 4394
2025-08-25 13:57:09 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1280.54 毫秒，每秒处理股票数: 3431.36
2025-08-25 13:57:09 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.28s (网络: 1.28s, 处理: 0.00s, 数据库: 1.28s)
2025-08-25 13:57:10 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:10 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1404
2025-08-25 13:57:10 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1404/1404 条Tick数据
2025-08-25 13:57:10 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:10 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:10 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:10 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1404 条记录，耗时: 0.09秒
2025-08-25 13:57:10 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:10, 股票数: 4394
2025-08-25 13:57:10 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 875.84 毫秒，每秒处理股票数: 5016.88
2025-08-25 13:57:10 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.88s (网络: 0.88s, 处理: 0.00s, 数据库: 0.88s)
2025-08-25 13:57:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1271
2025-08-25 13:57:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2675 条tick数据
2025-08-25 13:57:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:53'), 'stock_code': '001218', 'price': 19.12, 'volume': 13297, 'amount': 25622706.0, 'open': 19.32, 'high': 19.48, 'low': 19.11, 'last_close': 19.32, 'cur_vol': 6, 'change': 0.010000000000001563, 'bid1': 19.12, 'ask1': 19.13, 'bid_vol1': 12, 'ask_vol1': 8, 'bid2': 19.11, 'ask2': 19.14, 'bid_vol2': 37, 'ask_vol2': 12, 'bid3': 19.1, 'ask3': 19.150000000000002, 'bid_vol3': 121, 'ask_vol3': 11, 'bid4': 19.09, 'ask4': 19.16, 'bid_vol4': 5, 'ask_vol4': 20, 'bid5': 19.080000000000002, 'ask5': 19.17, 'bid_vol5': 51, 'ask_vol5': 5}
2025-08-25 13:57:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:32'), 'stock_code': '001221', 'price': 54.64, 'volume': 61703, 'amount': 336840832.0, 'open': 53.39, 'high': 55.35, 'low': 53.300000000000004, 'last_close': 53.410000000000004, 'cur_vol': 9, 'change': 0.01999999999999602, 'bid1': 54.63, 'ask1': 54.64, 'bid_vol1': 2, 'ask_vol1': 3, 'bid2': 54.620000000000005, 'ask2': 54.65, 'bid_vol2': 13, 'ask_vol2': 15, 'bid3': 54.61, 'ask3': 54.660000000000004, 'bid_vol3': 45, 'ask_vol3': 5, 'bid4': 54.6, 'ask4': 54.68, 'bid_vol4': 84, 'ask_vol4': 2, 'bid5': 54.58, 'ask5': 54.69, 'bid_vol5': 6, 'ask_vol5': 53}
2025-08-25 13:57:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:03'), 'stock_code': '001227', 'price': 2.54, 'volume': 908315, 'amount': 229762320.0, 'open': 2.5100000000000002, 'high': 2.5500000000000003, 'low': 2.5, 'last_close': 2.5100000000000002, 'cur_vol': 3, 'change': 0.009999999999999787, 'bid1': 2.5300000000000002, 'ask1': 2.54, 'bid_vol1': 64379, 'ask_vol1': 53117, 'bid2': 2.52, 'ask2': 2.5500000000000003, 'bid_vol2': 79717, 'ask_vol2': 80349, 'bid3': 2.5100000000000002, 'ask3': 2.56, 'bid_vol3': 67660, 'ask_vol3': 42124, 'bid4': 2.5, 'ask4': 2.57, 'bid_vol4': 80984, 'ask_vol4': 31724, 'bid5': 2.49, 'ask5': 2.58, 'bid_vol5': 25737, 'ask_vol5': 24397}
2025-08-25 13:57:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2675, 有效=2675, 无效=0
2025-08-25 13:57:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2675 条数据到stock_tick_data表
2025-08-25 13:57:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:53'), 'stock_code': '001218', 'price': 19.12, 'volume': 13297, 'amount': 25622706.0, 'open': 19.32, 'high': 19.48, 'low': 19.11, 'last_close': 19.32, 'cur_vol': 6, 'change': 0.010000000000001563, 'bid1': 19.12, 'ask1': 19.13, 'bid_vol1': 12, 'ask_vol1': 8, 'bid2': 19.11, 'ask2': 19.14, 'bid_vol2': 37, 'ask_vol2': 12, 'bid3': 19.1, 'ask3': 19.150000000000002, 'bid_vol3': 121, 'ask_vol3': 11, 'bid4': 19.09, 'ask4': 19.16, 'bid_vol4': 5, 'ask_vol4': 20, 'bid5': 19.080000000000002, 'ask5': 19.17, 'bid_vol5': 51, 'ask_vol5': 5}
2025-08-25 13:57:13 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2675 条数据
2025-08-25 13:57:13 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1271/1271 条Tick数据
2025-08-25 13:57:13 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:13 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:13 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:13 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1271 条记录，耗时: 0.43秒
2025-08-25 13:57:13 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:12, 股票数: 4394
2025-08-25 13:57:13 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1215.20 毫秒，每秒处理股票数: 3615.88
2025-08-25 13:57:13 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.22s (网络: 1.22s, 处理: -0.00s, 数据库: 1.22s)
2025-08-25 13:57:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2118
2025-08-25 13:57:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2118 条tick数据
2025-08-25 13:57:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:59'), 'stock_code': '000001', 'price': 12.42, 'volume': 2725494, 'amount': 3363798016.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 307, 'change': -0.009999999999999787, 'bid1': 12.42, 'ask1': 12.43, 'bid_vol1': 668, 'ask_vol1': 1949, 'bid2': 12.41, 'ask2': 12.44, 'bid_vol2': 7769, 'ask_vol2': 4806, 'bid3': 12.4, 'ask3': 12.450000000000001, 'bid_vol3': 9255, 'ask_vol3': 6547, 'bid4': 12.39, 'ask4': 12.46, 'bid_vol4': 4685, 'ask_vol4': 1948, 'bid5': 12.38, 'ask5': 12.47, 'bid_vol5': 8441, 'ask_vol5': 2064}
2025-08-25 13:57:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:02'), 'stock_code': '000002', 'price': 7.19, 'volume': 7146060, 'amount': 5061900800.0, 'open': 6.66, 'high': 7.22, 'low': 6.65, 'last_close': 6.5600000000000005, 'cur_vol': 272, 'change': 0.0, 'bid1': 7.18, 'ask1': 7.19, 'bid_vol1': 8286, 'ask_vol1': 1146, 'bid2': 7.17, 'ask2': 7.2, 'bid_vol2': 11117, 'ask_vol2': 12227, 'bid3': 7.16, 'ask3': 7.21, 'bid_vol3': 11456, 'ask_vol3': 17235, 'bid4': 7.15, 'ask4': 7.22, 'bid_vol4': 20809, 'ask_vol4': 121380, 'bid5': 7.140000000000001, 'ask5': 0.0, 'bid_vol5': 12284, 'ask_vol5': 0}
2025-08-25 13:57:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:01'), 'stock_code': '000011', 'price': 9.48, 'volume': 180472, 'amount': 171335248.0, 'open': 9.43, 'high': 9.620000000000001, 'low': 9.35, 'last_close': 9.41, 'cur_vol': 4, 'change': 0.0, 'bid1': 9.47, 'ask1': 9.48, 'bid_vol1': 172, 'ask_vol1': 140, 'bid2': 9.46, 'ask2': 9.49, 'bid_vol2': 621, 'ask_vol2': 94, 'bid3': 9.450000000000001, 'ask3': 9.5, 'bid_vol3': 1370, 'ask_vol3': 82, 'bid4': 9.44, 'ask4': 9.51, 'bid_vol4': 905, 'ask_vol4': 1, 'bid5': 9.43, 'ask5': 9.52, 'bid_vol5': 546, 'ask_vol5': 159}
2025-08-25 13:57:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2118, 有效=2118, 无效=0
2025-08-25 13:57:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2118 条数据到stock_tick_data表
2025-08-25 13:57:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:59'), 'stock_code': '000001', 'price': 12.42, 'volume': 2725494, 'amount': 3363798016.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 307, 'change': -0.009999999999999787, 'bid1': 12.42, 'ask1': 12.43, 'bid_vol1': 668, 'ask_vol1': 1949, 'bid2': 12.41, 'ask2': 12.44, 'bid_vol2': 7769, 'ask_vol2': 4806, 'bid3': 12.4, 'ask3': 12.450000000000001, 'bid_vol3': 9255, 'ask_vol3': 6547, 'bid4': 12.39, 'ask4': 12.46, 'bid_vol4': 4685, 'ask_vol4': 1948, 'bid5': 12.38, 'ask5': 12.47, 'bid_vol5': 8441, 'ask_vol5': 2064}
2025-08-25 13:57:15 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2118 条数据
2025-08-25 13:57:15 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2118/2118 条Tick数据
2025-08-25 13:57:15 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:15 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:15 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:15 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2118 条记录，耗时: 0.40秒
2025-08-25 13:57:15 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:14, 股票数: 4394
2025-08-25 13:57:15 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1172.98 毫秒，每秒处理股票数: 3746.02
2025-08-25 13:57:15 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.17s (网络: 1.17s, 处理: -0.00s, 数据库: 1.17s)
2025-08-25 13:57:16 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:16 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1392
2025-08-25 13:57:16 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1392/1392 条Tick数据
2025-08-25 13:57:16 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:16 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:16 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:16 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1392 条记录，耗时: 0.09秒
2025-08-25 13:57:16 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:16, 股票数: 4394
2025-08-25 13:57:16 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 873.89 毫秒，每秒处理股票数: 5028.09
2025-08-25 13:57:16 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.87s (网络: 0.87s, 处理: -0.00s, 数据库: 0.87s)
2025-08-25 13:57:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1271
2025-08-25 13:57:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2663 条tick数据
2025-08-25 13:57:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:06'), 'stock_code': '002175', 'price': 4.79, 'volume': 572645, 'amount': 275808096.0, 'open': 4.83, 'high': 4.87, 'low': 4.75, 'last_close': 4.82, 'cur_vol': 11, 'change': 0.0, 'bid1': 4.78, 'ask1': 4.79, 'bid_vol1': 1485, 'ask_vol1': 3065, 'bid2': 4.7700000000000005, 'ask2': 4.8, 'bid_vol2': 4615, 'ask_vol2': 4307, 'bid3': 4.76, 'ask3': 4.8100000000000005, 'bid_vol3': 6830, 'ask_vol3': 4113, 'bid4': 4.75, 'ask4': 4.82, 'bid_vol4': 27997, 'ask_vol4': 3233, 'bid5': 4.74, 'ask5': 4.83, 'bid_vol5': 6966, 'ask_vol5': 4607}
2025-08-25 13:57:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:03'), 'stock_code': '002176', 'price': 9.120000000000001, 'volume': 865971, 'amount': 790547072.0, 'open': 9.0, 'high': 9.27, 'low': 9.0, 'last_close': 9.0, 'cur_vol': 286, 'change': 0.0, 'bid1': 9.120000000000001, 'ask1': 9.13, 'bid_vol1': 65, 'ask_vol1': 1234, 'bid2': 9.11, 'ask2': 9.14, 'bid_vol2': 1723, 'ask_vol2': 696, 'bid3': 9.1, 'ask3': 9.15, 'bid_vol3': 5888, 'ask_vol3': 1175, 'bid4': 9.09, 'ask4': 9.16, 'bid_vol4': 3217, 'ask_vol4': 1190, 'bid5': 9.08, 'ask5': 9.17, 'bid_vol5': 6749, 'ask_vol5': 703}
2025-08-25 13:57:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:03'), 'stock_code': '002177', 'price': 9.51, 'volume': 3431899, 'amount': 3382176256.0, 'open': 10.120000000000001, 'high': 10.4, 'low': 9.4, 'last_close': 10.0, 'cur_vol': 157, 'change': 0.009999999999999787, 'bid1': 9.5, 'ask1': 9.51, 'bid_vol1': 2388, 'ask_vol1': 1227, 'bid2': 9.49, 'ask2': 9.52, 'bid_vol2': 1166, 'ask_vol2': 711, 'bid3': 9.48, 'ask3': 9.53, 'bid_vol3': 1752, 'ask_vol3': 843, 'bid4': 9.47, 'ask4': 9.540000000000001, 'bid_vol4': 882, 'ask_vol4': 381, 'bid5': 9.46, 'ask5': 9.55, 'bid_vol5': 887, 'ask_vol5': 1376}
2025-08-25 13:57:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2663, 有效=2663, 无效=0
2025-08-25 13:57:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2663 条数据到stock_tick_data表
2025-08-25 13:57:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:06'), 'stock_code': '002175', 'price': 4.79, 'volume': 572645, 'amount': 275808096.0, 'open': 4.83, 'high': 4.87, 'low': 4.75, 'last_close': 4.82, 'cur_vol': 11, 'change': 0.0, 'bid1': 4.78, 'ask1': 4.79, 'bid_vol1': 1485, 'ask_vol1': 3065, 'bid2': 4.7700000000000005, 'ask2': 4.8, 'bid_vol2': 4615, 'ask_vol2': 4307, 'bid3': 4.76, 'ask3': 4.8100000000000005, 'bid_vol3': 6830, 'ask_vol3': 4113, 'bid4': 4.75, 'ask4': 4.82, 'bid_vol4': 27997, 'ask_vol4': 3233, 'bid5': 4.74, 'ask5': 4.83, 'bid_vol5': 6966, 'ask_vol5': 4607}
2025-08-25 13:57:19 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2663 条数据
2025-08-25 13:57:19 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1271/1271 条Tick数据
2025-08-25 13:57:19 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:19 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:19 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:19 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1271 条记录，耗时: 0.40秒
2025-08-25 13:57:19 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:18, 股票数: 4394
2025-08-25 13:57:19 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1157.11 毫秒，每秒处理股票数: 3797.38
2025-08-25 13:57:19 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.16s (网络: 1.16s, 处理: -0.00s, 数据库: 1.16s)
2025-08-25 13:57:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2057
2025-08-25 13:57:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2057 条tick数据
2025-08-25 13:57:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:00'), 'stock_code': '002633', 'price': 16.61, 'volume': 44511, 'amount': 74124232.0, 'open': 16.77, 'high': 16.78, 'low': 16.56, 'last_close': 16.76, 'cur_vol': 2, 'change': 0.0, 'bid1': 16.6, 'ask1': 16.61, 'bid_vol1': 149, 'ask_vol1': 20, 'bid2': 16.59, 'ask2': 16.62, 'bid_vol2': 127, 'ask_vol2': 155, 'bid3': 16.580000000000002, 'ask3': 16.63, 'bid_vol3': 217, 'ask_vol3': 18, 'bid4': 16.57, 'ask4': 16.64, 'bid_vol4': 265, 'ask_vol4': 17, 'bid5': 16.56, 'ask5': 16.65, 'bid_vol5': 448, 'ask_vol5': 58}
2025-08-25 13:57:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:01'), 'stock_code': '002635', 'price': 15.3, 'volume': 271846, 'amount': 416035200.0, 'open': 15.21, 'high': 15.68, 'low': 15.05, 'last_close': 15.18, 'cur_vol': 38, 'change': 0.0, 'bid1': 15.3, 'ask1': 15.31, 'bid_vol1': 718, 'ask_vol1': 260, 'bid2': 15.290000000000001, 'ask2': 15.32, 'bid_vol2': 272, 'ask_vol2': 275, 'bid3': 15.280000000000001, 'ask3': 15.33, 'bid_vol3': 327, 'ask_vol3': 424, 'bid4': 15.27, 'ask4': 15.34, 'bid_vol4': 99, 'ask_vol4': 131, 'bid5': 15.26, 'ask5': 15.35, 'bid_vol5': 189, 'ask_vol5': 341}
2025-08-25 13:57:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:02'), 'stock_code': '002636', 'price': 13.41, 'volume': 360331, 'amount': 486277344.0, 'open': 13.6, 'high': 13.68, 'low': 13.35, 'last_close': 13.4, 'cur_vol': 10, 'change': 0.0, 'bid1': 13.4, 'ask1': 13.41, 'bid_vol1': 452, 'ask_vol1': 114, 'bid2': 13.39, 'ask2': 13.42, 'bid_vol2': 258, 'ask_vol2': 668, 'bid3': 13.38, 'ask3': 13.43, 'bid_vol3': 410, 'ask_vol3': 232, 'bid4': 13.370000000000001, 'ask4': 13.44, 'bid_vol4': 546, 'ask_vol4': 254, 'bid5': 13.36, 'ask5': 13.450000000000001, 'bid_vol5': 1137, 'ask_vol5': 637}
2025-08-25 13:57:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2057, 有效=2057, 无效=0
2025-08-25 13:57:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2057 条数据到stock_tick_data表
2025-08-25 13:57:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:00'), 'stock_code': '002633', 'price': 16.61, 'volume': 44511, 'amount': 74124232.0, 'open': 16.77, 'high': 16.78, 'low': 16.56, 'last_close': 16.76, 'cur_vol': 2, 'change': 0.0, 'bid1': 16.6, 'ask1': 16.61, 'bid_vol1': 149, 'ask_vol1': 20, 'bid2': 16.59, 'ask2': 16.62, 'bid_vol2': 127, 'ask_vol2': 155, 'bid3': 16.580000000000002, 'ask3': 16.63, 'bid_vol3': 217, 'ask_vol3': 18, 'bid4': 16.57, 'ask4': 16.64, 'bid_vol4': 265, 'ask_vol4': 17, 'bid5': 16.56, 'ask5': 16.65, 'bid_vol5': 448, 'ask_vol5': 58}
2025-08-25 13:57:21 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2057 条数据
2025-08-25 13:57:21 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2057/2057 条Tick数据
2025-08-25 13:57:21 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:21 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:21 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:21 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2057 条记录，耗时: 0.34秒
2025-08-25 13:57:21 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:20, 股票数: 4394
2025-08-25 13:57:21 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1129.08 毫秒，每秒处理股票数: 3891.66
2025-08-25 13:57:21 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.13s (网络: 1.13s, 处理: -0.00s, 数据库: 1.13s)
2025-08-25 13:57:22 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:22 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1365
2025-08-25 13:57:22 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1365/1365 条Tick数据
2025-08-25 13:57:22 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:22 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:22 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:22 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1365 条记录，耗时: 0.09秒
2025-08-25 13:57:22 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:22, 股票数: 4394
2025-08-25 13:57:22 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 831.44 毫秒，每秒处理股票数: 5284.81
2025-08-25 13:57:22 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.83s (网络: 0.83s, 处理: -0.00s, 数据库: 0.83s)
2025-08-25 13:57:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1203
2025-08-25 13:57:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2568 条tick数据
2025-08-25 13:57:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:10'), 'stock_code': '600067', 'price': 3.3200000000000003, 'volume': 427767, 'amount': 142427584.0, 'open': 3.35, 'high': 3.37, 'low': 3.31, 'last_close': 3.35, 'cur_vol': 3, 'change': 0.0, 'bid1': 3.3200000000000003, 'ask1': 3.33, 'bid_vol1': 14, 'ask_vol1': 6830, 'bid2': 3.31, 'ask2': 3.34, 'bid_vol2': 12765, 'ask_vol2': 3982, 'bid3': 3.3000000000000003, 'ask3': 3.35, 'bid_vol3': 13520, 'ask_vol3': 2957, 'bid4': 3.29, 'ask4': 3.36, 'bid_vol4': 3800, 'ask_vol4': 2524, 'bid5': 3.2800000000000002, 'ask5': 3.37, 'bid_vol5': 3425, 'ask_vol5': 2404}
2025-08-25 13:57:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:08'), 'stock_code': '600073', 'price': 7.63, 'volume': 205207, 'amount': 156484672.0, 'open': 7.63, 'high': 7.66, 'low': 7.5600000000000005, 'last_close': 7.63, 'cur_vol': 21, 'change': 0.009999999999999787, 'bid1': 7.62, 'ask1': 7.63, 'bid_vol1': 4028, 'ask_vol1': 1657, 'bid2': 7.61, 'ask2': 7.640000000000001, 'bid_vol2': 2885, 'ask_vol2': 1824, 'bid3': 7.6000000000000005, 'ask3': 7.65, 'bid_vol3': 2465, 'ask_vol3': 2098, 'bid4': 7.59, 'ask4': 7.66, 'bid_vol4': 1823, 'ask_vol4': 2423, 'bid5': 7.58, 'ask5': 7.67, 'bid_vol5': 1805, 'ask_vol5': 1614}
2025-08-25 13:57:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:09'), 'stock_code': '600075', 'price': 4.62, 'volume': 263865, 'amount': 121822664.0, 'open': 4.6000000000000005, 'high': 4.65, 'low': 4.57, 'last_close': 4.6000000000000005, 'cur_vol': 202, 'change': -0.009999999999999787, 'bid1': 4.62, 'ask1': 4.63, 'bid_vol1': 4282, 'ask_vol1': 5654, 'bid2': 4.61, 'ask2': 4.64, 'bid_vol2': 5010, 'ask_vol2': 10085, 'bid3': 4.6000000000000005, 'ask3': 4.65, 'bid_vol3': 6344, 'ask_vol3': 12415, 'bid4': 4.59, 'ask4': 4.66, 'bid_vol4': 3821, 'ask_vol4': 5180, 'bid5': 4.58, 'ask5': 4.67, 'bid_vol5': 4440, 'ask_vol5': 3068}
2025-08-25 13:57:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2568, 有效=2568, 无效=0
2025-08-25 13:57:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2568 条数据到stock_tick_data表
2025-08-25 13:57:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:10'), 'stock_code': '600067', 'price': 3.3200000000000003, 'volume': 427767, 'amount': 142427584.0, 'open': 3.35, 'high': 3.37, 'low': 3.31, 'last_close': 3.35, 'cur_vol': 3, 'change': 0.0, 'bid1': 3.3200000000000003, 'ask1': 3.33, 'bid_vol1': 14, 'ask_vol1': 6830, 'bid2': 3.31, 'ask2': 3.34, 'bid_vol2': 12765, 'ask_vol2': 3982, 'bid3': 3.3000000000000003, 'ask3': 3.35, 'bid_vol3': 13520, 'ask_vol3': 2957, 'bid4': 3.29, 'ask4': 3.36, 'bid_vol4': 3800, 'ask_vol4': 2524, 'bid5': 3.2800000000000002, 'ask5': 3.37, 'bid_vol5': 3425, 'ask_vol5': 2404}
2025-08-25 13:57:25 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2568 条数据
2025-08-25 13:57:25 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1203/1203 条Tick数据
2025-08-25 13:57:25 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:25 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:25 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:25 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1203 条记录，耗时: 0.39秒
2025-08-25 13:57:25 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:24, 股票数: 4394
2025-08-25 13:57:25 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1172.51 毫秒，每秒处理股票数: 3747.52
2025-08-25 13:57:25 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.17s (网络: 1.17s, 处理: -0.00s, 数据库: 1.17s)
2025-08-25 13:57:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1931
2025-08-25 13:57:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1931 条tick数据
2025-08-25 13:57:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:10'), 'stock_code': '002686', 'price': 7.12, 'volume': 132485, 'amount': 94969128.0, 'open': 7.25, 'high': 7.25, 'low': 7.09, 'last_close': 7.18, 'cur_vol': 2, 'change': 0.0, 'bid1': 7.11, 'ask1': 7.12, 'bid_vol1': 927, 'ask_vol1': 438, 'bid2': 7.1000000000000005, 'ask2': 7.13, 'bid_vol2': 2151, 'ask_vol2': 360, 'bid3': 7.09, 'ask3': 7.140000000000001, 'bid_vol3': 900, 'ask_vol3': 292, 'bid4': 7.08, 'ask4': 7.15, 'bid_vol4': 772, 'ask_vol4': 506, 'bid5': 7.07, 'ask5': 7.16, 'bid_vol5': 499, 'ask_vol5': 1085}
2025-08-25 13:57:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:10'), 'stock_code': '002688', 'price': 7.26, 'volume': 297381, 'amount': 216641664.0, 'open': 7.25, 'high': 7.3500000000000005, 'low': 7.21, 'last_close': 7.2700000000000005, 'cur_vol': 5, 'change': 0.0, 'bid1': 7.26, 'ask1': 7.2700000000000005, 'bid_vol1': 411, 'ask_vol1': 1389, 'bid2': 7.25, 'ask2': 7.28, 'bid_vol2': 2581, 'ask_vol2': 1792, 'bid3': 7.24, 'ask3': 7.29, 'bid_vol3': 3432, 'ask_vol3': 1145, 'bid4': 7.23, 'ask4': 7.3, 'bid_vol4': 2384, 'ask_vol4': 3021, 'bid5': 7.22, 'ask5': 7.3100000000000005, 'bid_vol5': 3627, 'ask_vol5': 1052}
2025-08-25 13:57:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:11'), 'stock_code': '002689', 'price': 4.17, 'volume': 349437, 'amount': 146924800.0, 'open': 4.22, 'high': 4.28, 'low': 4.14, 'last_close': 4.21, 'cur_vol': 43, 'change': 0.0, 'bid1': 4.16, 'ask1': 4.17, 'bid_vol1': 2316, 'ask_vol1': 1515, 'bid2': 4.15, 'ask2': 4.18, 'bid_vol2': 5770, 'ask_vol2': 1823, 'bid3': 4.14, 'ask3': 4.19, 'bid_vol3': 8036, 'ask_vol3': 1103, 'bid4': 4.13, 'ask4': 4.2, 'bid_vol4': 4937, 'ask_vol4': 1978, 'bid5': 4.12, 'ask5': 4.21, 'bid_vol5': 3963, 'ask_vol5': 1739}
2025-08-25 13:57:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1931, 有效=1931, 无效=0
2025-08-25 13:57:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1931 条数据到stock_tick_data表
2025-08-25 13:57:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:10'), 'stock_code': '002686', 'price': 7.12, 'volume': 132485, 'amount': 94969128.0, 'open': 7.25, 'high': 7.25, 'low': 7.09, 'last_close': 7.18, 'cur_vol': 2, 'change': 0.0, 'bid1': 7.11, 'ask1': 7.12, 'bid_vol1': 927, 'ask_vol1': 438, 'bid2': 7.1000000000000005, 'ask2': 7.13, 'bid_vol2': 2151, 'ask_vol2': 360, 'bid3': 7.09, 'ask3': 7.140000000000001, 'bid_vol3': 900, 'ask_vol3': 292, 'bid4': 7.08, 'ask4': 7.15, 'bid_vol4': 772, 'ask_vol4': 506, 'bid5': 7.07, 'ask5': 7.16, 'bid_vol5': 499, 'ask_vol5': 1085}
2025-08-25 13:57:27 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1931 条数据
2025-08-25 13:57:27 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1931/1931 条Tick数据
2025-08-25 13:57:27 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:27 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:27 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:27 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1931 条记录，耗时: 0.32秒
2025-08-25 13:57:27 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:26, 股票数: 4394
2025-08-25 13:57:27 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1106.96 毫秒，每秒处理股票数: 3969.44
2025-08-25 13:57:27 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.11s (网络: 1.11s, 处理: -0.00s, 数据库: 1.11s)
2025-08-25 13:57:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1346
2025-08-25 13:57:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1346 条tick数据
2025-08-25 13:57:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:03'), 'stock_code': '000777', 'price': 21.67, 'volume': 219354, 'amount': 472127808.0, 'open': 21.3, 'high': 21.8, 'low': 21.25, 'last_close': 21.28, 'cur_vol': 41, 'change': 0.0, 'bid1': 21.67, 'ask1': 21.68, 'bid_vol1': 67, 'ask_vol1': 358, 'bid2': 21.66, 'ask2': 21.69, 'bid_vol2': 184, 'ask_vol2': 282, 'bid3': 21.650000000000002, 'ask3': 21.7, 'bid_vol3': 380, 'ask_vol3': 873, 'bid4': 21.64, 'ask4': 21.71, 'bid_vol4': 100, 'ask_vol4': 156, 'bid5': 21.63, 'ask5': 21.72, 'bid_vol5': 121, 'ask_vol5': 596}
2025-08-25 13:57:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:12'), 'stock_code': '000782', 'price': 5.82, 'volume': 328784, 'amount': 192738160.0, 'open': 5.74, 'high': 5.99, 'low': 5.66, 'last_close': 5.74, 'cur_vol': 257, 'change': 0.0, 'bid1': 5.8100000000000005, 'ask1': 5.82, 'bid_vol1': 2167, 'ask_vol1': 13, 'bid2': 5.8, 'ask2': 5.83, 'bid_vol2': 1064, 'ask_vol2': 187, 'bid3': 5.79, 'ask3': 5.84, 'bid_vol3': 796, 'ask_vol3': 165, 'bid4': 5.78, 'ask4': 5.8500000000000005, 'bid_vol4': 827, 'ask_vol4': 227, 'bid5': 5.7700000000000005, 'ask5': 5.86, 'bid_vol5': 779, 'ask_vol5': 157}
2025-08-25 13:57:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:11'), 'stock_code': '000783', 'price': 8.58, 'volume': 2232772, 'amount': 1912153856.0, 'open': 8.32, 'high': 9.03, 'low': 8.25, 'last_close': 8.23, 'cur_vol': 13, 'change': -0.009999999999999787, 'bid1': 8.58, 'ask1': 8.59, 'bid_vol1': 68, 'ask_vol1': 1237, 'bid2': 8.57, 'ask2': 8.6, 'bid_vol2': 1636, 'ask_vol2': 2582, 'bid3': 8.56, 'ask3': 8.61, 'bid_vol3': 2070, 'ask_vol3': 2788, 'bid4': 8.55, 'ask4': 8.620000000000001, 'bid_vol4': 2186, 'ask_vol4': 2642, 'bid5': 8.540000000000001, 'ask5': 8.63, 'bid_vol5': 1814, 'ask_vol5': 1591}
2025-08-25 13:57:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1346, 有效=1346, 无效=0
2025-08-25 13:57:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1346 条数据到stock_tick_data表
2025-08-25 13:57:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:03'), 'stock_code': '000777', 'price': 21.67, 'volume': 219354, 'amount': 472127808.0, 'open': 21.3, 'high': 21.8, 'low': 21.25, 'last_close': 21.28, 'cur_vol': 41, 'change': 0.0, 'bid1': 21.67, 'ask1': 21.68, 'bid_vol1': 67, 'ask_vol1': 358, 'bid2': 21.66, 'ask2': 21.69, 'bid_vol2': 184, 'ask_vol2': 282, 'bid3': 21.650000000000002, 'ask3': 21.7, 'bid_vol3': 380, 'ask_vol3': 873, 'bid4': 21.64, 'ask4': 21.71, 'bid_vol4': 100, 'ask_vol4': 156, 'bid5': 21.63, 'ask5': 21.72, 'bid_vol5': 121, 'ask_vol5': 596}
2025-08-25 13:57:29 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1346 条数据
2025-08-25 13:57:29 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1346/1346 条Tick数据
2025-08-25 13:57:29 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:29 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:29 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:29 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1346 条记录，耗时: 0.25秒
2025-08-25 13:57:29 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:28, 股票数: 4394
2025-08-25 13:57:29 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1050.88 毫秒，每秒处理股票数: 4181.28
2025-08-25 13:57:29 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.05s (网络: 1.05s, 处理: 0.00s, 数据库: 1.05s)
2025-08-25 13:57:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1251
2025-08-25 13:57:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1251 条tick数据
2025-08-25 13:57:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:07'), 'stock_code': '300839', 'price': 15.15, 'volume': 84161, 'amount': 126775968.0, 'open': 15.1, 'high': 15.370000000000001, 'low': 14.77, 'last_close': 15.02, 'cur_vol': 51, 'change': 0.009999999999999787, 'bid1': 15.13, 'ask1': 15.15, 'bid_vol1': 150, 'ask_vol1': 190, 'bid2': 15.120000000000001, 'ask2': 15.16, 'bid_vol2': 12, 'ask_vol2': 13, 'bid3': 15.11, 'ask3': 15.17, 'bid_vol3': 71, 'ask_vol3': 4, 'bid4': 15.1, 'ask4': 15.18, 'bid_vol4': 142, 'ask_vol4': 54, 'bid5': 15.09, 'ask5': 15.19, 'bid_vol5': 10, 'ask_vol5': 8}
2025-08-25 13:57:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:01'), 'stock_code': '300840', 'price': 24.01, 'volume': 217002, 'amount': 522614240.0, 'open': 23.68, 'high': 24.55, 'low': 23.490000000000002, 'last_close': 23.68, 'cur_vol': 25, 'change': 0.0, 'bid1': 24.01, 'ask1': 24.02, 'bid_vol1': 19, 'ask_vol1': 29, 'bid2': 24.0, 'ask2': 24.03, 'bid_vol2': 208, 'ask_vol2': 39, 'bid3': 23.990000000000002, 'ask3': 24.04, 'bid_vol3': 1, 'ask_vol3': 51, 'bid4': 23.98, 'ask4': 24.05, 'bid_vol4': 214, 'ask_vol4': 80, 'bid5': 23.97, 'ask5': 24.060000000000002, 'bid_vol5': 313, 'ask_vol5': 132}
2025-08-25 13:57:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:25'), 'stock_code': '300841', 'price': 84.57000000000001, 'volume': 37769, 'amount': 321973824.0, 'open': 84.77, 'high': 86.49, 'low': 84.0, 'last_close': 84.77, 'cur_vol': 28, 'change': -0.06999999999999318, 'bid1': 84.56, 'ask1': 84.57000000000001, 'bid_vol1': 1, 'ask_vol1': 5, 'bid2': 84.55, 'ask2': 84.63, 'bid_vol2': 3, 'ask_vol2': 5, 'bid3': 84.54, 'ask3': 84.64, 'bid_vol3': 2, 'ask_vol3': 11, 'bid4': 84.53, 'ask4': 84.65, 'bid_vol4': 2, 'ask_vol4': 75, 'bid5': 84.49, 'ask5': 84.68, 'bid_vol5': 2, 'ask_vol5': 3}
2025-08-25 13:57:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1251, 有效=1251, 无效=0
2025-08-25 13:57:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1251 条数据到stock_tick_data表
2025-08-25 13:57:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:07'), 'stock_code': '300839', 'price': 15.15, 'volume': 84161, 'amount': 126775968.0, 'open': 15.1, 'high': 15.370000000000001, 'low': 14.77, 'last_close': 15.02, 'cur_vol': 51, 'change': 0.009999999999999787, 'bid1': 15.13, 'ask1': 15.15, 'bid_vol1': 150, 'ask_vol1': 190, 'bid2': 15.120000000000001, 'ask2': 15.16, 'bid_vol2': 12, 'ask_vol2': 13, 'bid3': 15.11, 'ask3': 15.17, 'bid_vol3': 71, 'ask_vol3': 4, 'bid4': 15.1, 'ask4': 15.18, 'bid_vol4': 142, 'ask_vol4': 54, 'bid5': 15.09, 'ask5': 15.19, 'bid_vol5': 10, 'ask_vol5': 8}
2025-08-25 13:57:31 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1251 条数据
2025-08-25 13:57:31 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1251/1251 条Tick数据
2025-08-25 13:57:31 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:31 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:31 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:31 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1251 条记录，耗时: 0.26秒
2025-08-25 13:57:31 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:30, 股票数: 4394
2025-08-25 13:57:31 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1045.55 毫秒，每秒处理股票数: 4202.57
2025-08-25 13:57:31 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.05s (网络: 1.05s, 处理: -0.00s, 数据库: 1.05s)
2025-08-25 13:57:32 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:32 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2144
2025-08-25 13:57:32 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2144/2144 条Tick数据
2025-08-25 13:57:32 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:32 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:32 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:32 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2144 条记录，耗时: 0.10秒
2025-08-25 13:57:32 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:32, 股票数: 4394
2025-08-25 13:57:32 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 887.18 毫秒，每秒处理股票数: 4952.79
2025-08-25 13:57:32 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.89s (网络: 0.89s, 处理: -0.00s, 数据库: 0.89s)
2025-08-25 13:57:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1777
2025-08-25 13:57:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3921 条tick数据
2025-08-25 13:57:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:10'), 'stock_code': '000001', 'price': 12.43, 'volume': 2725647, 'amount': 3363988224.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 79, 'change': 0.009999999999999787, 'bid1': 12.42, 'ask1': 12.43, 'bid_vol1': 875, 'ask_vol1': 1832, 'bid2': 12.41, 'ask2': 12.44, 'bid_vol2': 7776, 'ask_vol2': 4813, 'bid3': 12.4, 'ask3': 12.450000000000001, 'bid_vol3': 9278, 'ask_vol3': 6562, 'bid4': 12.39, 'ask4': 12.46, 'bid_vol4': 4705, 'ask_vol4': 1948, 'bid5': 12.38, 'ask5': 12.47, 'bid_vol5': 8431, 'ask_vol5': 2064}
2025-08-25 13:57:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:13'), 'stock_code': '000002', 'price': 7.19, 'volume': 7147622, 'amount': 5063023616.0, 'open': 6.66, 'high': 7.22, 'low': 6.65, 'last_close': 6.5600000000000005, 'cur_vol': 87, 'change': 0.0, 'bid1': 7.18, 'ask1': 7.19, 'bid_vol1': 8357, 'ask_vol1': 903, 'bid2': 7.17, 'ask2': 7.2, 'bid_vol2': 11132, 'ask_vol2': 12844, 'bid3': 7.16, 'ask3': 7.21, 'bid_vol3': 11400, 'ask_vol3': 17175, 'bid4': 7.15, 'ask4': 7.22, 'bid_vol4': 20770, 'ask_vol4': 122643, 'bid5': 7.140000000000001, 'ask5': 0.0, 'bid_vol5': 12299, 'ask_vol5': 0}
2025-08-25 13:57:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:13'), 'stock_code': '000006', 'price': 7.22, 'volume': 509837, 'amount': 372189280.0, 'open': 7.29, 'high': 7.45, 'low': 7.15, 'last_close': 7.3, 'cur_vol': 3, 'change': 0.0, 'bid1': 7.21, 'ask1': 7.22, 'bid_vol1': 1289, 'ask_vol1': 1123, 'bid2': 7.2, 'ask2': 7.23, 'bid_vol2': 5854, 'ask_vol2': 1396, 'bid3': 7.19, 'ask3': 7.24, 'bid_vol3': 3441, 'ask_vol3': 720, 'bid4': 7.18, 'ask4': 7.25, 'bid_vol4': 2881, 'ask_vol4': 545, 'bid5': 7.17, 'ask5': 7.26, 'bid_vol5': 1173, 'ask_vol5': 172}
2025-08-25 13:57:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3921, 有效=3921, 无效=0
2025-08-25 13:57:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3921 条数据到stock_tick_data表
2025-08-25 13:57:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:10'), 'stock_code': '000001', 'price': 12.43, 'volume': 2725647, 'amount': 3363988224.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 79, 'change': 0.009999999999999787, 'bid1': 12.42, 'ask1': 12.43, 'bid_vol1': 875, 'ask_vol1': 1832, 'bid2': 12.41, 'ask2': 12.44, 'bid_vol2': 7776, 'ask_vol2': 4813, 'bid3': 12.4, 'ask3': 12.450000000000001, 'bid_vol3': 9278, 'ask_vol3': 6562, 'bid4': 12.39, 'ask4': 12.46, 'bid_vol4': 4705, 'ask_vol4': 1948, 'bid5': 12.38, 'ask5': 12.47, 'bid_vol5': 8431, 'ask_vol5': 2064}
2025-08-25 13:57:35 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3921 条数据
2025-08-25 13:57:35 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1777/1777 条Tick数据
2025-08-25 13:57:35 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:35 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:35 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:35 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1777 条记录，耗时: 0.56秒
2025-08-25 13:57:35 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:34, 股票数: 4394
2025-08-25 13:57:35 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1287.24 毫秒，每秒处理股票数: 3413.51
2025-08-25 13:57:35 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.29s (网络: 1.29s, 处理: -0.00s, 数据库: 1.29s)
2025-08-25 13:57:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1362
2025-08-25 13:57:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1362 条tick数据
2025-08-25 13:57:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:02'), 'stock_code': '301282', 'price': 28.67, 'volume': 131334, 'amount': 384268928.0, 'open': 30.26, 'high': 30.45, 'low': 28.57, 'last_close': 29.45, 'cur_vol': 2, 'change': 0.0, 'bid1': 28.67, 'ask1': 28.68, 'bid_vol1': 22, 'ask_vol1': 21, 'bid2': 28.650000000000002, 'ask2': 28.69, 'bid_vol2': 73, 'ask_vol2': 3, 'bid3': 28.64, 'ask3': 28.7, 'bid_vol3': 42, 'ask_vol3': 34, 'bid4': 28.63, 'ask4': 28.71, 'bid_vol4': 115, 'ask_vol4': 10, 'bid5': 28.62, 'ask5': 28.72, 'bid_vol5': 32, 'ask_vol5': 30}
2025-08-25 13:57:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:03'), 'stock_code': '301286', 'price': 27.57, 'volume': 36190, 'amount': 100217616.0, 'open': 27.84, 'high': 28.1, 'low': 27.21, 'last_close': 27.830000000000002, 'cur_vol': 1, 'change': 0.0, 'bid1': 27.560000000000002, 'ask1': 27.57, 'bid_vol1': 14, 'ask_vol1': 2, 'bid2': 27.54, 'ask2': 27.580000000000002, 'bid_vol2': 5, 'ask_vol2': 11, 'bid3': 27.53, 'ask3': 27.59, 'bid_vol3': 24, 'ask_vol3': 20, 'bid4': 27.52, 'ask4': 27.6, 'bid_vol4': 15, 'ask_vol4': 10, 'bid5': 27.51, 'ask5': 27.62, 'bid_vol5': 36, 'ask_vol5': 5}
2025-08-25 13:57:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:54'), 'stock_code': '301287', 'price': 42.22, 'volume': 16059, 'amount': 68141640.0, 'open': 43.09, 'high': 43.1, 'low': 42.08, 'last_close': 42.83, 'cur_vol': 2, 'change': 0.0, 'bid1': 42.22, 'ask1': 42.230000000000004, 'bid_vol1': 89, 'ask_vol1': 121, 'bid2': 42.21, 'ask2': 42.24, 'bid_vol2': 69, 'ask_vol2': 61, 'bid3': 42.2, 'ask3': 42.25, 'bid_vol3': 125, 'ask_vol3': 2, 'bid4': 42.19, 'ask4': 42.26, 'bid_vol4': 105, 'ask_vol4': 2, 'bid5': 42.18, 'ask5': 42.28, 'bid_vol5': 65, 'ask_vol5': 1}
2025-08-25 13:57:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1362, 有效=1362, 无效=0
2025-08-25 13:57:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1362 条数据到stock_tick_data表
2025-08-25 13:57:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:02'), 'stock_code': '301282', 'price': 28.67, 'volume': 131334, 'amount': 384268928.0, 'open': 30.26, 'high': 30.45, 'low': 28.57, 'last_close': 29.45, 'cur_vol': 2, 'change': 0.0, 'bid1': 28.67, 'ask1': 28.68, 'bid_vol1': 22, 'ask_vol1': 21, 'bid2': 28.650000000000002, 'ask2': 28.69, 'bid_vol2': 73, 'ask_vol2': 3, 'bid3': 28.64, 'ask3': 28.7, 'bid_vol3': 42, 'ask_vol3': 34, 'bid4': 28.63, 'ask4': 28.71, 'bid_vol4': 115, 'ask_vol4': 10, 'bid5': 28.62, 'ask5': 28.72, 'bid_vol5': 32, 'ask_vol5': 30}
2025-08-25 13:57:37 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1362 条数据
2025-08-25 13:57:37 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1362/1362 条Tick数据
2025-08-25 13:57:37 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:37 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:37 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:37 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1362 条记录，耗时: 0.25秒
2025-08-25 13:57:37 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:36, 股票数: 4394
2025-08-25 13:57:37 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1021.08 毫秒，每秒处理股票数: 4303.28
2025-08-25 13:57:37 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.02s (网络: 1.02s, 处理: -0.00s, 数据库: 1.02s)
2025-08-25 13:57:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2537
2025-08-25 13:57:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2537 条tick数据
2025-08-25 13:57:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:14'), 'stock_code': '000001', 'price': 12.42, 'volume': 2725674, 'amount': 3364021760.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 27, 'change': -0.009999999999999787, 'bid1': 12.42, 'ask1': 12.43, 'bid_vol1': 994, 'ask_vol1': 1851, 'bid2': 12.41, 'ask2': 12.44, 'bid_vol2': 7777, 'ask_vol2': 4816, 'bid3': 12.4, 'ask3': 12.450000000000001, 'bid_vol3': 9283, 'ask_vol3': 6607, 'bid4': 12.39, 'ask4': 12.46, 'bid_vol4': 4705, 'ask_vol4': 1948, 'bid5': 12.38, 'ask5': 12.47, 'bid_vol5': 8431, 'ask_vol5': 2094}
2025-08-25 13:57:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:17'), 'stock_code': '000002', 'price': 7.19, 'volume': 7148963, 'amount': 5063987712.0, 'open': 6.66, 'high': 7.22, 'low': 6.65, 'last_close': 6.5600000000000005, 'cur_vol': 301, 'change': 0.0, 'bid1': 7.18, 'ask1': 7.19, 'bid_vol1': 7897, 'ask_vol1': 188, 'bid2': 7.17, 'ask2': 7.2, 'bid_vol2': 11992, 'ask_vol2': 12840, 'bid3': 7.16, 'ask3': 7.21, 'bid_vol3': 11400, 'ask_vol3': 17243, 'bid4': 7.15, 'ask4': 7.22, 'bid_vol4': 20764, 'ask_vol4': 122695, 'bid5': 7.140000000000001, 'ask5': 0.0, 'bid_vol5': 12299, 'ask_vol5': 0}
2025-08-25 13:57:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:17'), 'stock_code': '000006', 'price': 7.22, 'volume': 509930, 'amount': 372256448.0, 'open': 7.29, 'high': 7.45, 'low': 7.15, 'last_close': 7.3, 'cur_vol': 51, 'change': 0.0, 'bid1': 7.21, 'ask1': 7.22, 'bid_vol1': 1310, 'ask_vol1': 1030, 'bid2': 7.2, 'ask2': 7.23, 'bid_vol2': 5854, 'ask_vol2': 1396, 'bid3': 7.19, 'ask3': 7.24, 'bid_vol3': 3427, 'ask_vol3': 720, 'bid4': 7.18, 'ask4': 7.25, 'bid_vol4': 2892, 'ask_vol4': 545, 'bid5': 7.17, 'ask5': 7.26, 'bid_vol5': 1173, 'ask_vol5': 172}
2025-08-25 13:57:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2537, 有效=2537, 无效=0
2025-08-25 13:57:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2537 条数据到stock_tick_data表
2025-08-25 13:57:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:14'), 'stock_code': '000001', 'price': 12.42, 'volume': 2725674, 'amount': 3364021760.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 27, 'change': -0.009999999999999787, 'bid1': 12.42, 'ask1': 12.43, 'bid_vol1': 994, 'ask_vol1': 1851, 'bid2': 12.41, 'ask2': 12.44, 'bid_vol2': 7777, 'ask_vol2': 4816, 'bid3': 12.4, 'ask3': 12.450000000000001, 'bid_vol3': 9283, 'ask_vol3': 6607, 'bid4': 12.39, 'ask4': 12.46, 'bid_vol4': 4705, 'ask_vol4': 1948, 'bid5': 12.38, 'ask5': 12.47, 'bid_vol5': 8431, 'ask_vol5': 2094}
2025-08-25 13:57:39 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2537 条数据
2025-08-25 13:57:39 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2537/2537 条Tick数据
2025-08-25 13:57:39 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:39 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:39 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:39 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2537 条记录，耗时: 0.41秒
2025-08-25 13:57:39 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:38, 股票数: 4394
2025-08-25 13:57:39 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1211.56 毫秒，每秒处理股票数: 3626.73
2025-08-25 13:57:39 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.21s (网络: 1.21s, 处理: -0.00s, 数据库: 1.21s)
2025-08-25 13:57:40 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:40 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1618
2025-08-25 13:57:40 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1618/1618 条Tick数据
2025-08-25 13:57:40 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:40 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:40 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:40 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1618 条记录，耗时: 0.09秒
2025-08-25 13:57:40 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:40, 股票数: 4394
2025-08-25 13:57:40 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 902.92 毫秒，每秒处理股票数: 4866.45
2025-08-25 13:57:40 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.90s (网络: 0.90s, 处理: -0.00s, 数据库: 0.90s)
2025-08-25 13:57:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1479
2025-08-25 13:57:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3097 条tick数据
2025-08-25 13:57:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:13'), 'stock_code': '000860', 'price': 16.95, 'volume': 325093, 'amount': 543816960.0, 'open': 16.42, 'high': 17.11, 'low': 16.330000000000002, 'last_close': 16.39, 'cur_vol': 271, 'change': 0.0, 'bid1': 16.95, 'ask1': 16.96, 'bid_vol1': 227, 'ask_vol1': 10, 'bid2': 16.94, 'ask2': 16.97, 'bid_vol2': 192, 'ask_vol2': 308, 'bid3': 16.93, 'ask3': 16.98, 'bid_vol3': 216, 'ask_vol3': 375, 'bid4': 16.92, 'ask4': 16.990000000000002, 'bid_vol4': 88, 'ask_vol4': 312, 'bid5': 16.91, 'ask5': 17.0, 'bid_vol5': 117, 'ask_vol5': 1542}
2025-08-25 13:57:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:17'), 'stock_code': '000876', 'price': 10.040000000000001, 'volume': 388417, 'amount': 388436512.0, 'open': 9.99, 'high': 10.05, 'low': 9.94, 'last_close': 9.99, 'cur_vol': 3, 'change': 0.0, 'bid1': 10.040000000000001, 'ask1': 10.05, 'bid_vol1': 988, 'ask_vol1': 7920, 'bid2': 10.03, 'ask2': 10.06, 'bid_vol2': 3997, 'ask_vol2': 2490, 'bid3': 10.02, 'ask3': 10.07, 'bid_vol3': 1314, 'ask_vol3': 1528, 'bid4': 10.01, 'ask4': 10.08, 'bid_vol4': 2109, 'ask_vol4': 2800, 'bid5': 10.0, 'ask5': 10.09, 'bid_vol5': 2215, 'ask_vol5': 2430}
2025-08-25 13:57:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:19'), 'stock_code': '000877', 'price': 7.24, 'volume': 1864186, 'amount': 1306726656.0, 'open': 6.72, 'high': 7.24, 'low': 6.66, 'last_close': 6.58, 'cur_vol': 14, 'change': 0.0, 'bid1': 7.24, 'ask1': 0.0, 'bid_vol1': 219454, 'ask_vol1': 0, 'bid2': 7.23, 'ask2': 0.0, 'bid_vol2': 1035, 'ask_vol2': 0, 'bid3': 7.22, 'ask3': 0.0, 'bid_vol3': 1968, 'ask_vol3': 0, 'bid4': 7.21, 'ask4': 0.0, 'bid_vol4': 841, 'ask_vol4': 0, 'bid5': 7.2, 'ask5': 0.0, 'bid_vol5': 369, 'ask_vol5': 0}
2025-08-25 13:57:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3097, 有效=3097, 无效=0
2025-08-25 13:57:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3097 条数据到stock_tick_data表
2025-08-25 13:57:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:13'), 'stock_code': '000860', 'price': 16.95, 'volume': 325093, 'amount': 543816960.0, 'open': 16.42, 'high': 17.11, 'low': 16.330000000000002, 'last_close': 16.39, 'cur_vol': 271, 'change': 0.0, 'bid1': 16.95, 'ask1': 16.96, 'bid_vol1': 227, 'ask_vol1': 10, 'bid2': 16.94, 'ask2': 16.97, 'bid_vol2': 192, 'ask_vol2': 308, 'bid3': 16.93, 'ask3': 16.98, 'bid_vol3': 216, 'ask_vol3': 375, 'bid4': 16.92, 'ask4': 16.990000000000002, 'bid_vol4': 88, 'ask_vol4': 312, 'bid5': 16.91, 'ask5': 17.0, 'bid_vol5': 117, 'ask_vol5': 1542}
2025-08-25 13:57:43 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3097 条数据
2025-08-25 13:57:43 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1479/1479 条Tick数据
2025-08-25 13:57:43 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:43 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:43 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:43 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1479 条记录，耗时: 0.46秒
2025-08-25 13:57:43 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:42, 股票数: 4394
2025-08-25 13:57:43 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1255.16 毫秒，每秒处理股票数: 3500.75
2025-08-25 13:57:43 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.26s (网络: 1.26s, 处理: 0.00s, 数据库: 1.26s)
2025-08-25 13:57:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2098
2025-08-25 13:57:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2098 条tick数据
2025-08-25 13:57:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:27'), 'stock_code': '301486', 'price': 95.4, 'volume': 113819, 'amount': 1052292416.0, 'open': 92.01, 'high': 95.78, 'low': 89.5, 'last_close': 91.8, 'cur_vol': 20, 'change': 0.0, 'bid1': 95.39, 'ask1': 95.4, 'bid_vol1': 14, 'ask_vol1': 1, 'bid2': 95.38, 'ask2': 95.47, 'bid_vol2': 3, 'ask_vol2': 24, 'bid3': 95.36, 'ask3': 95.48, 'bid_vol3': 5, 'ask_vol3': 23, 'bid4': 95.35000000000001, 'ask4': 95.49, 'bid_vol4': 3, 'ask_vol4': 39, 'bid5': 95.34, 'ask5': 95.5, 'bid_vol5': 15, 'ask_vol5': 102}
2025-08-25 13:57:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:11'), 'stock_code': '301487', 'price': 22.25, 'volume': 132654, 'amount': 297375520.0, 'open': 22.36, 'high': 22.740000000000002, 'low': 22.12, 'last_close': 22.25, 'cur_vol': 23, 'change': 0.0, 'bid1': 22.240000000000002, 'ask1': 22.25, 'bid_vol1': 30, 'ask_vol1': 26, 'bid2': 22.23, 'ask2': 22.26, 'bid_vol2': 201, 'ask_vol2': 73, 'bid3': 22.22, 'ask3': 22.27, 'bid_vol3': 115, 'ask_vol3': 14, 'bid4': 22.21, 'ask4': 22.28, 'bid_vol4': 90, 'ask_vol4': 9, 'bid5': 22.2, 'ask5': 22.29, 'bid_vol5': 165, 'ask_vol5': 34}
2025-08-25 13:57:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:56:26'), 'stock_code': '301498', 'price': 97.10000000000001, 'volume': 29835, 'amount': 287223360.0, 'open': 95.5, 'high': 97.3, 'low': 94.9, 'last_close': 94.97, 'cur_vol': 1, 'change': -0.009999999999990905, 'bid1': 97.10000000000001, 'ask1': 97.11, 'bid_vol1': 3, 'ask_vol1': 4, 'bid2': 97.09, 'ask2': 97.12, 'bid_vol2': 2, 'ask_vol2': 7, 'bid3': 97.08, 'ask3': 97.13, 'bid_vol3': 6, 'ask_vol3': 12, 'bid4': 97.07000000000001, 'ask4': 97.14, 'bid_vol4': 2, 'ask_vol4': 11, 'bid5': 97.05, 'ask5': 97.15, 'bid_vol5': 5, 'ask_vol5': 10}
2025-08-25 13:57:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2098, 有效=2098, 无效=0
2025-08-25 13:57:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2098 条数据到stock_tick_data表
2025-08-25 13:57:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:27'), 'stock_code': '301486', 'price': 95.4, 'volume': 113819, 'amount': 1052292416.0, 'open': 92.01, 'high': 95.78, 'low': 89.5, 'last_close': 91.8, 'cur_vol': 20, 'change': 0.0, 'bid1': 95.39, 'ask1': 95.4, 'bid_vol1': 14, 'ask_vol1': 1, 'bid2': 95.38, 'ask2': 95.47, 'bid_vol2': 3, 'ask_vol2': 24, 'bid3': 95.36, 'ask3': 95.48, 'bid_vol3': 5, 'ask_vol3': 23, 'bid4': 95.35000000000001, 'ask4': 95.49, 'bid_vol4': 3, 'ask_vol4': 39, 'bid5': 95.34, 'ask5': 95.5, 'bid_vol5': 15, 'ask_vol5': 102}
2025-08-25 13:57:45 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2098 条数据
2025-08-25 13:57:45 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2098/2098 条Tick数据
2025-08-25 13:57:45 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:45 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:45 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:45 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2098 条记录，耗时: 0.35秒
2025-08-25 13:57:45 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:44, 股票数: 4394
2025-08-25 13:57:45 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1148.63 毫秒，每秒处理股票数: 3825.43
2025-08-25 13:57:45 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.15s (网络: 1.15s, 处理: -0.00s, 数据库: 1.15s)
2025-08-25 13:57:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1593
2025-08-25 13:57:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1593 条tick数据
2025-08-25 13:57:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:22'), 'stock_code': '000936', 'price': 7.75, 'volume': 302475, 'amount': 235162368.0, 'open': 7.75, 'high': 7.88, 'low': 7.68, 'last_close': 7.7, 'cur_vol': 1, 'change': 0.0, 'bid1': 7.74, 'ask1': 7.75, 'bid_vol1': 1193, 'ask_vol1': 198, 'bid2': 7.73, 'ask2': 7.76, 'bid_vol2': 496, 'ask_vol2': 1067, 'bid3': 7.72, 'ask3': 7.7700000000000005, 'bid_vol3': 1149, 'ask_vol3': 1764, 'bid4': 7.71, 'ask4': 7.78, 'bid_vol4': 1170, 'ask_vol4': 834, 'bid5': 7.7, 'ask5': 7.79, 'bid_vol5': 3838, 'ask_vol5': 512}
2025-08-25 13:57:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:10'), 'stock_code': '000938', 'price': 28.310000000000002, 'volume': 1486080, 'amount': 4252208384.0, 'open': 29.05, 'high': 29.21, 'low': 28.060000000000002, 'last_close': 28.5, 'cur_vol': 129, 'change': -0.00999999999999801, 'bid1': 28.310000000000002, 'ask1': 28.32, 'bid_vol1': 1, 'ask_vol1': 41, 'bid2': 28.3, 'ask2': 28.330000000000002, 'bid_vol2': 814, 'ask_vol2': 206, 'bid3': 28.29, 'ask3': 28.34, 'bid_vol3': 241, 'ask_vol3': 294, 'bid4': 28.28, 'ask4': 28.35, 'bid_vol4': 201, 'ask_vol4': 557, 'bid5': 28.27, 'ask5': 28.36, 'bid_vol5': 181, 'ask_vol5': 32}
2025-08-25 13:57:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:14'), 'stock_code': '000948', 'price': 20.71, 'volume': 178276, 'amount': 370241312.0, 'open': 20.97, 'high': 21.1, 'low': 20.6, 'last_close': 20.86, 'cur_vol': 1, 'change': 0.010000000000001563, 'bid1': 20.7, 'ask1': 20.71, 'bid_vol1': 411, 'ask_vol1': 56, 'bid2': 20.69, 'ask2': 20.72, 'bid_vol2': 182, 'ask_vol2': 102, 'bid3': 20.68, 'ask3': 20.73, 'bid_vol3': 122, 'ask_vol3': 92, 'bid4': 20.67, 'ask4': 20.740000000000002, 'bid_vol4': 149, 'ask_vol4': 97, 'bid5': 20.66, 'ask5': 20.75, 'bid_vol5': 193, 'ask_vol5': 267}
2025-08-25 13:57:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1593, 有效=1593, 无效=0
2025-08-25 13:57:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1593 条数据到stock_tick_data表
2025-08-25 13:57:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:22'), 'stock_code': '000936', 'price': 7.75, 'volume': 302475, 'amount': 235162368.0, 'open': 7.75, 'high': 7.88, 'low': 7.68, 'last_close': 7.7, 'cur_vol': 1, 'change': 0.0, 'bid1': 7.74, 'ask1': 7.75, 'bid_vol1': 1193, 'ask_vol1': 198, 'bid2': 7.73, 'ask2': 7.76, 'bid_vol2': 496, 'ask_vol2': 1067, 'bid3': 7.72, 'ask3': 7.7700000000000005, 'bid_vol3': 1149, 'ask_vol3': 1764, 'bid4': 7.71, 'ask4': 7.78, 'bid_vol4': 1170, 'ask_vol4': 834, 'bid5': 7.7, 'ask5': 7.79, 'bid_vol5': 3838, 'ask_vol5': 512}
2025-08-25 13:57:47 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1593 条数据
2025-08-25 13:57:47 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1593/1593 条Tick数据
2025-08-25 13:57:47 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:47 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:47 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:47 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1593 条记录，耗时: 0.28秒
2025-08-25 13:57:47 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:46, 股票数: 4394
2025-08-25 13:57:47 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1093.28 毫秒，每秒处理股票数: 4019.09
2025-08-25 13:57:47 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.09s (网络: 1.09s, 处理: -0.00s, 数据库: 1.09s)
2025-08-25 13:57:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1021
2025-08-25 13:57:48 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1021/1021 条Tick数据
2025-08-25 13:57:48 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:48 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:48 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:48 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1021 条记录，耗时: 0.09秒
2025-08-25 13:57:48 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:48, 股票数: 4394
2025-08-25 13:57:48 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 880.92 毫秒，每秒处理股票数: 4987.96
2025-08-25 13:57:48 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.88s (网络: 0.88s, 处理: -0.00s, 数据库: 0.88s)
2025-08-25 13:57:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1989
2025-08-25 13:57:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3010 条tick数据
2025-08-25 13:57:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:03'), 'stock_code': '301099', 'price': 39.9, 'volume': 49576, 'amount': 199989904.0, 'open': 40.68, 'high': 41.11, 'low': 39.7, 'last_close': 40.1, 'cur_vol': 9, 'change': 0.0, 'bid1': 39.89, 'ask1': 39.9, 'bid_vol1': 50, 'ask_vol1': 2, 'bid2': 39.88, 'ask2': 39.93, 'bid_vol2': 13, 'ask_vol2': 1, 'bid3': 39.87, 'ask3': 39.94, 'bid_vol3': 3, 'ask_vol3': 18, 'bid4': 39.86, 'ask4': 39.95, 'bid_vol4': 4, 'ask_vol4': 48, 'bid5': 39.84, 'ask5': 39.97, 'bid_vol5': 2, 'ask_vol5': 1}
2025-08-25 13:57:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:57'), 'stock_code': '301101', 'price': 49.64, 'volume': 81574, 'amount': 404110944.0, 'open': 48.79, 'high': 50.81, 'low': 48.5, 'last_close': 49.0, 'cur_vol': 1, 'change': 0.0, 'bid1': 49.63, 'ask1': 49.64, 'bid_vol1': 10, 'ask_vol1': 3, 'bid2': 49.620000000000005, 'ask2': 49.65, 'bid_vol2': 5, 'ask_vol2': 9, 'bid3': 49.6, 'ask3': 49.660000000000004, 'bid_vol3': 32, 'ask_vol3': 123, 'bid4': 49.57, 'ask4': 49.68, 'bid_vol4': 7, 'ask_vol4': 12, 'bid5': 49.56, 'ask5': 49.71, 'bid_vol5': 23, 'ask_vol5': 221}
2025-08-25 13:57:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:07'), 'stock_code': '301106', 'price': 32.6, 'volume': 15722, 'amount': 51504560.0, 'open': 32.94, 'high': 33.12, 'low': 32.42, 'last_close': 32.730000000000004, 'cur_vol': 8, 'change': 0.0, 'bid1': 32.59, 'ask1': 32.6, 'bid_vol1': 27, 'ask_vol1': 27, 'bid2': 32.58, 'ask2': 32.61, 'bid_vol2': 7, 'ask_vol2': 8, 'bid3': 32.57, 'ask3': 32.63, 'bid_vol3': 8, 'ask_vol3': 5, 'bid4': 32.56, 'ask4': 32.660000000000004, 'bid_vol4': 153, 'ask_vol4': 7, 'bid5': 32.55, 'ask5': 32.67, 'bid_vol5': 1, 'ask_vol5': 62}
2025-08-25 13:57:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3010, 有效=3010, 无效=0
2025-08-25 13:57:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3010 条数据到stock_tick_data表
2025-08-25 13:57:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:03'), 'stock_code': '301099', 'price': 39.9, 'volume': 49576, 'amount': 199989904.0, 'open': 40.68, 'high': 41.11, 'low': 39.7, 'last_close': 40.1, 'cur_vol': 9, 'change': 0.0, 'bid1': 39.89, 'ask1': 39.9, 'bid_vol1': 50, 'ask_vol1': 2, 'bid2': 39.88, 'ask2': 39.93, 'bid_vol2': 13, 'ask_vol2': 1, 'bid3': 39.87, 'ask3': 39.94, 'bid_vol3': 3, 'ask_vol3': 18, 'bid4': 39.86, 'ask4': 39.95, 'bid_vol4': 4, 'ask_vol4': 48, 'bid5': 39.84, 'ask5': 39.97, 'bid_vol5': 2, 'ask_vol5': 1}
2025-08-25 13:57:51 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3010 条数据
2025-08-25 13:57:51 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1989/1989 条Tick数据
2025-08-25 13:57:51 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:51 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:51 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:51 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1989 条记录，耗时: 0.44秒
2025-08-25 13:57:51 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:50, 股票数: 4394
2025-08-25 13:57:51 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1251.88 毫秒，每秒处理股票数: 3509.92
2025-08-25 13:57:51 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.25s (网络: 1.25s, 处理: 0.00s, 数据库: 1.25s)
2025-08-25 13:57:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1469
2025-08-25 13:57:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1469 条tick数据
2025-08-25 13:57:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:19'), 'stock_code': '002871', 'price': 17.84, 'volume': 333747, 'amount': 611298304.0, 'open': 19.5, 'high': 19.580000000000002, 'low': 17.5, 'last_close': 18.43, 'cur_vol': 2, 'change': 0.00999999999999801, 'bid1': 17.830000000000002, 'ask1': 17.84, 'bid_vol1': 19, 'ask_vol1': 30, 'bid2': 17.82, 'ask2': 17.85, 'bid_vol2': 227, 'ask_vol2': 53, 'bid3': 17.81, 'ask3': 17.86, 'bid_vol3': 222, 'ask_vol3': 20, 'bid4': 17.8, 'ask4': 17.87, 'bid_vol4': 185, 'ask_vol4': 19, 'bid5': 17.79, 'ask5': 17.88, 'bid_vol5': 31, 'ask_vol5': 1}
2025-08-25 13:57:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:22'), 'stock_code': '002873', 'price': 13.18, 'volume': 455889, 'amount': 609226752.0, 'open': 12.57, 'high': 13.81, 'low': 12.47, 'last_close': 12.55, 'cur_vol': 68, 'change': 0.0, 'bid1': 13.18, 'ask1': 13.19, 'bid_vol1': 72, 'ask_vol1': 66, 'bid2': 13.17, 'ask2': 13.200000000000001, 'bid_vol2': 128, 'ask_vol2': 16, 'bid3': 13.16, 'ask3': 13.21, 'bid_vol3': 123, 'ask_vol3': 19, 'bid4': 13.15, 'ask4': 13.22, 'bid_vol4': 382, 'ask_vol4': 55, 'bid5': 13.14, 'ask5': 13.23, 'bid_vol5': 125, 'ask_vol5': 125}
2025-08-25 13:57:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:18'), 'stock_code': '002878', 'price': 19.68, 'volume': 189385, 'amount': 372770464.0, 'open': 19.88, 'high': 19.88, 'low': 19.51, 'last_close': 19.580000000000002, 'cur_vol': 51, 'change': 0.00999999999999801, 'bid1': 19.67, 'ask1': 19.68, 'bid_vol1': 5, 'ask_vol1': 61, 'bid2': 19.66, 'ask2': 19.69, 'bid_vol2': 186, 'ask_vol2': 111, 'bid3': 19.650000000000002, 'ask3': 19.7, 'bid_vol3': 750, 'ask_vol3': 60, 'bid4': 19.64, 'ask4': 19.71, 'bid_vol4': 54, 'ask_vol4': 162, 'bid5': 19.63, 'ask5': 19.72, 'bid_vol5': 24, 'ask_vol5': 102}
2025-08-25 13:57:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1469, 有效=1469, 无效=0
2025-08-25 13:57:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1469 条数据到stock_tick_data表
2025-08-25 13:57:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:19'), 'stock_code': '002871', 'price': 17.84, 'volume': 333747, 'amount': 611298304.0, 'open': 19.5, 'high': 19.580000000000002, 'low': 17.5, 'last_close': 18.43, 'cur_vol': 2, 'change': 0.00999999999999801, 'bid1': 17.830000000000002, 'ask1': 17.84, 'bid_vol1': 19, 'ask_vol1': 30, 'bid2': 17.82, 'ask2': 17.85, 'bid_vol2': 227, 'ask_vol2': 53, 'bid3': 17.81, 'ask3': 17.86, 'bid_vol3': 222, 'ask_vol3': 20, 'bid4': 17.8, 'ask4': 17.87, 'bid_vol4': 185, 'ask_vol4': 19, 'bid5': 17.79, 'ask5': 17.88, 'bid_vol5': 31, 'ask_vol5': 1}
2025-08-25 13:57:53 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1469 条数据
2025-08-25 13:57:53 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1469/1469 条Tick数据
2025-08-25 13:57:53 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:53 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:53 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:53 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1469 条记录，耗时: 0.27秒
2025-08-25 13:57:53 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:52, 股票数: 4394
2025-08-25 13:57:53 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1086.91 毫秒，每秒处理股票数: 4042.66
2025-08-25 13:57:53 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.09s (网络: 1.09s, 处理: -0.00s, 数据库: 1.09s)
2025-08-25 13:57:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1137
2025-08-25 13:57:54 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1137/1137 条Tick数据
2025-08-25 13:57:54 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:54 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:54 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:54 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1137 条记录，耗时: 0.09秒
2025-08-25 13:57:54 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:54, 股票数: 4394
2025-08-25 13:57:54 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 845.82 毫秒，每秒处理股票数: 5194.93
2025-08-25 13:57:54 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.85s (网络: 0.85s, 处理: -0.00s, 数据库: 0.85s)
2025-08-25 13:57:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2067
2025-08-25 13:57:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3204 条tick数据
2025-08-25 13:57:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:56:36'), 'stock_code': '300620', 'price': 90.95, 'volume': 269299, 'amount': 2489574400.0, 'open': 91.66, 'high': 95.68, 'low': 90.17, 'last_close': 88.82000000000001, 'cur_vol': 21, 'change': 0.0, 'bid1': 90.93, 'ask1': 90.95, 'bid_vol1': 17, 'ask_vol1': 16, 'bid2': 90.92, 'ask2': 90.97, 'bid_vol2': 5, 'ask_vol2': 9, 'bid3': 90.91, 'ask3': 90.98, 'bid_vol3': 9, 'ask_vol3': 17, 'bid4': 90.9, 'ask4': 90.99, 'bid_vol4': 91, 'ask_vol4': 13, 'bid5': 90.89, 'ask5': 91.0, 'bid_vol5': 6, 'ask_vol5': 12}
2025-08-25 13:57:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:24'), 'stock_code': '300621', 'price': 9.81, 'volume': 122904, 'amount': 121978192.0, 'open': 9.68, 'high': 10.13, 'low': 9.65, 'last_close': 9.53, 'cur_vol': 56, 'change': -0.009999999999999787, 'bid1': 9.81, 'ask1': 9.82, 'bid_vol1': 182, 'ask_vol1': 207, 'bid2': 9.8, 'ask2': 9.83, 'bid_vol2': 288, 'ask_vol2': 149, 'bid3': 9.790000000000001, 'ask3': 9.84, 'bid_vol3': 60, 'ask_vol3': 318, 'bid4': 9.78, 'ask4': 9.85, 'bid_vol4': 335, 'ask_vol4': 71, 'bid5': 9.77, 'ask5': 9.86, 'bid_vol5': 26, 'ask_vol5': 60}
2025-08-25 13:57:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:10'), 'stock_code': '300623', 'price': 33.83, 'volume': 377143, 'amount': 1283438080.0, 'open': 34.1, 'high': 34.59, 'low': 33.51, 'last_close': 33.68, 'cur_vol': 17, 'change': 0.01999999999999602, 'bid1': 33.83, 'ask1': 33.84, 'bid_vol1': 159, 'ask_vol1': 60, 'bid2': 33.81, 'ask2': 33.85, 'bid_vol2': 69, 'ask_vol2': 24, 'bid3': 33.8, 'ask3': 33.86, 'bid_vol3': 438, 'ask_vol3': 25, 'bid4': 33.79, 'ask4': 33.87, 'bid_vol4': 115, 'ask_vol4': 26, 'bid5': 33.78, 'ask5': 33.88, 'bid_vol5': 102, 'ask_vol5': 83}
2025-08-25 13:57:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3204, 有效=3204, 无效=0
2025-08-25 13:57:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3204 条数据到stock_tick_data表
2025-08-25 13:57:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:56:36'), 'stock_code': '300620', 'price': 90.95, 'volume': 269299, 'amount': 2489574400.0, 'open': 91.66, 'high': 95.68, 'low': 90.17, 'last_close': 88.82000000000001, 'cur_vol': 21, 'change': 0.0, 'bid1': 90.93, 'ask1': 90.95, 'bid_vol1': 17, 'ask_vol1': 16, 'bid2': 90.92, 'ask2': 90.97, 'bid_vol2': 5, 'ask_vol2': 9, 'bid3': 90.91, 'ask3': 90.98, 'bid_vol3': 9, 'ask_vol3': 17, 'bid4': 90.9, 'ask4': 90.99, 'bid_vol4': 91, 'ask_vol4': 13, 'bid5': 90.89, 'ask5': 91.0, 'bid_vol5': 6, 'ask_vol5': 12}
2025-08-25 13:57:57 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3204 条数据
2025-08-25 13:57:57 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2067/2067 条Tick数据
2025-08-25 13:57:57 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:57 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:57 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:57 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2067 条记录，耗时: 0.49秒
2025-08-25 13:57:57 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:56, 股票数: 4394
2025-08-25 13:57:57 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1295.85 毫秒，每秒处理股票数: 3390.82
2025-08-25 13:57:57 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.30s (网络: 1.30s, 处理: -0.00s, 数据库: 1.30s)
2025-08-25 13:57:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:57:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1283
2025-08-25 13:57:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1283 条tick数据
2025-08-25 13:57:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:11'), 'stock_code': '301658', 'price': 34.87, 'volume': 38808, 'amount': 135911792.0, 'open': 35.1, 'high': 35.410000000000004, 'low': 34.72, 'last_close': 35.01, 'cur_vol': 17, 'change': -0.010000000000005116, 'bid1': 34.87, 'ask1': 34.88, 'bid_vol1': 22, 'ask_vol1': 10, 'bid2': 34.86, 'ask2': 34.9, 'bid_vol2': 501, 'ask_vol2': 7, 'bid3': 34.85, 'ask3': 34.910000000000004, 'bid_vol3': 74, 'ask_vol3': 111, 'bid4': 34.84, 'ask4': 34.92, 'bid_vol4': 5, 'ask_vol4': 15, 'bid5': 34.83, 'ask5': 34.94, 'bid_vol5': 2, 'ask_vol5': 13}
2025-08-25 13:57:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:27'), 'stock_code': '002286', 'price': 10.75, 'volume': 94599, 'amount': 101926856.0, 'open': 10.77, 'high': 10.84, 'low': 10.72, 'last_close': 10.77, 'cur_vol': 6, 'change': 0.0, 'bid1': 10.74, 'ask1': 10.75, 'bid_vol1': 750, 'ask_vol1': 206, 'bid2': 10.73, 'ask2': 10.76, 'bid_vol2': 2421, 'ask_vol2': 220, 'bid3': 10.72, 'ask3': 10.77, 'bid_vol3': 1395, 'ask_vol3': 202, 'bid4': 10.71, 'ask4': 10.78, 'bid_vol4': 794, 'ask_vol4': 610, 'bid5': 10.700000000000001, 'ask5': 10.790000000000001, 'bid_vol5': 1823, 'ask_vol5': 48}
2025-08-25 13:57:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:16'), 'stock_code': '002287', 'price': 29.11, 'volume': 121397, 'amount': 350247008.0, 'open': 28.7, 'high': 29.19, 'low': 28.400000000000002, 'last_close': 28.51, 'cur_vol': 17, 'change': 0.00999999999999801, 'bid1': 29.080000000000002, 'ask1': 29.11, 'bid_vol1': 12, 'ask_vol1': 65, 'bid2': 29.07, 'ask2': 29.12, 'bid_vol2': 15, 'ask_vol2': 34, 'bid3': 29.060000000000002, 'ask3': 29.13, 'bid_vol3': 4, 'ask_vol3': 135, 'bid4': 29.05, 'ask4': 29.14, 'bid_vol4': 19, 'ask_vol4': 96, 'bid5': 29.03, 'ask5': 29.150000000000002, 'bid_vol5': 4, 'ask_vol5': 58}
2025-08-25 13:57:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1283, 有效=1283, 无效=0
2025-08-25 13:57:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1283 条数据到stock_tick_data表
2025-08-25 13:57:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:11'), 'stock_code': '301658', 'price': 34.87, 'volume': 38808, 'amount': 135911792.0, 'open': 35.1, 'high': 35.410000000000004, 'low': 34.72, 'last_close': 35.01, 'cur_vol': 17, 'change': -0.010000000000005116, 'bid1': 34.87, 'ask1': 34.88, 'bid_vol1': 22, 'ask_vol1': 10, 'bid2': 34.86, 'ask2': 34.9, 'bid_vol2': 501, 'ask_vol2': 7, 'bid3': 34.85, 'ask3': 34.910000000000004, 'bid_vol3': 74, 'ask_vol3': 111, 'bid4': 34.84, 'ask4': 34.92, 'bid_vol4': 5, 'ask_vol4': 15, 'bid5': 34.83, 'ask5': 34.94, 'bid_vol5': 2, 'ask_vol5': 13}
2025-08-25 13:57:59 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1283 条数据
2025-08-25 13:57:59 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1283/1283 条Tick数据
2025-08-25 13:57:59 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:57:59 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:57:59 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:57:59 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1283 条记录，耗时: 0.25秒
2025-08-25 13:57:59 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:57:58, 股票数: 4394
2025-08-25 13:57:59 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1067.44 毫秒，每秒处理股票数: 4116.38
2025-08-25 13:57:59 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.07s (网络: 1.07s, 处理: -0.00s, 数据库: 1.07s)
2025-08-25 13:58:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:00 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1180
2025-08-25 13:58:00 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1180/1180 条Tick数据
2025-08-25 13:58:00 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:00 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:00 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:00 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1180 条记录，耗时: 0.12秒
2025-08-25 13:58:00 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:00, 股票数: 4394
2025-08-25 13:58:00 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 701.70 毫秒，每秒处理股票数: 6261.97
2025-08-25 13:58:00 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.70s (网络: 0.70s, 处理: -0.00s, 数据库: 0.70s)
2025-08-25 13:58:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2133
2025-08-25 13:58:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3313 条tick数据
2025-08-25 13:58:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:13'), 'stock_code': '300939', 'price': 34.42, 'volume': 28992, 'amount': 100216712.0, 'open': 34.86, 'high': 34.95, 'low': 34.26, 'last_close': 34.65, 'cur_vol': 8, 'change': 0.00999999999999801, 'bid1': 34.410000000000004, 'ask1': 34.42, 'bid_vol1': 3, 'ask_vol1': 6, 'bid2': 34.4, 'ask2': 34.43, 'bid_vol2': 24, 'ask_vol2': 8, 'bid3': 34.39, 'ask3': 34.44, 'bid_vol3': 27, 'ask_vol3': 11, 'bid4': 34.38, 'ask4': 34.45, 'bid_vol4': 43, 'ask_vol4': 28, 'bid5': 34.37, 'ask5': 34.46, 'bid_vol5': 4, 'ask_vol5': 5}
2025-08-25 13:58:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:16'), 'stock_code': '300941', 'price': 28.69, 'volume': 74573, 'amount': 215430592.0, 'open': 29.080000000000002, 'high': 29.25, 'low': 28.53, 'last_close': 28.82, 'cur_vol': 1, 'change': 0.03999999999999915, 'bid1': 28.650000000000002, 'ask1': 28.69, 'bid_vol1': 2, 'ask_vol1': 7, 'bid2': 28.64, 'ask2': 28.7, 'bid_vol2': 30, 'ask_vol2': 32, 'bid3': 28.63, 'ask3': 28.71, 'bid_vol3': 8, 'ask_vol3': 11, 'bid4': 28.62, 'ask4': 28.72, 'bid_vol4': 171, 'ask_vol4': 12, 'bid5': 28.61, 'ask5': 28.73, 'bid_vol5': 121, 'ask_vol5': 44}
2025-08-25 13:58:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:23'), 'stock_code': '300943', 'price': 18.59, 'volume': 110015, 'amount': 202504896.0, 'open': 18.29, 'high': 18.79, 'low': 18.0, 'last_close': 18.39, 'cur_vol': 17, 'change': 0.0, 'bid1': 18.59, 'ask1': 18.6, 'bid_vol1': 37, 'ask_vol1': 57, 'bid2': 18.55, 'ask2': 18.61, 'bid_vol2': 36, 'ask_vol2': 109, 'bid3': 18.54, 'ask3': 18.62, 'bid_vol3': 387, 'ask_vol3': 337, 'bid4': 18.53, 'ask4': 18.63, 'bid_vol4': 77, 'ask_vol4': 159, 'bid5': 18.52, 'ask5': 18.64, 'bid_vol5': 10, 'ask_vol5': 242}
2025-08-25 13:58:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3313, 有效=3313, 无效=0
2025-08-25 13:58:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3313 条数据到stock_tick_data表
2025-08-25 13:58:02 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:13'), 'stock_code': '300939', 'price': 34.42, 'volume': 28992, 'amount': 100216712.0, 'open': 34.86, 'high': 34.95, 'low': 34.26, 'last_close': 34.65, 'cur_vol': 8, 'change': 0.00999999999999801, 'bid1': 34.410000000000004, 'ask1': 34.42, 'bid_vol1': 3, 'ask_vol1': 6, 'bid2': 34.4, 'ask2': 34.43, 'bid_vol2': 24, 'ask_vol2': 8, 'bid3': 34.39, 'ask3': 34.44, 'bid_vol3': 27, 'ask_vol3': 11, 'bid4': 34.38, 'ask4': 34.45, 'bid_vol4': 43, 'ask_vol4': 28, 'bid5': 34.37, 'ask5': 34.46, 'bid_vol5': 4, 'ask_vol5': 5}
2025-08-25 13:58:03 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3313 条数据
2025-08-25 13:58:03 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2133/2133 条Tick数据
2025-08-25 13:58:03 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:03 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:03 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:03 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2133 条记录，耗时: 0.50秒
2025-08-25 13:58:03 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:02, 股票数: 4394
2025-08-25 13:58:03 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1306.42 毫秒，每秒处理股票数: 3363.38
2025-08-25 13:58:03 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.31s (网络: 1.31s, 处理: -0.00s, 数据库: 1.31s)
2025-08-25 13:58:04 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:04 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1603
2025-08-25 13:58:04 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1603/1603 条Tick数据
2025-08-25 13:58:04 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:04 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:04 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:04 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1603 条记录，耗时: 0.10秒
2025-08-25 13:58:04 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:04, 股票数: 4394
2025-08-25 13:58:04 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 901.89 毫秒，每秒处理股票数: 4871.99
2025-08-25 13:58:04 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.90s (网络: 0.90s, 处理: -0.00s, 数据库: 0.90s)
2025-08-25 13:58:06 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:06 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1321
2025-08-25 13:58:06 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2924 条tick数据
2025-08-25 13:58:06 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:50'), 'stock_code': '002925', 'price': 18.46, 'volume': 85095, 'amount': 158152928.0, 'open': 18.68, 'high': 18.8, 'low': 18.39, 'last_close': 18.56, 'cur_vol': 14, 'change': 0.0, 'bid1': 18.45, 'ask1': 18.46, 'bid_vol1': 276, 'ask_vol1': 231, 'bid2': 18.44, 'ask2': 18.47, 'bid_vol2': 107, 'ask_vol2': 61, 'bid3': 18.43, 'ask3': 18.48, 'bid_vol3': 54, 'ask_vol3': 152, 'bid4': 18.42, 'ask4': 18.490000000000002, 'bid_vol4': 85, 'ask_vol4': 14, 'bid5': 18.41, 'ask5': 18.5, 'bid_vol5': 204, 'ask_vol5': 82}
2025-08-25 13:58:06 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:55'), 'stock_code': '002926', 'price': 10.73, 'volume': 668693, 'amount': 721203904.0, 'open': 10.9, 'high': 10.96, 'low': 10.66, 'last_close': 10.82, 'cur_vol': 134, 'change': 0.0, 'bid1': 10.72, 'ask1': 10.73, 'bid_vol1': 200, 'ask_vol1': 234, 'bid2': 10.71, 'ask2': 10.74, 'bid_vol2': 1324, 'ask_vol2': 515, 'bid3': 10.700000000000001, 'ask3': 10.75, 'bid_vol3': 2033, 'ask_vol3': 2257, 'bid4': 10.69, 'ask4': 10.76, 'bid_vol4': 587, 'ask_vol4': 148, 'bid5': 10.68, 'ask5': 10.77, 'bid_vol5': 1589, 'ask_vol5': 559}
2025-08-25 13:58:06 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:30'), 'stock_code': '002929', 'price': 51.72, 'volume': 207495, 'amount': 1066358592.0, 'open': 50.56, 'high': 52.76, 'low': 49.6, 'last_close': 49.980000000000004, 'cur_vol': 10, 'change': 0.0, 'bid1': 51.71, 'ask1': 51.72, 'bid_vol1': 5, 'ask_vol1': 33, 'bid2': 51.7, 'ask2': 51.730000000000004, 'bid_vol2': 12, 'ask_vol2': 37, 'bid3': 51.68, 'ask3': 51.75, 'bid_vol3': 33, 'ask_vol3': 6, 'bid4': 51.65, 'ask4': 51.76, 'bid_vol4': 1, 'ask_vol4': 57, 'bid5': 51.63, 'ask5': 51.77, 'bid_vol5': 10, 'ask_vol5': 4}
2025-08-25 13:58:06 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2924, 有效=2924, 无效=0
2025-08-25 13:58:06 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2924 条数据到stock_tick_data表
2025-08-25 13:58:06 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:50'), 'stock_code': '002925', 'price': 18.46, 'volume': 85095, 'amount': 158152928.0, 'open': 18.68, 'high': 18.8, 'low': 18.39, 'last_close': 18.56, 'cur_vol': 14, 'change': 0.0, 'bid1': 18.45, 'ask1': 18.46, 'bid_vol1': 276, 'ask_vol1': 231, 'bid2': 18.44, 'ask2': 18.47, 'bid_vol2': 107, 'ask_vol2': 61, 'bid3': 18.43, 'ask3': 18.48, 'bid_vol3': 54, 'ask_vol3': 152, 'bid4': 18.42, 'ask4': 18.490000000000002, 'bid_vol4': 85, 'ask_vol4': 14, 'bid5': 18.41, 'ask5': 18.5, 'bid_vol5': 204, 'ask_vol5': 82}
2025-08-25 13:58:07 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2924 条数据
2025-08-25 13:58:07 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1321/1321 条Tick数据
2025-08-25 13:58:07 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:07 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:07 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:07 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1321 条记录，耗时: 0.46秒
2025-08-25 13:58:07 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:06, 股票数: 4394
2025-08-25 13:58:07 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1277.09 毫秒，每秒处理股票数: 3440.64
2025-08-25 13:58:07 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.28s (网络: 1.28s, 处理: 0.00s, 数据库: 1.28s)
2025-08-25 13:58:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2170
2025-08-25 13:58:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2170 条tick数据
2025-08-25 13:58:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:57'), 'stock_code': '002632', 'price': 9.94, 'volume': 165257, 'amount': 165236224.0, 'open': 9.96, 'high': 10.15, 'low': 9.89, 'last_close': 9.89, 'cur_vol': 5, 'change': 0.0, 'bid1': 9.93, 'ask1': 9.94, 'bid_vol1': 317, 'ask_vol1': 372, 'bid2': 9.92, 'ask2': 9.950000000000001, 'bid_vol2': 630, 'ask_vol2': 590, 'bid3': 9.91, 'ask3': 9.96, 'bid_vol3': 400, 'ask_vol3': 136, 'bid4': 9.9, 'ask4': 9.97, 'bid_vol4': 1436, 'ask_vol4': 211, 'bid5': 9.89, 'ask5': 9.98, 'bid_vol5': 615, 'ask_vol5': 379}
2025-08-25 13:58:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:54'), 'stock_code': '002635', 'price': 15.3, 'volume': 272029, 'amount': 416315296.0, 'open': 15.21, 'high': 15.68, 'low': 15.05, 'last_close': 15.18, 'cur_vol': 28, 'change': 0.0, 'bid1': 15.3, 'ask1': 15.31, 'bid_vol1': 808, 'ask_vol1': 301, 'bid2': 15.290000000000001, 'ask2': 15.32, 'bid_vol2': 276, 'ask_vol2': 251, 'bid3': 15.280000000000001, 'ask3': 15.33, 'bid_vol3': 327, 'ask_vol3': 401, 'bid4': 15.27, 'ask4': 15.34, 'bid_vol4': 62, 'ask_vol4': 128, 'bid5': 15.26, 'ask5': 15.35, 'bid_vol5': 189, 'ask_vol5': 341}
2025-08-25 13:58:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:55'), 'stock_code': '002636', 'price': 13.41, 'volume': 360635, 'amount': 486684800.0, 'open': 13.6, 'high': 13.68, 'low': 13.35, 'last_close': 13.4, 'cur_vol': 26, 'change': 0.0, 'bid1': 13.4, 'ask1': 13.41, 'bid_vol1': 497, 'ask_vol1': 185, 'bid2': 13.39, 'ask2': 13.42, 'bid_vol2': 317, 'ask_vol2': 597, 'bid3': 13.38, 'ask3': 13.43, 'bid_vol3': 418, 'ask_vol3': 279, 'bid4': 13.370000000000001, 'ask4': 13.44, 'bid_vol4': 579, 'ask_vol4': 255, 'bid5': 13.36, 'ask5': 13.450000000000001, 'bid_vol5': 1124, 'ask_vol5': 637}
2025-08-25 13:58:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2170, 有效=2170, 无效=0
2025-08-25 13:58:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2170 条数据到stock_tick_data表
2025-08-25 13:58:08 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:57'), 'stock_code': '002632', 'price': 9.94, 'volume': 165257, 'amount': 165236224.0, 'open': 9.96, 'high': 10.15, 'low': 9.89, 'last_close': 9.89, 'cur_vol': 5, 'change': 0.0, 'bid1': 9.93, 'ask1': 9.94, 'bid_vol1': 317, 'ask_vol1': 372, 'bid2': 9.92, 'ask2': 9.950000000000001, 'bid_vol2': 630, 'ask_vol2': 590, 'bid3': 9.91, 'ask3': 9.96, 'bid_vol3': 400, 'ask_vol3': 136, 'bid4': 9.9, 'ask4': 9.97, 'bid_vol4': 1436, 'ask_vol4': 211, 'bid5': 9.89, 'ask5': 9.98, 'bid_vol5': 615, 'ask_vol5': 379}
2025-08-25 13:58:09 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2170 条数据
2025-08-25 13:58:09 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2170/2170 条Tick数据
2025-08-25 13:58:09 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:09 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:09 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:09 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2170 条记录，耗时: 0.35秒
2025-08-25 13:58:09 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:08, 股票数: 4394
2025-08-25 13:58:09 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1183.71 毫秒，每秒处理股票数: 3712.06
2025-08-25 13:58:09 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.18s (网络: 1.18s, 处理: -0.00s, 数据库: 1.18s)
2025-08-25 13:58:10 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:10 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1407
2025-08-25 13:58:10 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1407/1407 条Tick数据
2025-08-25 13:58:10 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:10 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:10 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:10 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1407 条记录，耗时: 0.09秒
2025-08-25 13:58:10 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:10, 股票数: 4394
2025-08-25 13:58:10 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 922.87 毫秒，每秒处理股票数: 4761.22
2025-08-25 13:58:10 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.92s (网络: 0.92s, 处理: 0.00s, 数据库: 0.92s)
2025-08-25 13:58:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1320
2025-08-25 13:58:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2727 条tick数据
2025-08-25 13:58:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:02'), 'stock_code': '002175', 'price': 4.79, 'volume': 573429, 'amount': 276183584.0, 'open': 4.83, 'high': 4.87, 'low': 4.75, 'last_close': 4.82, 'cur_vol': 28, 'change': 0.0, 'bid1': 4.78, 'ask1': 4.79, 'bid_vol1': 1971, 'ask_vol1': 1666, 'bid2': 4.7700000000000005, 'ask2': 4.8, 'bid_vol2': 4211, 'ask_vol2': 4495, 'bid3': 4.76, 'ask3': 4.8100000000000005, 'bid_vol3': 6839, 'ask_vol3': 4367, 'bid4': 4.75, 'ask4': 4.82, 'bid_vol4': 28125, 'ask_vol4': 3599, 'bid5': 4.74, 'ask5': 4.83, 'bid_vol5': 6941, 'ask_vol5': 4598}
2025-08-25 13:58:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:59'), 'stock_code': '002177', 'price': 9.52, 'volume': 3435906, 'amount': 3385986304.0, 'open': 10.120000000000001, 'high': 10.4, 'low': 9.4, 'last_close': 10.0, 'cur_vol': 84, 'change': 0.009999999999999787, 'bid1': 9.51, 'ask1': 9.52, 'bid_vol1': 592, 'ask_vol1': 286, 'bid2': 9.5, 'ask2': 9.53, 'bid_vol2': 2741, 'ask_vol2': 1062, 'bid3': 9.49, 'ask3': 9.540000000000001, 'bid_vol3': 1343, 'ask_vol3': 399, 'bid4': 9.48, 'ask4': 9.55, 'bid_vol4': 1739, 'ask_vol4': 1537, 'bid5': 9.47, 'ask5': 9.56, 'bid_vol5': 954, 'ask_vol5': 204}
2025-08-25 13:58:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:00'), 'stock_code': '002178', 'price': 7.51, 'volume': 633009, 'amount': 478957248.0, 'open': 7.67, 'high': 7.73, 'low': 7.47, 'last_close': 7.59, 'cur_vol': 8, 'change': 0.0, 'bid1': 7.51, 'ask1': 7.5200000000000005, 'bid_vol1': 2, 'ask_vol1': 1241, 'bid2': 7.5, 'ask2': 7.53, 'bid_vol2': 3160, 'ask_vol2': 716, 'bid3': 7.49, 'ask3': 7.54, 'bid_vol3': 1313, 'ask_vol3': 1185, 'bid4': 7.48, 'ask4': 7.55, 'bid_vol4': 3559, 'ask_vol4': 1259, 'bid5': 7.47, 'ask5': 7.5600000000000005, 'bid_vol5': 4694, 'ask_vol5': 625}
2025-08-25 13:58:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2727, 有效=2727, 无效=0
2025-08-25 13:58:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2727 条数据到stock_tick_data表
2025-08-25 13:58:12 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:02'), 'stock_code': '002175', 'price': 4.79, 'volume': 573429, 'amount': 276183584.0, 'open': 4.83, 'high': 4.87, 'low': 4.75, 'last_close': 4.82, 'cur_vol': 28, 'change': 0.0, 'bid1': 4.78, 'ask1': 4.79, 'bid_vol1': 1971, 'ask_vol1': 1666, 'bid2': 4.7700000000000005, 'ask2': 4.8, 'bid_vol2': 4211, 'ask_vol2': 4495, 'bid3': 4.76, 'ask3': 4.8100000000000005, 'bid_vol3': 6839, 'ask_vol3': 4367, 'bid4': 4.75, 'ask4': 4.82, 'bid_vol4': 28125, 'ask_vol4': 3599, 'bid5': 4.74, 'ask5': 4.83, 'bid_vol5': 6941, 'ask_vol5': 4598}
2025-08-25 13:58:13 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2727 条数据
2025-08-25 13:58:13 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1320/1320 条Tick数据
2025-08-25 13:58:13 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:13 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:13 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:13 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1320 条记录，耗时: 0.40秒
2025-08-25 13:58:13 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:12, 股票数: 4394
2025-08-25 13:58:13 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1216.15 毫秒，每秒处理股票数: 3613.04
2025-08-25 13:58:13 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.22s (网络: 1.22s, 处理: 0.00s, 数据库: 1.22s)
2025-08-25 13:58:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:14 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2145
2025-08-25 13:58:14 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2145/2145 条Tick数据
2025-08-25 13:58:14 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:14 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:14 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:14 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2145 条记录，耗时: 0.10秒
2025-08-25 13:58:14 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:14, 股票数: 4394
2025-08-25 13:58:14 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 892.04 毫秒，每秒处理股票数: 4925.80
2025-08-25 13:58:14 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.89s (网络: 0.89s, 处理: -0.00s, 数据库: 0.89s)
2025-08-25 13:58:16 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:16 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1437
2025-08-25 13:58:16 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3582 条tick数据
2025-08-25 13:58:16 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:00'), 'stock_code': '002065', 'price': 10.76, 'volume': 2423323, 'amount': 2655967232.0, 'open': 10.92, 'high': 11.32, 'low': 10.65, 'last_close': 10.55, 'cur_vol': 35, 'change': -0.009999999999999787, 'bid1': 10.76, 'ask1': 10.77, 'bid_vol1': 1313, 'ask_vol1': 223, 'bid2': 10.75, 'ask2': 10.78, 'bid_vol2': 1806, 'ask_vol2': 2197, 'bid3': 10.74, 'ask3': 10.790000000000001, 'bid_vol3': 1438, 'ask_vol3': 1696, 'bid4': 10.73, 'ask4': 10.8, 'bid_vol4': 613, 'ask_vol4': 4114, 'bid5': 10.72, 'ask5': 10.81, 'bid_vol5': 618, 'ask_vol5': 1388}
2025-08-25 13:58:16 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:58'), 'stock_code': '002066', 'price': 13.72, 'volume': 77106, 'amount': 105943328.0, 'open': 14.030000000000001, 'high': 14.05, 'low': 13.57, 'last_close': 13.93, 'cur_vol': 48, 'change': 0.009999999999999787, 'bid1': 13.72, 'ask1': 13.73, 'bid_vol1': 410, 'ask_vol1': 55, 'bid2': 13.71, 'ask2': 13.74, 'bid_vol2': 3, 'ask_vol2': 78, 'bid3': 13.700000000000001, 'ask3': 13.75, 'bid_vol3': 106, 'ask_vol3': 270, 'bid4': 13.69, 'ask4': 13.76, 'bid_vol4': 5, 'ask_vol4': 20, 'bid5': 13.68, 'ask5': 13.77, 'bid_vol5': 12, 'ask_vol5': 12}
2025-08-25 13:58:16 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:04'), 'stock_code': '002069', 'price': 4.36, 'volume': 114144, 'amount': 49558672.0, 'open': 4.39, 'high': 4.4, 'low': 4.32, 'last_close': 4.38, 'cur_vol': 44, 'change': 0.0, 'bid1': 4.3500000000000005, 'ask1': 4.36, 'bid_vol1': 2463, 'ask_vol1': 635, 'bid2': 4.34, 'ask2': 4.37, 'bid_vol2': 1698, 'ask_vol2': 505, 'bid3': 4.33, 'ask3': 4.38, 'bid_vol3': 2093, 'ask_vol3': 2431, 'bid4': 4.32, 'ask4': 4.39, 'bid_vol4': 2640, 'ask_vol4': 247, 'bid5': 4.3100000000000005, 'ask5': 4.4, 'bid_vol5': 1219, 'ask_vol5': 928}
2025-08-25 13:58:17 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3582, 有效=3582, 无效=0
2025-08-25 13:58:17 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3582 条数据到stock_tick_data表
2025-08-25 13:58:17 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:00'), 'stock_code': '002065', 'price': 10.76, 'volume': 2423323, 'amount': 2655967232.0, 'open': 10.92, 'high': 11.32, 'low': 10.65, 'last_close': 10.55, 'cur_vol': 35, 'change': -0.009999999999999787, 'bid1': 10.76, 'ask1': 10.77, 'bid_vol1': 1313, 'ask_vol1': 223, 'bid2': 10.75, 'ask2': 10.78, 'bid_vol2': 1806, 'ask_vol2': 2197, 'bid3': 10.74, 'ask3': 10.790000000000001, 'bid_vol3': 1438, 'ask_vol3': 1696, 'bid4': 10.73, 'ask4': 10.8, 'bid_vol4': 613, 'ask_vol4': 4114, 'bid5': 10.72, 'ask5': 10.81, 'bid_vol5': 618, 'ask_vol5': 1388}
2025-08-25 13:58:17 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3582 条数据
2025-08-25 13:58:17 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1437/1437 条Tick数据
2025-08-25 13:58:17 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:17 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:17 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:17 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1437 条记录，耗时: 0.52秒
2025-08-25 13:58:17 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:16, 股票数: 4394
2025-08-25 13:58:17 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1346.11 毫秒，每秒处理股票数: 3264.21
2025-08-25 13:58:17 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.35s (网络: 1.35s, 处理: 0.00s, 数据库: 1.35s)
2025-08-25 13:58:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:18 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1426
2025-08-25 13:58:18 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1426/1426 条Tick数据
2025-08-25 13:58:18 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:18 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:18 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:18 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1426 条记录，耗时: 0.09秒
2025-08-25 13:58:18 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:18, 股票数: 4394
2025-08-25 13:58:18 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 886.61 毫秒，每秒处理股票数: 4955.94
2025-08-25 13:58:18 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.89s (网络: 0.89s, 处理: -0.00s, 数据库: 0.89s)
2025-08-25 13:58:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2462
2025-08-25 13:58:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3888 条tick数据
2025-08-25 13:58:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:56'), 'stock_code': '301429', 'price': 20.23, 'volume': 12392, 'amount': 25266926.0, 'open': 20.330000000000002, 'high': 20.62, 'low': 20.19, 'last_close': 20.23, 'cur_vol': 1, 'change': -0.010000000000001563, 'bid1': 20.23, 'ask1': 20.240000000000002, 'bid_vol1': 27, 'ask_vol1': 15, 'bid2': 20.22, 'ask2': 20.25, 'bid_vol2': 27, 'ask_vol2': 45, 'bid3': 20.21, 'ask3': 20.26, 'bid_vol3': 44, 'ask_vol3': 30, 'bid4': 20.2, 'ask4': 20.27, 'bid_vol4': 120, 'ask_vol4': 3, 'bid5': 20.19, 'ask5': 20.29, 'bid_vol5': 132, 'ask_vol5': 40}
2025-08-25 13:58:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:58'), 'stock_code': '301439', 'price': 16.91, 'volume': 65190, 'amount': 110927584.0, 'open': 17.02, 'high': 17.22, 'low': 16.82, 'last_close': 16.98, 'cur_vol': 2, 'change': 0.0, 'bid1': 16.91, 'ask1': 16.92, 'bid_vol1': 4, 'ask_vol1': 1, 'bid2': 16.9, 'ask2': 16.93, 'bid_vol2': 33, 'ask_vol2': 2, 'bid3': 16.89, 'ask3': 16.94, 'bid_vol3': 110, 'ask_vol3': 139, 'bid4': 16.88, 'ask4': 16.95, 'bid_vol4': 210, 'ask_vol4': 43, 'bid5': 16.87, 'ask5': 16.96, 'bid_vol5': 157, 'ask_vol5': 9}
2025-08-25 13:58:20 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:11'), 'stock_code': '301486', 'price': 95.46000000000001, 'volume': 113946, 'amount': 1053504640.0, 'open': 92.01, 'high': 95.78, 'low': 89.5, 'last_close': 91.8, 'cur_vol': 31, 'change': 0.0, 'bid1': 95.4, 'ask1': 95.46000000000001, 'bid_vol1': 36, 'ask_vol1': 7, 'bid2': 95.39, 'ask2': 95.47, 'bid_vol2': 16, 'ask_vol2': 3, 'bid3': 95.36, 'ask3': 95.48, 'bid_vol3': 1, 'ask_vol3': 13, 'bid4': 95.35000000000001, 'ask4': 95.49, 'bid_vol4': 3, 'ask_vol4': 40, 'bid5': 95.22, 'ask5': 95.5, 'bid_vol5': 13, 'ask_vol5': 100}
2025-08-25 13:58:21 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3888, 有效=3888, 无效=0
2025-08-25 13:58:21 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3888 条数据到stock_tick_data表
2025-08-25 13:58:21 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:56'), 'stock_code': '301429', 'price': 20.23, 'volume': 12392, 'amount': 25266926.0, 'open': 20.330000000000002, 'high': 20.62, 'low': 20.19, 'last_close': 20.23, 'cur_vol': 1, 'change': -0.010000000000001563, 'bid1': 20.23, 'ask1': 20.240000000000002, 'bid_vol1': 27, 'ask_vol1': 15, 'bid2': 20.22, 'ask2': 20.25, 'bid_vol2': 27, 'ask_vol2': 45, 'bid3': 20.21, 'ask3': 20.26, 'bid_vol3': 44, 'ask_vol3': 30, 'bid4': 20.2, 'ask4': 20.27, 'bid_vol4': 120, 'ask_vol4': 3, 'bid5': 20.19, 'ask5': 20.29, 'bid_vol5': 132, 'ask_vol5': 40}
2025-08-25 13:58:21 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3888 条数据
2025-08-25 13:58:21 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2462/2462 条Tick数据
2025-08-25 13:58:21 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:21 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:21 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:21 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2462 条记录，耗时: 0.55秒
2025-08-25 13:58:21 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:20, 股票数: 4394
2025-08-25 13:58:21 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1369.95 毫秒，每秒处理股票数: 3207.41
2025-08-25 13:58:21 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.37s (网络: 1.37s, 处理: -0.00s, 数据库: 1.37s)
2025-08-25 13:58:22 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:22 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1592
2025-08-25 13:58:22 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1592/1592 条Tick数据
2025-08-25 13:58:22 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:22 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:22 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:22 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1592 条记录，耗时: 0.10秒
2025-08-25 13:58:22 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:22, 股票数: 4394
2025-08-25 13:58:22 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 896.24 毫秒，每秒处理股票数: 4902.72
2025-08-25 13:58:22 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.90s (网络: 0.90s, 处理: -0.00s, 数据库: 0.90s)
2025-08-25 13:58:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1271
2025-08-25 13:58:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2863 条tick数据
2025-08-25 13:58:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:02'), 'stock_code': '002927', 'price': 16.4, 'volume': 65657, 'amount': 108262928.0, 'open': 16.48, 'high': 16.66, 'low': 16.29, 'last_close': 16.48, 'cur_vol': 4, 'change': 0.0, 'bid1': 16.39, 'ask1': 16.4, 'bid_vol1': 28, 'ask_vol1': 98, 'bid2': 16.38, 'ask2': 16.41, 'bid_vol2': 98, 'ask_vol2': 211, 'bid3': 16.37, 'ask3': 16.42, 'bid_vol3': 139, 'ask_vol3': 19, 'bid4': 16.36, 'ask4': 16.43, 'bid_vol4': 30, 'ask_vol4': 32, 'bid5': 16.35, 'ask5': 16.44, 'bid_vol5': 106, 'ask_vol5': 42}
2025-08-25 13:58:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:58:07'), 'stock_code': '002928', 'price': 9.07, 'volume': 117228, 'amount': 106181792.0, 'open': 9.07, 'high': 9.11, 'low': 9.0, 'last_close': 9.07, 'cur_vol': 16, 'change': -0.009999999999999787, 'bid1': 9.07, 'ask1': 9.08, 'bid_vol1': 615, 'ask_vol1': 464, 'bid2': 9.06, 'ask2': 9.09, 'bid_vol2': 221, 'ask_vol2': 735, 'bid3': 9.05, 'ask3': 9.1, 'bid_vol3': 259, 'ask_vol3': 660, 'bid4': 9.040000000000001, 'ask4': 9.11, 'bid_vol4': 335, 'ask_vol4': 289, 'bid5': 9.03, 'ask5': 9.120000000000001, 'bid_vol5': 660, 'ask_vol5': 197}
2025-08-25 13:58:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:41'), 'stock_code': '002929', 'price': 51.72, 'volume': 207531, 'amount': 1066544768.0, 'open': 50.56, 'high': 52.76, 'low': 49.6, 'last_close': 49.980000000000004, 'cur_vol': 23, 'change': 0.00999999999999801, 'bid1': 51.72, 'ask1': 51.730000000000004, 'bid_vol1': 34, 'ask_vol1': 37, 'bid2': 51.71, 'ask2': 51.75, 'bid_vol2': 4, 'ask_vol2': 6, 'bid3': 51.7, 'ask3': 51.76, 'bid_vol3': 22, 'ask_vol3': 57, 'bid4': 51.68, 'ask4': 51.77, 'bid_vol4': 33, 'ask_vol4': 4, 'bid5': 51.65, 'ask5': 51.78, 'bid_vol5': 1, 'ask_vol5': 1}
2025-08-25 13:58:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2863, 有效=2863, 无效=0
2025-08-25 13:58:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2863 条数据到stock_tick_data表
2025-08-25 13:58:24 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:02'), 'stock_code': '002927', 'price': 16.4, 'volume': 65657, 'amount': 108262928.0, 'open': 16.48, 'high': 16.66, 'low': 16.29, 'last_close': 16.48, 'cur_vol': 4, 'change': 0.0, 'bid1': 16.39, 'ask1': 16.4, 'bid_vol1': 28, 'ask_vol1': 98, 'bid2': 16.38, 'ask2': 16.41, 'bid_vol2': 98, 'ask_vol2': 211, 'bid3': 16.37, 'ask3': 16.42, 'bid_vol3': 139, 'ask_vol3': 19, 'bid4': 16.36, 'ask4': 16.43, 'bid_vol4': 30, 'ask_vol4': 32, 'bid5': 16.35, 'ask5': 16.44, 'bid_vol5': 106, 'ask_vol5': 42}
2025-08-25 13:58:25 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2863 条数据
2025-08-25 13:58:25 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1271/1271 条Tick数据
2025-08-25 13:58:25 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:25 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:25 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:25 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1271 条记录，耗时: 0.42秒
2025-08-25 13:58:25 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:24, 股票数: 4394
2025-08-25 13:58:25 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1062.51 毫秒，每秒处理股票数: 4135.51
2025-08-25 13:58:25 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.06s (网络: 1.06s, 处理: 0.00s, 数据库: 1.06s)
2025-08-25 13:58:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2246
2025-08-25 13:58:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2246 条tick数据
2025-08-25 13:58:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:07'), 'stock_code': '002286', 'price': 10.74, 'volume': 94900, 'amount': 102250352.0, 'open': 10.77, 'high': 10.84, 'low': 10.72, 'last_close': 10.77, 'cur_vol': 37, 'change': 0.0, 'bid1': 10.74, 'ask1': 10.75, 'bid_vol1': 1131, 'ask_vol1': 91, 'bid2': 10.73, 'ask2': 10.76, 'bid_vol2': 2431, 'ask_vol2': 220, 'bid3': 10.72, 'ask3': 10.77, 'bid_vol3': 1398, 'ask_vol3': 189, 'bid4': 10.71, 'ask4': 10.78, 'bid_vol4': 856, 'ask_vol4': 610, 'bid5': 10.700000000000001, 'ask5': 10.790000000000001, 'bid_vol5': 1823, 'ask_vol5': 48}
2025-08-25 13:58:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:56'), 'stock_code': '002287', 'price': 29.080000000000002, 'volume': 121594, 'amount': 350820096.0, 'open': 28.7, 'high': 29.19, 'low': 28.400000000000002, 'last_close': 28.51, 'cur_vol': 7, 'change': 0.0, 'bid1': 29.080000000000002, 'ask1': 29.1, 'bid_vol1': 77, 'ask_vol1': 65, 'bid2': 29.07, 'ask2': 29.11, 'bid_vol2': 25, 'ask_vol2': 95, 'bid3': 29.060000000000002, 'ask3': 29.12, 'bid_vol3': 1, 'ask_vol3': 69, 'bid4': 29.05, 'ask4': 29.13, 'bid_vol4': 25, 'ask_vol4': 158, 'bid5': 29.03, 'ask5': 29.14, 'bid_vol5': 4, 'ask_vol5': 84}
2025-08-25 13:58:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:57:53'), 'stock_code': '002290', 'price': 35.38, 'volume': 245214, 'amount': 868218368.0, 'open': 34.31, 'high': 36.18, 'low': 34.31, 'last_close': 32.89, 'cur_vol': 455, 'change': 0.0, 'bid1': 35.37, 'ask1': 35.39, 'bid_vol1': 8, 'ask_vol1': 70, 'bid2': 35.36, 'ask2': 35.4, 'bid_vol2': 41, 'ask_vol2': 991, 'bid3': 35.35, 'ask3': 35.410000000000004, 'bid_vol3': 5, 'ask_vol3': 41, 'bid4': 35.34, 'ask4': 35.42, 'bid_vol4': 2, 'ask_vol4': 7, 'bid5': 35.32, 'ask5': 35.43, 'bid_vol5': 11, 'ask_vol5': 53}
2025-08-25 13:58:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2246, 有效=2246, 无效=0
2025-08-25 13:58:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2246 条数据到stock_tick_data表
2025-08-25 13:58:26 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:07'), 'stock_code': '002286', 'price': 10.74, 'volume': 94900, 'amount': 102250352.0, 'open': 10.77, 'high': 10.84, 'low': 10.72, 'last_close': 10.77, 'cur_vol': 37, 'change': 0.0, 'bid1': 10.74, 'ask1': 10.75, 'bid_vol1': 1131, 'ask_vol1': 91, 'bid2': 10.73, 'ask2': 10.76, 'bid_vol2': 2431, 'ask_vol2': 220, 'bid3': 10.72, 'ask3': 10.77, 'bid_vol3': 1398, 'ask_vol3': 189, 'bid4': 10.71, 'ask4': 10.78, 'bid_vol4': 856, 'ask_vol4': 610, 'bid5': 10.700000000000001, 'ask5': 10.790000000000001, 'bid_vol5': 1823, 'ask_vol5': 48}
2025-08-25 13:58:27 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2246 条数据
2025-08-25 13:58:27 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2246/2246 条Tick数据
2025-08-25 13:58:27 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:27 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:27 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:27 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2246 条记录，耗时: 0.37秒
2025-08-25 13:58:27 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:26, 股票数: 4394
2025-08-25 13:58:27 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1127.97 毫秒，每秒处理股票数: 3895.48
2025-08-25 13:58:27 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.13s (网络: 1.13s, 处理: -0.00s, 数据库: 1.13s)
2025-08-25 13:58:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1517
2025-08-25 13:58:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1517 条tick数据
2025-08-25 13:58:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:02'), 'stock_code': '600506', 'price': 22.79, 'volume': 116107, 'amount': 262718976.0, 'open': 22.68, 'high': 23.2, 'low': 22.3, 'last_close': 22.37, 'cur_vol': 1, 'change': -0.010000000000001563, 'bid1': 22.79, 'ask1': 22.8, 'bid_vol1': 79, 'ask_vol1': 51, 'bid2': 22.78, 'ask2': 22.81, 'bid_vol2': 17, 'ask_vol2': 26, 'bid3': 22.77, 'ask3': 22.82, 'bid_vol3': 35, 'ask_vol3': 37, 'bid4': 22.75, 'ask4': 22.830000000000002, 'bid_vol4': 28, 'ask_vol4': 65, 'bid5': 22.740000000000002, 'ask5': 22.84, 'bid_vol5': 61, 'ask_vol5': 70}
2025-08-25 13:58:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:58:12'), 'stock_code': '600507', 'price': 5.88, 'volume': 264586, 'amount': 155460480.0, 'open': 5.88, 'high': 5.94, 'low': 5.8, 'last_close': 5.86, 'cur_vol': 24, 'change': -0.009999999999999787, 'bid1': 5.88, 'ask1': 5.89, 'bid_vol1': 1235, 'ask_vol1': 408, 'bid2': 5.87, 'ask2': 5.9, 'bid_vol2': 537, 'ask_vol2': 3377, 'bid3': 5.86, 'ask3': 5.91, 'bid_vol3': 1273, 'ask_vol3': 2034, 'bid4': 5.8500000000000005, 'ask4': 5.92, 'bid_vol4': 2483, 'ask_vol4': 2420, 'bid5': 5.84, 'ask5': 5.93, 'bid_vol5': 966, 'ask_vol5': 4582}
2025-08-25 13:58:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:11'), 'stock_code': '600509', 'price': 6.8, 'volume': 457616, 'amount': 312710112.0, 'open': 6.82, 'high': 6.92, 'low': 6.78, 'last_close': 6.82, 'cur_vol': 154, 'change': 0.0, 'bid1': 6.8, 'ask1': 6.8100000000000005, 'bid_vol1': 2169, 'ask_vol1': 1687, 'bid2': 6.79, 'ask2': 6.82, 'bid_vol2': 5182, 'ask_vol2': 3436, 'bid3': 6.78, 'ask3': 6.83, 'bid_vol3': 5070, 'ask_vol3': 1270, 'bid4': 6.7700000000000005, 'ask4': 6.84, 'bid_vol4': 2018, 'ask_vol4': 2949, 'bid5': 6.76, 'ask5': 6.8500000000000005, 'bid_vol5': 2569, 'ask_vol5': 1448}
2025-08-25 13:58:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1517, 有效=1517, 无效=0
2025-08-25 13:58:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1517 条数据到stock_tick_data表
2025-08-25 13:58:28 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:02'), 'stock_code': '600506', 'price': 22.79, 'volume': 116107, 'amount': 262718976.0, 'open': 22.68, 'high': 23.2, 'low': 22.3, 'last_close': 22.37, 'cur_vol': 1, 'change': -0.010000000000001563, 'bid1': 22.79, 'ask1': 22.8, 'bid_vol1': 79, 'ask_vol1': 51, 'bid2': 22.78, 'ask2': 22.81, 'bid_vol2': 17, 'ask_vol2': 26, 'bid3': 22.77, 'ask3': 22.82, 'bid_vol3': 35, 'ask_vol3': 37, 'bid4': 22.75, 'ask4': 22.830000000000002, 'bid_vol4': 28, 'ask_vol4': 65, 'bid5': 22.740000000000002, 'ask5': 22.84, 'bid_vol5': 61, 'ask_vol5': 70}
2025-08-25 13:58:29 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1517 条数据
2025-08-25 13:58:29 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1517/1517 条Tick数据
2025-08-25 13:58:29 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:29 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:29 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:29 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1517 条记录，耗时: 0.28秒
2025-08-25 13:58:29 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:28, 股票数: 4394
2025-08-25 13:58:29 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1097.90 毫秒，每秒处理股票数: 4002.18
2025-08-25 13:58:29 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.10s (网络: 1.10s, 处理: 0.00s, 数据库: 1.10s)
2025-08-25 13:58:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1238
2025-08-25 13:58:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1238 条tick数据
2025-08-25 13:58:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:13'), 'stock_code': '600281', 'price': 7.12, 'volume': 358318, 'amount': 256802800.0, 'open': 7.05, 'high': 7.25, 'low': 7.03, 'last_close': 7.0600000000000005, 'cur_vol': 20, 'change': 0.0, 'bid1': 7.11, 'ask1': 7.12, 'bid_vol1': 1353, 'ask_vol1': 1349, 'bid2': 7.1000000000000005, 'ask2': 7.13, 'bid_vol2': 4123, 'ask_vol2': 903, 'bid3': 7.09, 'ask3': 7.140000000000001, 'bid_vol3': 6660, 'ask_vol3': 260, 'bid4': 7.08, 'ask4': 7.15, 'bid_vol4': 3607, 'ask_vol4': 1234, 'bid5': 7.07, 'ask5': 7.16, 'bid_vol5': 1740, 'ask_vol5': 634}
2025-08-25 13:58:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:58:14'), 'stock_code': '600282', 'price': 4.83, 'volume': 404578, 'amount': 195331872.0, 'open': 4.78, 'high': 4.88, 'low': 4.75, 'last_close': 4.7700000000000005, 'cur_vol': 1, 'change': 0.0, 'bid1': 4.83, 'ask1': 4.84, 'bid_vol1': 168, 'ask_vol1': 4692, 'bid2': 4.82, 'ask2': 4.8500000000000005, 'bid_vol2': 1639, 'ask_vol2': 3562, 'bid3': 4.8100000000000005, 'ask3': 4.86, 'bid_vol3': 2872, 'ask_vol3': 5292, 'bid4': 4.8, 'ask4': 4.87, 'bid_vol4': 2046, 'ask_vol4': 12671, 'bid5': 4.79, 'ask5': 4.88, 'bid_vol5': 774, 'ask_vol5': 13351}
2025-08-25 13:58:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:11'), 'stock_code': '600284', 'price': 9.01, 'volume': 865845, 'amount': 768652032.0, 'open': 8.8, 'high': 9.16, 'low': 8.58, 'last_close': 8.44, 'cur_vol': 1, 'change': 0.0, 'bid1': 9.0, 'ask1': 9.01, 'bid_vol1': 90, 'ask_vol1': 153, 'bid2': 8.99, 'ask2': 9.02, 'bid_vol2': 502, 'ask_vol2': 172, 'bid3': 8.98, 'ask3': 9.03, 'bid_vol3': 152, 'ask_vol3': 204, 'bid4': 8.97, 'ask4': 9.040000000000001, 'bid_vol4': 55, 'ask_vol4': 80, 'bid5': 8.96, 'ask5': 9.05, 'bid_vol5': 395, 'ask_vol5': 197}
2025-08-25 13:58:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1238, 有效=1238, 无效=0
2025-08-25 13:58:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1238 条数据到stock_tick_data表
2025-08-25 13:58:30 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:13'), 'stock_code': '600281', 'price': 7.12, 'volume': 358318, 'amount': 256802800.0, 'open': 7.05, 'high': 7.25, 'low': 7.03, 'last_close': 7.0600000000000005, 'cur_vol': 20, 'change': 0.0, 'bid1': 7.11, 'ask1': 7.12, 'bid_vol1': 1353, 'ask_vol1': 1349, 'bid2': 7.1000000000000005, 'ask2': 7.13, 'bid_vol2': 4123, 'ask_vol2': 903, 'bid3': 7.09, 'ask3': 7.140000000000001, 'bid_vol3': 6660, 'ask_vol3': 260, 'bid4': 7.08, 'ask4': 7.15, 'bid_vol4': 3607, 'ask_vol4': 1234, 'bid5': 7.07, 'ask5': 7.16, 'bid_vol5': 1740, 'ask_vol5': 634}
2025-08-25 13:58:31 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1238 条数据
2025-08-25 13:58:31 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1238/1238 条Tick数据
2025-08-25 13:58:31 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:31 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:31 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:31 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1238 条记录，耗时: 0.26秒
2025-08-25 13:58:31 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:30, 股票数: 4394
2025-08-25 13:58:31 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1090.05 毫秒，每秒处理股票数: 4031.01
2025-08-25 13:58:31 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.09s (网络: 1.09s, 处理: -0.00s, 数据库: 1.09s)
2025-08-25 13:58:32 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:32 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2049
2025-08-25 13:58:32 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2049/2049 条Tick数据
2025-08-25 13:58:32 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:32 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:32 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:32 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2049 条记录，耗时: 0.11秒
2025-08-25 13:58:32 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:32, 股票数: 4394
2025-08-25 13:58:32 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 938.24 毫秒，每秒处理股票数: 4683.22
2025-08-25 13:58:32 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.94s (网络: 0.94s, 处理: -0.00s, 数据库: 0.94s)
2025-08-25 13:58:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1359
2025-08-25 13:58:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3408 条tick数据
2025-08-25 13:58:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:11'), 'stock_code': '600138', 'price': 10.13, 'volume': 166591, 'amount': 168862688.0, 'open': 10.11, 'high': 10.18, 'low': 10.09, 'last_close': 10.11, 'cur_vol': 1, 'change': 0.0, 'bid1': 10.13, 'ask1': 10.14, 'bid_vol1': 1847, 'ask_vol1': 2111, 'bid2': 10.120000000000001, 'ask2': 10.15, 'bid_vol2': 2062, 'ask_vol2': 1244, 'bid3': 10.11, 'ask3': 10.16, 'bid_vol3': 3574, 'ask_vol3': 1514, 'bid4': 10.1, 'ask4': 10.17, 'bid_vol4': 3146, 'ask_vol4': 2433, 'bid5': 10.09, 'ask5': 10.18, 'bid_vol5': 1456, 'ask_vol5': 3186}
2025-08-25 13:58:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:58:01'), 'stock_code': '600141', 'price': 26.98, 'volume': 235329, 'amount': 629722560.0, 'open': 25.75, 'high': 27.080000000000002, 'low': 25.75, 'last_close': 25.88, 'cur_vol': 8, 'change': 0.0, 'bid1': 26.97, 'ask1': 26.98, 'bid_vol1': 23, 'ask_vol1': 1524, 'bid2': 26.96, 'ask2': 26.990000000000002, 'bid_vol2': 55, 'ask_vol2': 214, 'bid3': 26.95, 'ask3': 27.0, 'bid_vol3': 84, 'ask_vol3': 2017, 'bid4': 26.94, 'ask4': 27.01, 'bid_vol4': 64, 'ask_vol4': 95, 'bid5': 26.93, 'ask5': 27.02, 'bid_vol5': 67, 'ask_vol5': 175}
2025-08-25 13:58:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:15'), 'stock_code': '600149', 'price': 5.8, 'volume': 131547, 'amount': 76659784.0, 'open': 5.93, 'high': 5.94, 'low': 5.7700000000000005, 'last_close': 5.87, 'cur_vol': 32, 'change': 0.0, 'bid1': 5.8, 'ask1': 5.8100000000000005, 'bid_vol1': 71, 'ask_vol1': 348, 'bid2': 5.79, 'ask2': 5.82, 'bid_vol2': 559, 'ask_vol2': 419, 'bid3': 5.78, 'ask3': 5.83, 'bid_vol3': 835, 'ask_vol3': 382, 'bid4': 5.7700000000000005, 'ask4': 5.84, 'bid_vol4': 1811, 'ask_vol4': 387, 'bid5': 5.76, 'ask5': 5.8500000000000005, 'bid_vol5': 780, 'ask_vol5': 528}
2025-08-25 13:58:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3408, 有效=3408, 无效=0
2025-08-25 13:58:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3408 条数据到stock_tick_data表
2025-08-25 13:58:34 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:11'), 'stock_code': '600138', 'price': 10.13, 'volume': 166591, 'amount': 168862688.0, 'open': 10.11, 'high': 10.18, 'low': 10.09, 'last_close': 10.11, 'cur_vol': 1, 'change': 0.0, 'bid1': 10.13, 'ask1': 10.14, 'bid_vol1': 1847, 'ask_vol1': 2111, 'bid2': 10.120000000000001, 'ask2': 10.15, 'bid_vol2': 2062, 'ask_vol2': 1244, 'bid3': 10.11, 'ask3': 10.16, 'bid_vol3': 3574, 'ask_vol3': 1514, 'bid4': 10.1, 'ask4': 10.17, 'bid_vol4': 3146, 'ask_vol4': 2433, 'bid5': 10.09, 'ask5': 10.18, 'bid_vol5': 1456, 'ask_vol5': 3186}
2025-08-25 13:58:35 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3408 条数据
2025-08-25 13:58:35 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1359/1359 条Tick数据
2025-08-25 13:58:35 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:35 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:35 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:35 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1359 条记录，耗时: 0.49秒
2025-08-25 13:58:35 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:34, 股票数: 4394
2025-08-25 13:58:35 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1289.49 毫秒，每秒处理股票数: 3407.54
2025-08-25 13:58:35 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.29s (网络: 1.29s, 处理: -0.00s, 数据库: 1.29s)
2025-08-25 13:58:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1277
2025-08-25 13:58:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1277 条tick数据
2025-08-25 13:58:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:07'), 'stock_code': '300170', 'price': 20.59, 'volume': 1297929, 'amount': 2683736320.0, 'open': 20.25, 'high': 21.5, 'low': 20.04, 'last_close': 19.97, 'cur_vol': 20, 'change': 0.0, 'bid1': 20.580000000000002, 'ask1': 20.59, 'bid_vol1': 402, 'ask_vol1': 62, 'bid2': 20.57, 'ask2': 20.6, 'bid_vol2': 83, 'ask_vol2': 542, 'bid3': 20.56, 'ask3': 20.61, 'bid_vol3': 220, 'ask_vol3': 128, 'bid4': 20.55, 'ask4': 20.62, 'bid_vol4': 289, 'ask_vol4': 49, 'bid5': 20.54, 'ask5': 20.63, 'bid_vol5': 72, 'ask_vol5': 17}
2025-08-25 13:58:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:58:16'), 'stock_code': '300173', 'price': 6.140000000000001, 'volume': 278881, 'amount': 172276688.0, 'open': 6.16, 'high': 6.22, 'low': 6.13, 'last_close': 6.16, 'cur_vol': 2, 'change': 0.0, 'bid1': 6.140000000000001, 'ask1': 6.15, 'bid_vol1': 173, 'ask_vol1': 1739, 'bid2': 6.13, 'ask2': 6.16, 'bid_vol2': 5332, 'ask_vol2': 1845, 'bid3': 6.12, 'ask3': 6.17, 'bid_vol3': 4969, 'ask_vol3': 2009, 'bid4': 6.11, 'ask4': 6.18, 'bid_vol4': 3269, 'ask_vol4': 1587, 'bid5': 6.1000000000000005, 'ask5': 6.19, 'bid_vol5': 5202, 'ask_vol5': 1789}
2025-08-25 13:58:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:12'), 'stock_code': '300177', 'price': 11.68, 'volume': 438651, 'amount': 509390592.0, 'open': 11.47, 'high': 11.75, 'low': 11.43, 'last_close': 11.47, 'cur_vol': 5, 'change': -0.009999999999999787, 'bid1': 11.68, 'ask1': 11.69, 'bid_vol1': 307, 'ask_vol1': 326, 'bid2': 11.67, 'ask2': 11.700000000000001, 'bid_vol2': 498, 'ask_vol2': 6182, 'bid3': 11.66, 'ask3': 11.71, 'bid_vol3': 426, 'ask_vol3': 612, 'bid4': 11.65, 'ask4': 11.72, 'bid_vol4': 231, 'ask_vol4': 1557, 'bid5': 11.64, 'ask5': 11.73, 'bid_vol5': 220, 'ask_vol5': 964}
2025-08-25 13:58:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1277, 有效=1277, 无效=0
2025-08-25 13:58:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1277 条数据到stock_tick_data表
2025-08-25 13:58:36 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:07'), 'stock_code': '300170', 'price': 20.59, 'volume': 1297929, 'amount': 2683736320.0, 'open': 20.25, 'high': 21.5, 'low': 20.04, 'last_close': 19.97, 'cur_vol': 20, 'change': 0.0, 'bid1': 20.580000000000002, 'ask1': 20.59, 'bid_vol1': 402, 'ask_vol1': 62, 'bid2': 20.57, 'ask2': 20.6, 'bid_vol2': 83, 'ask_vol2': 542, 'bid3': 20.56, 'ask3': 20.61, 'bid_vol3': 220, 'ask_vol3': 128, 'bid4': 20.55, 'ask4': 20.62, 'bid_vol4': 289, 'ask_vol4': 49, 'bid5': 20.54, 'ask5': 20.63, 'bid_vol5': 72, 'ask_vol5': 17}
2025-08-25 13:58:37 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1277 条数据
2025-08-25 13:58:37 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1277/1277 条Tick数据
2025-08-25 13:58:37 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:37 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:37 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:37 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1277 条记录，耗时: 0.25秒
2025-08-25 13:58:37 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:36, 股票数: 4394
2025-08-25 13:58:37 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1071.49 毫秒，每秒处理股票数: 4100.81
2025-08-25 13:58:37 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.07s (网络: 1.07s, 处理: -0.00s, 数据库: 1.07s)
2025-08-25 13:58:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2018
2025-08-25 13:58:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2018 条tick数据
2025-08-25 13:58:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:40'), 'stock_code': '300347', 'price': 68.07000000000001, 'volume': 115909, 'amount': 780555712.0, 'open': 66.6, 'high': 68.38, 'low': 66.2, 'last_close': 66.42, 'cur_vol': 6, 'change': 0.0, 'bid1': 68.01, 'ask1': 68.06, 'bid_vol1': 4, 'ask_vol1': 9, 'bid2': 68.0, 'ask2': 68.07000000000001, 'bid_vol2': 5, 'ask_vol2': 4, 'bid3': 67.99, 'ask3': 68.13, 'bid_vol3': 2, 'ask_vol3': 10, 'bid4': 67.98, 'ask4': 68.18, 'bid_vol4': 2, 'ask_vol4': 18, 'bid5': 67.97, 'ask5': 68.19, 'bid_vol5': 8, 'ask_vol5': 22}
2025-08-25 13:58:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:58:10'), 'stock_code': '300348', 'price': 18.51, 'volume': 963403, 'amount': 1766458368.0, 'open': 17.88, 'high': 18.68, 'low': 17.85, 'last_close': 17.88, 'cur_vol': 16, 'change': 0.010000000000001563, 'bid1': 18.5, 'ask1': 18.51, 'bid_vol1': 12, 'ask_vol1': 560, 'bid2': 18.490000000000002, 'ask2': 18.52, 'bid_vol2': 410, 'ask_vol2': 70, 'bid3': 18.48, 'ask3': 18.53, 'bid_vol3': 512, 'ask_vol3': 290, 'bid4': 18.47, 'ask4': 18.54, 'bid_vol4': 342, 'ask_vol4': 72, 'bid5': 18.46, 'ask5': 18.55, 'bid_vol5': 673, 'ask_vol5': 120}
2025-08-25 13:58:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:13'), 'stock_code': '300349', 'price': 12.85, 'volume': 73136, 'amount': 94385776.0, 'open': 12.98, 'high': 12.99, 'low': 12.790000000000001, 'last_close': 12.91, 'cur_vol': 272, 'change': 0.0, 'bid1': 12.84, 'ask1': 12.85, 'bid_vol1': 500, 'ask_vol1': 84, 'bid2': 12.83, 'ask2': 12.86, 'bid_vol2': 183, 'ask_vol2': 94, 'bid3': 12.82, 'ask3': 12.870000000000001, 'bid_vol3': 49, 'ask_vol3': 100, 'bid4': 12.81, 'ask4': 12.88, 'bid_vol4': 158, 'ask_vol4': 106, 'bid5': 12.8, 'ask5': 12.89, 'bid_vol5': 316, 'ask_vol5': 301}
2025-08-25 13:58:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2018, 有效=2018, 无效=0
2025-08-25 13:58:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2018 条数据到stock_tick_data表
2025-08-25 13:58:38 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:40'), 'stock_code': '300347', 'price': 68.07000000000001, 'volume': 115909, 'amount': 780555712.0, 'open': 66.6, 'high': 68.38, 'low': 66.2, 'last_close': 66.42, 'cur_vol': 6, 'change': 0.0, 'bid1': 68.01, 'ask1': 68.06, 'bid_vol1': 4, 'ask_vol1': 9, 'bid2': 68.0, 'ask2': 68.07000000000001, 'bid_vol2': 5, 'ask_vol2': 4, 'bid3': 67.99, 'ask3': 68.13, 'bid_vol3': 2, 'ask_vol3': 10, 'bid4': 67.98, 'ask4': 68.18, 'bid_vol4': 2, 'ask_vol4': 18, 'bid5': 67.97, 'ask5': 68.19, 'bid_vol5': 8, 'ask_vol5': 22}
2025-08-25 13:58:39 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2018 条数据
2025-08-25 13:58:39 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2018/2018 条Tick数据
2025-08-25 13:58:39 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:39 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:39 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:39 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2018 条记录，耗时: 0.35秒
2025-08-25 13:58:39 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:38, 股票数: 4394
2025-08-25 13:58:39 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1184.27 毫秒，每秒处理股票数: 3710.29
2025-08-25 13:58:39 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.18s (网络: 1.18s, 处理: 0.00s, 数据库: 1.18s)
2025-08-25 13:58:40 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:40 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1421
2025-08-25 13:58:40 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1421/1421 条Tick数据
2025-08-25 13:58:40 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:40 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:40 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:40 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1421 条记录，耗时: 0.10秒
2025-08-25 13:58:40 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:40, 股票数: 4394
2025-08-25 13:58:40 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 923.91 毫秒，每秒处理股票数: 4755.86
2025-08-25 13:58:40 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.92s (网络: 0.92s, 处理: 0.00s, 数据库: 0.92s)
2025-08-25 13:58:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1250
2025-08-25 13:58:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2671 条tick数据
2025-08-25 13:58:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:57:45'), 'stock_code': '300049', 'price': 63.49, 'volume': 172245, 'amount': 1097413376.0, 'open': 63.01, 'high': 65.28, 'low': 62.63, 'last_close': 64.32000000000001, 'cur_vol': 9, 'change': 0.03999999999999915, 'bid1': 63.46, 'ask1': 63.49, 'bid_vol1': 21, 'ask_vol1': 29, 'bid2': 63.45, 'ask2': 63.54, 'bid_vol2': 22, 'ask_vol2': 2, 'bid3': 63.440000000000005, 'ask3': 63.57, 'bid_vol3': 4, 'ask_vol3': 1, 'bid4': 63.43, 'ask4': 63.58, 'bid_vol4': 33, 'ask_vol4': 11, 'bid5': 63.42, 'ask5': 63.59, 'bid_vol5': 26, 'ask_vol5': 9}
2025-08-25 13:58:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:58:19'), 'stock_code': '300050', 'price': 6.78, 'volume': 540101, 'amount': 368826176.0, 'open': 6.8500000000000005, 'high': 7.03, 'low': 6.67, 'last_close': 6.79, 'cur_vol': 189, 'change': 0.009999999999999787, 'bid1': 6.7700000000000005, 'ask1': 6.78, 'bid_vol1': 609, 'ask_vol1': 167, 'bid2': 6.76, 'ask2': 6.79, 'bid_vol2': 1184, 'ask_vol2': 502, 'bid3': 6.75, 'ask3': 6.8, 'bid_vol3': 1558, 'ask_vol3': 781, 'bid4': 6.74, 'ask4': 6.8100000000000005, 'bid_vol4': 780, 'ask_vol4': 638, 'bid5': 6.73, 'ask5': 6.82, 'bid_vol5': 1069, 'ask_vol5': 627}
2025-08-25 13:58:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:18'), 'stock_code': '300051', 'price': 7.74, 'volume': 139311, 'amount': 108777768.0, 'open': 7.94, 'high': 7.94, 'low': 7.72, 'last_close': 7.91, 'cur_vol': 2, 'change': 0.0, 'bid1': 7.73, 'ask1': 7.74, 'bid_vol1': 922, 'ask_vol1': 340, 'bid2': 7.72, 'ask2': 7.75, 'bid_vol2': 1655, 'ask_vol2': 778, 'bid3': 7.71, 'ask3': 7.76, 'bid_vol3': 1435, 'ask_vol3': 413, 'bid4': 7.7, 'ask4': 7.7700000000000005, 'bid_vol4': 1568, 'ask_vol4': 107, 'bid5': 7.69, 'ask5': 7.78, 'bid_vol5': 439, 'ask_vol5': 130}
2025-08-25 13:58:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2671, 有效=2671, 无效=0
2025-08-25 13:58:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2671 条数据到stock_tick_data表
2025-08-25 13:58:42 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:57:45'), 'stock_code': '300049', 'price': 63.49, 'volume': 172245, 'amount': 1097413376.0, 'open': 63.01, 'high': 65.28, 'low': 62.63, 'last_close': 64.32000000000001, 'cur_vol': 9, 'change': 0.03999999999999915, 'bid1': 63.46, 'ask1': 63.49, 'bid_vol1': 21, 'ask_vol1': 29, 'bid2': 63.45, 'ask2': 63.54, 'bid_vol2': 22, 'ask_vol2': 2, 'bid3': 63.440000000000005, 'ask3': 63.57, 'bid_vol3': 4, 'ask_vol3': 1, 'bid4': 63.43, 'ask4': 63.58, 'bid_vol4': 33, 'ask_vol4': 11, 'bid5': 63.42, 'ask5': 63.59, 'bid_vol5': 26, 'ask_vol5': 9}
2025-08-25 13:58:43 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2671 条数据
2025-08-25 13:58:43 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1250/1250 条Tick数据
2025-08-25 13:58:43 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:43 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:43 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:43 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1250 条记录，耗时: 0.44秒
2025-08-25 13:58:43 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:42, 股票数: 4394
2025-08-25 13:58:43 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1226.32 毫秒，每秒处理股票数: 3583.09
2025-08-25 13:58:43 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.23s (网络: 1.23s, 处理: 0.00s, 数据库: 1.23s)
2025-08-25 13:58:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:44 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2055
2025-08-25 13:58:44 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2055/2055 条Tick数据
2025-08-25 13:58:44 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:44 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:44 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:44 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2055 条记录，耗时: 0.10秒
2025-08-25 13:58:44 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:44, 股票数: 4394
2025-08-25 13:58:44 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 869.80 毫秒，每秒处理股票数: 5051.71
2025-08-25 13:58:44 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.87s (网络: 0.87s, 处理: -0.00s, 数据库: 0.87s)
2025-08-25 13:58:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1428
2025-08-25 13:58:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3483 条tick数据
2025-08-25 13:58:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:23'), 'stock_code': '000592', 'price': 3.35, 'volume': 1782531, 'amount': 598029248.0, 'open': 3.3000000000000003, 'high': 3.42, 'low': 3.24, 'last_close': 3.3200000000000003, 'cur_vol': 44, 'change': 0.0, 'bid1': 3.34, 'ask1': 3.35, 'bid_vol1': 16758, 'ask_vol1': 3115, 'bid2': 3.33, 'ask2': 3.36, 'bid_vol2': 7319, 'ask_vol2': 8741, 'bid3': 3.3200000000000003, 'ask3': 3.37, 'bid_vol3': 10785, 'ask_vol3': 6077, 'bid4': 3.31, 'ask4': 3.38, 'bid_vol4': 7120, 'ask_vol4': 10277, 'bid5': 3.3000000000000003, 'ask5': 3.39, 'bid_vol5': 9436, 'ask_vol5': 17093}
2025-08-25 13:58:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:39'), 'stock_code': '000596', 'price': 175.42000000000002, 'volume': 48403, 'amount': 819069056.0, 'open': 164.8, 'high': 176.08, 'low': 164.0, 'last_close': 164.33, 'cur_vol': 26, 'change': 0.0, 'bid1': 175.41, 'ask1': 175.42000000000002, 'bid_vol1': 19, 'ask_vol1': 1, 'bid2': 175.31, 'ask2': 175.62, 'bid_vol2': 7, 'ask_vol2': 1, 'bid3': 175.3, 'ask3': 175.69, 'bid_vol3': 24, 'ask_vol3': 1, 'bid4': 175.28, 'ask4': 175.8, 'bid_vol4': 3, 'ask_vol4': 1, 'bid5': 175.27, 'ask5': 175.85, 'bid_vol5': 5, 'ask_vol5': 4}
2025-08-25 13:58:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:21'), 'stock_code': '000597', 'price': 5.79, 'volume': 191789, 'amount': 111141336.0, 'open': 5.76, 'high': 5.84, 'low': 5.75, 'last_close': 5.7700000000000005, 'cur_vol': 51, 'change': 0.0, 'bid1': 5.79, 'ask1': 5.8, 'bid_vol1': 456, 'ask_vol1': 2240, 'bid2': 5.78, 'ask2': 5.8100000000000005, 'bid_vol2': 5710, 'ask_vol2': 1905, 'bid3': 5.7700000000000005, 'ask3': 5.82, 'bid_vol3': 3285, 'ask_vol3': 1728, 'bid4': 5.76, 'ask4': 5.83, 'bid_vol4': 4533, 'ask_vol4': 5410, 'bid5': 5.75, 'ask5': 5.84, 'bid_vol5': 4210, 'ask_vol5': 6085}
2025-08-25 13:58:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3483, 有效=3483, 无效=0
2025-08-25 13:58:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3483 条数据到stock_tick_data表
2025-08-25 13:58:46 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:23'), 'stock_code': '000592', 'price': 3.35, 'volume': 1782531, 'amount': 598029248.0, 'open': 3.3000000000000003, 'high': 3.42, 'low': 3.24, 'last_close': 3.3200000000000003, 'cur_vol': 44, 'change': 0.0, 'bid1': 3.34, 'ask1': 3.35, 'bid_vol1': 16758, 'ask_vol1': 3115, 'bid2': 3.33, 'ask2': 3.36, 'bid_vol2': 7319, 'ask_vol2': 8741, 'bid3': 3.3200000000000003, 'ask3': 3.37, 'bid_vol3': 10785, 'ask_vol3': 6077, 'bid4': 3.31, 'ask4': 3.38, 'bid_vol4': 7120, 'ask_vol4': 10277, 'bid5': 3.3000000000000003, 'ask5': 3.39, 'bid_vol5': 9436, 'ask_vol5': 17093}
2025-08-25 13:58:47 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3483 条数据
2025-08-25 13:58:47 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1428/1428 条Tick数据
2025-08-25 13:58:47 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:47 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:47 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:47 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1428 条记录，耗时: 0.50秒
2025-08-25 13:58:47 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:46, 股票数: 4394
2025-08-25 13:58:47 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1317.25 毫秒，每秒处理股票数: 3335.74
2025-08-25 13:58:47 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.32s (网络: 1.32s, 处理: -0.00s, 数据库: 1.32s)
2025-08-25 13:58:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:48 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1311
2025-08-25 13:58:48 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1311/1311 条Tick数据
2025-08-25 13:58:48 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:48 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:48 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:48 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1311 条记录，耗时: 0.09秒
2025-08-25 13:58:48 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:48, 股票数: 4394
2025-08-25 13:58:48 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 878.17 毫秒，每秒处理股票数: 5003.59
2025-08-25 13:58:48 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.88s (网络: 0.88s, 处理: -0.00s, 数据库: 0.88s)
2025-08-25 13:58:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2022
2025-08-25 13:58:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 3333 条tick数据
2025-08-25 13:58:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:10'), 'stock_code': '300674', 'price': 28.22, 'volume': 432541, 'amount': 1213314048.0, 'open': 27.82, 'high': 28.38, 'low': 27.71, 'last_close': 27.810000000000002, 'cur_vol': 58, 'change': 0.0, 'bid1': 28.22, 'ask1': 28.23, 'bid_vol1': 694, 'ask_vol1': 161, 'bid2': 28.21, 'ask2': 28.240000000000002, 'bid_vol2': 40, 'ask_vol2': 120, 'bid3': 28.2, 'ask3': 28.25, 'bid_vol3': 183, 'ask_vol3': 193, 'bid4': 28.19, 'ask4': 28.26, 'bid_vol4': 133, 'ask_vol4': 294, 'bid5': 28.18, 'ask5': 28.27, 'bid_vol5': 109, 'ask_vol5': 386}
2025-08-25 13:58:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:57:54'), 'stock_code': '300676', 'price': 54.68, 'volume': 137283, 'amount': 756295808.0, 'open': 53.99, 'high': 56.72, 'low': 53.95, 'last_close': 53.45, 'cur_vol': 22, 'change': -0.020000000000003126, 'bid1': 54.67, 'ask1': 54.68, 'bid_vol1': 6, 'ask_vol1': 2, 'bid2': 54.660000000000004, 'ask2': 54.72, 'bid_vol2': 3, 'ask_vol2': 11, 'bid3': 54.620000000000005, 'ask3': 54.74, 'bid_vol3': 4, 'ask_vol3': 54, 'bid4': 54.58, 'ask4': 54.75, 'bid_vol4': 15, 'ask_vol4': 14, 'bid5': 54.550000000000004, 'ask5': 54.77, 'bid_vol5': 1, 'ask_vol5': 43}
2025-08-25 13:58:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:03'), 'stock_code': '300677', 'price': 38.43, 'volume': 196933, 'amount': 758735104.0, 'open': 38.4, 'high': 39.300000000000004, 'low': 37.660000000000004, 'last_close': 38.09, 'cur_vol': 67, 'change': 0.01999999999999602, 'bid1': 38.42, 'ask1': 38.43, 'bid_vol1': 19, 'ask_vol1': 157, 'bid2': 38.410000000000004, 'ask2': 38.45, 'bid_vol2': 1, 'ask_vol2': 20, 'bid3': 38.4, 'ask3': 38.46, 'bid_vol3': 48, 'ask_vol3': 18, 'bid4': 38.39, 'ask4': 38.47, 'bid_vol4': 84, 'ask_vol4': 12, 'bid5': 38.38, 'ask5': 38.480000000000004, 'bid_vol5': 34, 'ask_vol5': 3}
2025-08-25 13:58:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=3333, 有效=3333, 无效=0
2025-08-25 13:58:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 3333 条数据到stock_tick_data表
2025-08-25 13:58:50 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:10'), 'stock_code': '300674', 'price': 28.22, 'volume': 432541, 'amount': 1213314048.0, 'open': 27.82, 'high': 28.38, 'low': 27.71, 'last_close': 27.810000000000002, 'cur_vol': 58, 'change': 0.0, 'bid1': 28.22, 'ask1': 28.23, 'bid_vol1': 694, 'ask_vol1': 161, 'bid2': 28.21, 'ask2': 28.240000000000002, 'bid_vol2': 40, 'ask_vol2': 120, 'bid3': 28.2, 'ask3': 28.25, 'bid_vol3': 183, 'ask_vol3': 193, 'bid4': 28.19, 'ask4': 28.26, 'bid_vol4': 133, 'ask_vol4': 294, 'bid5': 28.18, 'ask5': 28.27, 'bid_vol5': 109, 'ask_vol5': 386}
2025-08-25 13:58:51 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 3333 条数据
2025-08-25 13:58:51 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2022/2022 条Tick数据
2025-08-25 13:58:51 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:51 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:51 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:51 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2022 条记录，耗时: 0.50秒
2025-08-25 13:58:51 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:50, 股票数: 4394
2025-08-25 13:58:51 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1287.15 毫秒，每秒处理股票数: 3413.74
2025-08-25 13:58:51 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.29s (网络: 1.29s, 处理: 0.00s, 数据库: 1.29s)
2025-08-25 13:58:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:52 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1621
2025-08-25 13:58:52 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1621/1621 条Tick数据
2025-08-25 13:58:52 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:52 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:52 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:52 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1621 条记录，耗时: 0.10秒
2025-08-25 13:58:52 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:52, 股票数: 4394
2025-08-25 13:58:52 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 881.43 毫秒，每秒处理股票数: 4985.07
2025-08-25 13:58:52 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 0.88s (网络: 0.88s, 处理: -0.00s, 数据库: 0.88s)
2025-08-25 13:58:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1167
2025-08-25 13:58:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2788 条tick数据
2025-08-25 13:58:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:28'), 'stock_code': '000592', 'price': 3.35, 'volume': 1782549, 'amount': 598035264.0, 'open': 3.3000000000000003, 'high': 3.42, 'low': 3.24, 'last_close': 3.3200000000000003, 'cur_vol': 1, 'change': 0.0, 'bid1': 3.34, 'ask1': 3.35, 'bid_vol1': 16811, 'ask_vol1': 3121, 'bid2': 3.33, 'ask2': 3.36, 'bid_vol2': 7322, 'ask_vol2': 8745, 'bid3': 3.3200000000000003, 'ask3': 3.37, 'bid_vol3': 10785, 'ask_vol3': 6077, 'bid4': 3.31, 'ask4': 3.38, 'bid_vol4': 7087, 'ask_vol4': 10277, 'bid5': 3.3000000000000003, 'ask5': 3.39, 'bid_vol5': 9437, 'ask_vol5': 17138}
2025-08-25 13:58:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:56:45'), 'stock_code': '000596', 'price': 175.41, 'volume': 48419, 'amount': 819349760.0, 'open': 164.8, 'high': 176.08, 'low': 164.0, 'last_close': 164.33, 'cur_vol': 7, 'change': 0.0, 'bid1': 175.41, 'ask1': 175.42000000000002, 'bid_vol1': 22, 'ask_vol1': 2, 'bid2': 175.31, 'ask2': 175.46, 'bid_vol2': 7, 'ask_vol2': 1, 'bid3': 175.3, 'ask3': 175.62, 'bid_vol3': 25, 'ask_vol3': 1, 'bid4': 175.28, 'ask4': 175.8, 'bid_vol4': 3, 'ask_vol4': 1, 'bid5': 175.27, 'ask5': 175.85, 'bid_vol5': 5, 'ask_vol5': 4}
2025-08-25 13:58:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:26'), 'stock_code': '000598', 'price': 6.95, 'volume': 299831, 'amount': 208773744.0, 'open': 6.96, 'high': 6.99, 'low': 6.93, 'last_close': 6.95, 'cur_vol': 7, 'change': 0.0, 'bid1': 6.95, 'ask1': 6.96, 'bid_vol1': 2688, 'ask_vol1': 5680, 'bid2': 6.94, 'ask2': 6.97, 'bid_vol2': 10859, 'ask_vol2': 1816, 'bid3': 6.93, 'ask3': 6.98, 'bid_vol3': 7350, 'ask_vol3': 3839, 'bid4': 6.92, 'ask4': 6.99, 'bid_vol4': 6657, 'ask_vol4': 4897, 'bid5': 6.91, 'ask5': 7.0, 'bid_vol5': 6573, 'ask_vol5': 5163}
2025-08-25 13:58:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2788, 有效=2788, 无效=0
2025-08-25 13:58:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2788 条数据到stock_tick_data表
2025-08-25 13:58:54 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:28'), 'stock_code': '000592', 'price': 3.35, 'volume': 1782549, 'amount': 598035264.0, 'open': 3.3000000000000003, 'high': 3.42, 'low': 3.24, 'last_close': 3.3200000000000003, 'cur_vol': 1, 'change': 0.0, 'bid1': 3.34, 'ask1': 3.35, 'bid_vol1': 16811, 'ask_vol1': 3121, 'bid2': 3.33, 'ask2': 3.36, 'bid_vol2': 7322, 'ask_vol2': 8745, 'bid3': 3.3200000000000003, 'ask3': 3.37, 'bid_vol3': 10785, 'ask_vol3': 6077, 'bid4': 3.31, 'ask4': 3.38, 'bid_vol4': 7087, 'ask_vol4': 10277, 'bid5': 3.3000000000000003, 'ask5': 3.39, 'bid_vol5': 9437, 'ask_vol5': 17138}
2025-08-25 13:58:55 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2788 条数据
2025-08-25 13:58:55 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1167/1167 条Tick数据
2025-08-25 13:58:55 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:55 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:55 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:55 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1167 条记录，耗时: 0.44秒
2025-08-25 13:58:55 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:54, 股票数: 4394
2025-08-25 13:58:55 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1211.56 毫秒，每秒处理股票数: 3626.72
2025-08-25 13:58:55 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.21s (网络: 1.21s, 处理: 0.00s, 数据库: 1.21s)
2025-08-25 13:58:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 2065
2025-08-25 13:58:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 2065 条tick数据
2025-08-25 13:58:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:01'), 'stock_code': '001317', 'price': 51.06, 'volume': 68310, 'amount': 351087744.0, 'open': 52.480000000000004, 'high': 52.71, 'low': 50.11, 'last_close': 51.480000000000004, 'cur_vol': 1, 'change': -0.01999999999999602, 'bid1': 51.06, 'ask1': 51.08, 'bid_vol1': 8, 'ask_vol1': 9, 'bid2': 51.050000000000004, 'ask2': 51.1, 'bid_vol2': 4, 'ask_vol2': 37, 'bid3': 51.03, 'ask3': 51.13, 'bid_vol3': 18, 'ask_vol3': 5, 'bid4': 51.0, 'ask4': 51.14, 'bid_vol4': 21, 'ask_vol4': 7, 'bid5': 50.980000000000004, 'ask5': 51.15, 'bid_vol5': 5, 'ask_vol5': 4}
2025-08-25 13:58:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:58:14'), 'stock_code': '001323', 'price': 30.09, 'volume': 8543, 'amount': 25830806.0, 'open': 30.490000000000002, 'high': 30.68, 'low': 29.990000000000002, 'last_close': 30.53, 'cur_vol': 2, 'change': 0.0, 'bid1': 30.09, 'ask1': 30.1, 'bid_vol1': 2, 'ask_vol1': 4, 'bid2': 30.080000000000002, 'ask2': 30.12, 'bid_vol2': 31, 'ask_vol2': 8, 'bid3': 30.07, 'ask3': 30.16, 'bid_vol3': 27, 'ask_vol3': 4, 'bid4': 30.060000000000002, 'ask4': 30.17, 'bid_vol4': 55, 'ask_vol4': 5, 'bid5': 30.05, 'ask5': 30.18, 'bid_vol5': 105, 'ask_vol5': 0}
2025-08-25 13:58:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:18'), 'stock_code': '001324', 'price': 22.51, 'volume': 54286, 'amount': 122431192.0, 'open': 22.26, 'high': 22.830000000000002, 'low': 22.26, 'last_close': 22.27, 'cur_vol': 30, 'change': 0.0, 'bid1': 22.5, 'ask1': 22.51, 'bid_vol1': 55, 'ask_vol1': 93, 'bid2': 22.490000000000002, 'ask2': 22.52, 'bid_vol2': 26, 'ask_vol2': 134, 'bid3': 22.48, 'ask3': 22.53, 'bid_vol3': 149, 'ask_vol3': 39, 'bid4': 22.47, 'ask4': 22.54, 'bid_vol4': 10, 'ask_vol4': 11, 'bid5': 22.46, 'ask5': 22.55, 'bid_vol5': 12, 'ask_vol5': 1882}
2025-08-25 13:58:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=2065, 有效=2065, 无效=0
2025-08-25 13:58:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 2065 条数据到stock_tick_data表
2025-08-25 13:58:56 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:01'), 'stock_code': '001317', 'price': 51.06, 'volume': 68310, 'amount': 351087744.0, 'open': 52.480000000000004, 'high': 52.71, 'low': 50.11, 'last_close': 51.480000000000004, 'cur_vol': 1, 'change': -0.01999999999999602, 'bid1': 51.06, 'ask1': 51.08, 'bid_vol1': 8, 'ask_vol1': 9, 'bid2': 51.050000000000004, 'ask2': 51.1, 'bid_vol2': 4, 'ask_vol2': 37, 'bid3': 51.03, 'ask3': 51.13, 'bid_vol3': 18, 'ask_vol3': 5, 'bid4': 51.0, 'ask4': 51.14, 'bid_vol4': 21, 'ask_vol4': 7, 'bid5': 50.980000000000004, 'ask5': 51.15, 'bid_vol5': 5, 'ask_vol5': 4}
2025-08-25 13:58:57 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 2065 条数据
2025-08-25 13:58:57 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 2065/2065 条Tick数据
2025-08-25 13:58:57 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:57 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:57 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:57 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 2065 条记录，耗时: 0.36秒
2025-08-25 13:58:57 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:56, 股票数: 4394
2025-08-25 13:58:57 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1167.53 毫秒，每秒处理股票数: 3763.51
2025-08-25 13:58:57 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.17s (网络: 1.17s, 处理: -0.00s, 数据库: 1.17s)
2025-08-25 13:58:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理前数据量: 4394
2025-08-25 13:58:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 处理后数据量: 1478
2025-08-25 13:58:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 开始验证 1478 条tick数据
2025-08-25 13:58:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:58:16'), 'stock_code': '002287', 'price': 29.09, 'volume': 121848, 'amount': 351559008.0, 'open': 28.7, 'high': 29.19, 'low': 28.400000000000002, 'last_close': 28.51, 'cur_vol': 5, 'change': 0.00999999999999801, 'bid1': 29.080000000000002, 'ask1': 29.09, 'bid_vol1': 90, 'ask_vol1': 99, 'bid2': 29.07, 'ask2': 29.1, 'bid_vol2': 26, 'ask_vol2': 84, 'bid3': 29.060000000000002, 'ask3': 29.11, 'bid_vol3': 6, 'ask_vol3': 95, 'bid4': 29.05, 'ask4': 29.12, 'bid_vol4': 28, 'ask_vol4': 68, 'bid5': 29.04, 'ask5': 29.13, 'bid_vol5': 4, 'ask_vol5': 159}
2025-08-25 13:58:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:58:30'), 'stock_code': '002291', 'price': 6.8100000000000005, 'volume': 371526, 'amount': 254535344.0, 'open': 6.8500000000000005, 'high': 6.91, 'low': 6.78, 'last_close': 6.82, 'cur_vol': 3, 'change': -0.009999999999999787, 'bid1': 6.8100000000000005, 'ask1': 6.82, 'bid_vol1': 1117, 'ask_vol1': 1709, 'bid2': 6.8, 'ask2': 6.83, 'bid_vol2': 2345, 'ask_vol2': 2017, 'bid3': 6.79, 'ask3': 6.84, 'bid_vol3': 2417, 'ask_vol3': 1783, 'bid4': 6.78, 'ask4': 6.8500000000000005, 'bid_vol4': 4662, 'ask_vol4': 2687, 'bid5': 6.7700000000000005, 'ask5': 6.86, 'bid_vol5': 1179, 'ask_vol5': 2586}
2025-08-25 13:58:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:58:28'), 'stock_code': '002293', 'price': 8.69, 'volume': 133732, 'amount': 115678216.0, 'open': 8.66, 'high': 8.76, 'low': 8.55, 'last_close': 8.63, 'cur_vol': 5, 'change': 0.0, 'bid1': 8.69, 'ask1': 8.700000000000001, 'bid_vol1': 416, 'ask_vol1': 337, 'bid2': 8.68, 'ask2': 8.71, 'bid_vol2': 330, 'ask_vol2': 175, 'bid3': 8.67, 'ask3': 8.72, 'bid_vol3': 556, 'ask_vol3': 802, 'bid4': 8.66, 'ask4': 8.73, 'bid_vol4': 1997, 'ask_vol4': 352, 'bid5': 8.65, 'ask5': 8.74, 'bid_vol5': 355, 'ask_vol5': 909}
2025-08-25 13:58:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 验证完成: 原始=1478, 有效=1478, 无效=0
2025-08-25 13:58:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 准备插入 1478 条数据到stock_tick_data表
2025-08-25 13:58:58 [INFO] [MarketDataFetcher_1599_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:58:16'), 'stock_code': '002287', 'price': 29.09, 'volume': 121848, 'amount': 351559008.0, 'open': 28.7, 'high': 29.19, 'low': 28.400000000000002, 'last_close': 28.51, 'cur_vol': 5, 'change': 0.00999999999999801, 'bid1': 29.080000000000002, 'ask1': 29.09, 'bid_vol1': 90, 'ask_vol1': 99, 'bid2': 29.07, 'ask2': 29.1, 'bid_vol2': 26, 'ask_vol2': 84, 'bid3': 29.060000000000002, 'ask3': 29.11, 'bid_vol3': 6, 'ask_vol3': 95, 'bid4': 29.05, 'ask4': 29.12, 'bid_vol4': 28, 'ask_vol4': 68, 'bid5': 29.04, 'ask5': 29.13, 'bid_vol5': 4, 'ask_vol5': 159}
2025-08-25 13:58:59 [INFO] [MarketDataFetcher_1599_main] - [调试] 数据库插入成功: 1478 条数据
2025-08-25 13:58:59 [INFO] [MarketDataFetcher_1599_main] - [成功] 成功保存 1478/1478 条Tick数据
2025-08-25 13:58:59 [INFO] [MarketDataFetcher_1599_main] - 插入统计: 成功率=100.0%, 平均速度=8562条/秒, 总记录数=164651
2025-08-25 13:58:59 [INFO] [MarketDataFetcher_1599_main] - [启动] 性能优化报告:
2025-08-25 13:58:59 [INFO] [MarketDataFetcher_1599_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:58:59 [INFO] [MarketDataFetcher_1599_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:58:59 [INFO] [MarketDataFetcher_1599_main] - 保存Tick数据完成，共 1478 条记录，耗时: 0.27秒
2025-08-25 13:58:59 [INFO] [MarketDataFetcher_1599_main] - 处理完成，时间: 13:58:58, 股票数: 4394
2025-08-25 13:58:59 [INFO] [MarketDataFetcher_1599_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1104.06 毫秒，每秒处理股票数: 3979.84
2025-08-25 13:58:59 [INFO] [MarketDataFetcher_1599_main] - [目标] 本次获取和保存数据总耗时: 1.10s (网络: 1.10s, 处理: -0.00s, 数据库: 1.10s)
