nohup: 忽略输入
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 12:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 进入午休休市暂停模式，将在 15.0 分钟后恢复运行
2025-08-25 12:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 进入午休休市暂停模式，将在 15.0 分钟后恢复运行
2025-08-25 12:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 进入午休休市暂停模式，将在 15.0 分钟后恢复运行
2025-08-25 12:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 进入午休休市暂停模式，将在 15.0 分钟后恢复运行
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:09:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=10, 平均周期时间=0.174s, 最近周期时间=0.129s
2025-08-25 13:09:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=10, 平均周期时间=0.173s, 最近周期时间=0.138s
2025-08-25 13:09:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=10, 平均周期时间=0.176s, 最近周期时间=0.258s
2025-08-25 13:09:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=10, 平均周期时间=0.178s, 最近周期时间=0.266s
2025-08-25 13:19:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=20, 平均周期时间=0.180s, 最近周期时间=0.118s
2025-08-25 13:19:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=20, 平均周期时间=0.179s, 最近周期时间=0.123s
2025-08-25 13:19:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=20, 平均周期时间=0.180s, 最近周期时间=0.229s
2025-08-25 13:19:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=20, 平均周期时间=0.181s, 最近周期时间=0.247s
2025-08-25 13:29:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=30, 平均周期时间=0.181s, 最近周期时间=0.137s
2025-08-25 13:29:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=30, 平均周期时间=0.181s, 最近周期时间=0.139s
2025-08-25 13:29:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=30, 平均周期时间=0.182s, 最近周期时间=0.271s
2025-08-25 13:29:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=30, 平均周期时间=0.182s, 最近周期时间=0.268s
2025-08-25 13:39:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=40, 平均周期时间=0.181s, 最近周期时间=0.135s
2025-08-25 13:39:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=40, 平均周期时间=0.181s, 最近周期时间=0.143s
2025-08-25 13:39:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=40, 平均周期时间=0.182s, 最近周期时间=0.277s
2025-08-25 13:39:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=40, 平均周期时间=0.183s, 最近周期时间=0.296s
2025-08-25 13:49:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=50, 平均周期时间=0.181s, 最近周期时间=0.102s
2025-08-25 13:49:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=50, 平均周期时间=0.181s, 最近周期时间=0.109s
2025-08-25 13:49:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=50, 平均周期时间=0.181s, 最近周期时间=0.204s
2025-08-25 13:49:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=50, 平均周期时间=0.181s, 最近周期时间=0.221s
2025-08-25 13:54:54 [INFO] [VolumeSurgeProcessor] - 接收到信号 15，正在停止处理器...
2025-08-25 13:54:54 [INFO] [VolumeSurgeProcessor] - 🛑 停止成交量激增处理器...
2025-08-25 13:54:54 [INFO] [VolumeSurgeProcessor] - 🛑 信号处理线程结束运行
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] - 🛑 工作线程 2 结束运行
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] - 🛑 工作线程 3 结束运行
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] - 🛑 工作线程 1 结束运行
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] - 🛑 工作线程 0 结束运行
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] - 📊 最终统计信息:
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    运行时间: 2:09:59.733598
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    总周期数: 220
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    总信号数: 0
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    平均周期时间: 0.183秒
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    处理股票数: 241670
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    错误次数: 0
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    平均执行一轮时间: 0.183秒
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器已停止
