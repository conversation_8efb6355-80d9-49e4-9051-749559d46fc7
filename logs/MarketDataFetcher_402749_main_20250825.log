﻿2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - 成功加载配置文件: config/main.toml
2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - 开始加载股票列表...
2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - 正在从数据库加载股票列表...
2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - [初始化] 股票列表加载完成: 4394 只股票
2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - [初始化] 有效股票代码: 4394 个
2025-08-25 09:39:43 [INFO] [MarketDataFetcher_402749_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-08-25 09:39:44 [INFO] [MarketDataFetcher_402749_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-08-25 09:39:44 [INFO] [MarketDataFetcher_402749_main] - 正在初始化数据库管理器...
2025-08-25 09:39:44 [INFO] [MarketDataFetcher_402749_main] - [成功] 数据库管理器初始化成功
2025-08-25 09:39:44 [INFO] [MarketDataFetcher_402749_main] - 📦 使用数据库缓存机制
2025-08-25 09:39:44 [INFO] [MarketDataFetcher_402749_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-08-25 09:39:44 [INFO] [MarketDataFetcher_402749_main] - 开始加载股票列表...
2025-08-25 09:39:44 [INFO] [MarketDataFetcher_402749_main] - 正在从数据库加载股票列表...
2025-08-25 09:39:44 [INFO] [MarketDataFetcher_402749_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 09:39:44 [INFO] [MarketDataFetcher_402749_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 09:39:44 [INFO] [MarketDataFetcher_402749_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 09:39:44 [INFO] [MarketDataFetcher_402749_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 09:39:44 [INFO] [MarketDataFetcher_402749_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 09:39:44 [INFO] [MarketDataFetcher_402749_main] - 定时任务线程启动
2025-08-25 09:39:46 [WARNING] [MarketDataFetcher_402749_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-25 09:39:46 [ERROR] [MarketDataFetcher_402749_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-25 09:39:46 [ERROR] [MarketDataFetcher_402749_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_to_trading_time(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(raw_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_to_trading_time(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_to_trading_time(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(current_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_to_trading_time(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_to_trading_time(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(current_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_to_trading_time(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-25 09:39:46 [INFO] [MarketDataFetcher_402749_main] - 处理完成，时间: 09:39:46, 股票数: 4394
2025-08-25 09:39:46 [INFO] [MarketDataFetcher_402749_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1740.37 毫秒，每秒处理股票数: 2524.76
2025-08-25 09:39:46 [INFO] [MarketDataFetcher_402749_main] - [目标] 本次获取和保存数据总耗时: 1.74s (网络: 1.74s, 处理: -0.00s, 数据库: 1.74s)
2025-08-25 09:39:46 [WARNING] [MarketDataFetcher_402749_main] - [警告] 总耗时 1.74s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 09:39:46 [WARNING] [MarketDataFetcher_402749_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-25 09:39:46 [ERROR] [MarketDataFetcher_402749_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-25 09:39:46 [ERROR] [MarketDataFetcher_402749_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_to_trading_time(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(raw_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_to_trading_time(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_to_trading_time(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(current_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_to_trading_time(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_to_trading_time(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(current_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_to_trading_time(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-25 09:39:46 [INFO] [MarketDataFetcher_402749_main] - 处理完成，时间: 09:39:46, 股票数: 4394
2025-08-25 09:39:46 [INFO] [MarketDataFetcher_402749_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 705.66 毫秒，每秒处理股票数: 6226.84
2025-08-25 09:39:46 [INFO] [MarketDataFetcher_402749_main] - [目标] 本次获取和保存数据总耗时: 0.71s (网络: 0.71s, 处理: -0.00s, 数据库: 0.71s)
2025-08-25 09:39:48 [WARNING] [MarketDataFetcher_402749_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-25 09:39:48 [ERROR] [MarketDataFetcher_402749_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-25 09:39:48 [ERROR] [MarketDataFetcher_402749_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_to_trading_time(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(raw_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_to_trading_time(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_to_trading_time(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(current_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_to_trading_time(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_to_trading_time(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(current_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_to_trading_time(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-25 09:39:48 [INFO] [MarketDataFetcher_402749_main] - 处理完成，时间: 09:39:48, 股票数: 4394
2025-08-25 09:39:48 [INFO] [MarketDataFetcher_402749_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 831.22 毫秒，每秒处理股票数: 5286.22
2025-08-25 09:39:48 [INFO] [MarketDataFetcher_402749_main] - [目标] 本次获取和保存数据总耗时: 0.83s (网络: 0.83s, 处理: -0.00s, 数据库: 0.83s)
2025-08-25 09:39:50 [WARNING] [MarketDataFetcher_402749_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-25 09:39:50 [ERROR] [MarketDataFetcher_402749_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-25 09:39:50 [ERROR] [MarketDataFetcher_402749_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_to_trading_time(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(raw_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_to_trading_time(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_to_trading_time(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(current_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_to_trading_time(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2464, in _adjust_to_trading_time_cached
    adjusted_time = self._adjust_to_trading_time(timestamp)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time_cached(current_time)
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 2482, in _adjust_to_trading_time_cached
    return self._adjust_to_trading_time(timestamp)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-25 09:39:50 [INFO] [MarketDataFetcher_402749_main] - 处理完成，时间: 09:39:50, 股票数: 4394
2025-08-25 09:39:50 [INFO] [MarketDataFetcher_402749_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 647.18 毫秒，每秒处理股票数: 6789.48
2025-08-25 09:39:50 [INFO] [MarketDataFetcher_402749_main] - [目标] 本次获取和保存数据总耗时: 0.65s (网络: 0.65s, 处理: -0.00s, 数据库: 0.65s)
