﻿2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - 成功加载配置文件: config/main.toml
2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - 开始加载股票列表...
2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - 正在从数据库加载股票列表...
2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - [初始化] 股票列表加载完成: 4394 只股票
2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - [初始化] 有效股票代码: 4394 个
2025-08-25 10:01:11 [INFO] [MarketDataFetcher_429787_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-08-25 10:01:12 [INFO] [MarketDataFetcher_429787_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-08-25 10:01:12 [INFO] [MarketDataFetcher_429787_main] - 正在初始化数据库管理器...
2025-08-25 10:01:12 [INFO] [MarketDataFetcher_429787_main] - [成功] 数据库管理器初始化成功
2025-08-25 10:01:12 [INFO] [MarketDataFetcher_429787_main] - 📦 使用数据库缓存机制
2025-08-25 10:01:12 [INFO] [MarketDataFetcher_429787_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-08-25 10:01:12 [INFO] [MarketDataFetcher_429787_main] - 开始加载股票列表...
2025-08-25 10:01:12 [INFO] [MarketDataFetcher_429787_main] - 正在从数据库加载股票列表...
2025-08-25 10:01:12 [INFO] [MarketDataFetcher_429787_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 10:01:12 [INFO] [MarketDataFetcher_429787_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 10:01:12 [INFO] [MarketDataFetcher_429787_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 10:01:12 [INFO] [MarketDataFetcher_429787_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 10:01:12 [INFO] [MarketDataFetcher_429787_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 10:01:12 [INFO] [MarketDataFetcher_429787_main] - 定时任务线程启动
2025-08-25 10:01:14 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 4390/4390 条Tick数据
2025-08-25 10:01:14 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:14 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:14 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:14 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 4390 条记录，耗时: 0.18秒
2025-08-25 10:01:14 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:14, 股票数: 4394
2025-08-25 10:01:14 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 2062.35 毫秒，每秒处理股票数: 2130.58
2025-08-25 10:01:14 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 2.08s (网络: 2.08s, 处理: -0.00s, 数据库: 2.08s)
2025-08-25 10:01:14 [WARNING] [MarketDataFetcher_429787_main] - [警告] 总耗时 2.08s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 10:01:14 [WARNING] [MarketDataFetcher_429787_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 10:01:15 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 1886/1886 条Tick数据
2025-08-25 10:01:15 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:15 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:15 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:15 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 1886 条记录，耗时: 0.17秒
2025-08-25 10:01:15 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:15, 股票数: 4394
2025-08-25 10:01:15 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1053.23 毫秒，每秒处理股票数: 4171.93
2025-08-25 10:01:15 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 1.05s (网络: 1.05s, 处理: -0.00s, 数据库: 1.05s)
2025-08-25 10:01:16 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 1825/1825 条Tick数据
2025-08-25 10:01:16 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:16 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:16 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:16 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 1825 条记录，耗时: 0.19秒
2025-08-25 10:01:16 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:16, 股票数: 4394
2025-08-25 10:01:16 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 991.25 毫秒，每秒处理股票数: 4432.80
2025-08-25 10:01:17 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 1.00s (网络: 1.00s, 处理: -0.00s, 数据库: 1.00s)
2025-08-25 10:01:18 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 1785/1785 条Tick数据
2025-08-25 10:01:18 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:18 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:18 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:18 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 1785 条记录，耗时: 0.13秒
2025-08-25 10:01:18 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:18, 股票数: 4394
2025-08-25 10:01:18 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 865.52 毫秒，每秒处理股票数: 5076.72
2025-08-25 10:01:18 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 0.87s (网络: 0.87s, 处理: -0.00s, 数据库: 0.87s)
2025-08-25 10:01:21 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 2936/2936 条Tick数据
2025-08-25 10:01:21 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:21 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:21 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:21 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 2936 条记录，耗时: 0.13秒
2025-08-25 10:01:21 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:20, 股票数: 4394
2025-08-25 10:01:21 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1001.91 毫秒，每秒处理股票数: 4385.61
2025-08-25 10:01:21 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 1.00s (网络: 1.00s, 处理: -0.00s, 数据库: 1.00s)
2025-08-25 10:01:22 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 2171/2171 条Tick数据
2025-08-25 10:01:22 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:22 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:22 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:22 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 2171 条记录，耗时: 0.14秒
2025-08-25 10:01:22 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:22, 股票数: 4394
2025-08-25 10:01:22 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 900.98 毫秒，每秒处理股票数: 4876.89
2025-08-25 10:01:22 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 0.90s (网络: 0.90s, 处理: -0.00s, 数据库: 0.90s)
2025-08-25 10:01:24 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 1740/1740 条Tick数据
2025-08-25 10:01:24 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:24 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:24 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:24 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 1740 条记录，耗时: 0.11秒
2025-08-25 10:01:24 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:24, 股票数: 4394
2025-08-25 10:01:24 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 921.86 毫秒，每秒处理股票数: 4766.47
2025-08-25 10:01:24 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 0.92s (网络: 0.92s, 处理: -0.00s, 数据库: 0.92s)
2025-08-25 10:01:26 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 2985/2985 条Tick数据
2025-08-25 10:01:26 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:26 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:26 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:26 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 2985 条记录，耗时: 0.13秒
2025-08-25 10:01:26 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:26, 股票数: 4394
2025-08-25 10:01:26 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 897.17 毫秒，每秒处理股票数: 4897.61
2025-08-25 10:01:26 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 0.90s (网络: 0.90s, 处理: -0.00s, 数据库: 0.90s)
2025-08-25 10:01:28 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 2083/2083 条Tick数据
2025-08-25 10:01:28 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:28 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:28 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:28 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 2083 条记录，耗时: 0.13秒
2025-08-25 10:01:28 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:28, 股票数: 4394
2025-08-25 10:01:28 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 866.88 毫秒，每秒处理股票数: 5068.75
2025-08-25 10:01:28 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 0.87s (网络: 0.87s, 处理: -0.00s, 数据库: 0.87s)
2025-08-25 10:01:30 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 1749/1749 条Tick数据
2025-08-25 10:01:30 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:30 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:30 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:30 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 1749 条记录，耗时: 0.11秒
2025-08-25 10:01:30 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:30, 股票数: 4394
2025-08-25 10:01:30 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 974.05 毫秒，每秒处理股票数: 4511.07
2025-08-25 10:01:30 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 0.97s (网络: 0.97s, 处理: 0.00s, 数据库: 0.97s)
2025-08-25 10:01:32 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 2833/2833 条Tick数据
2025-08-25 10:01:32 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:32 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:32 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:32 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 2833 条记录，耗时: 0.11秒
2025-08-25 10:01:32 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:32, 股票数: 4394
2025-08-25 10:01:32 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 802.27 毫秒，每秒处理股票数: 5476.95
2025-08-25 10:01:32 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 0.80s (网络: 0.80s, 处理: 0.00s, 数据库: 0.80s)
2025-08-25 10:01:34 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 2332/2332 条Tick数据
2025-08-25 10:01:34 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:34 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:34 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:34 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 2332 条记录，耗时: 0.12秒
2025-08-25 10:01:34 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:34, 股票数: 4394
2025-08-25 10:01:34 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 734.20 毫秒，每秒处理股票数: 5984.77
2025-08-25 10:01:34 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 0.73s (网络: 0.73s, 处理: -0.00s, 数据库: 0.73s)
2025-08-25 10:01:36 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 1609/1609 条Tick数据
2025-08-25 10:01:36 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:36 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:36 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:36 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 1609 条记录，耗时: 0.11秒
2025-08-25 10:01:36 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:36, 股票数: 4394
2025-08-25 10:01:36 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 889.97 毫秒，每秒处理股票数: 4937.24
2025-08-25 10:01:36 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 0.89s (网络: 0.89s, 处理: -0.00s, 数据库: 0.89s)
2025-08-25 10:01:38 [INFO] [MarketDataFetcher_429787_main] - [成功] 成功保存 2849/2849 条Tick数据
2025-08-25 10:01:38 [INFO] [MarketDataFetcher_429787_main] - [启动] 性能优化报告:
2025-08-25 10:01:38 [INFO] [MarketDataFetcher_429787_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 10:01:38 [INFO] [MarketDataFetcher_429787_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 10:01:38 [INFO] [MarketDataFetcher_429787_main] - 保存Tick数据完成，共 2849 条记录，耗时: 0.12秒
2025-08-25 10:01:38 [INFO] [MarketDataFetcher_429787_main] - 处理完成，时间: 10:01:38, 股票数: 4394
2025-08-25 10:01:38 [INFO] [MarketDataFetcher_429787_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 772.01 毫秒，每秒处理股票数: 5691.63
2025-08-25 10:01:38 [INFO] [MarketDataFetcher_429787_main] - [目标] 本次获取和保存数据总耗时: 0.77s (网络: 0.77s, 处理: -0.00s, 数据库: 0.77s)
