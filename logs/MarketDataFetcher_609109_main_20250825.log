﻿2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - 成功加载配置文件: config/main.toml
2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - 开始加载股票列表...
2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - 正在从数据库加载股票列表...
2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - [初始化] 股票列表加载完成: 4394 只股票
2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - [初始化] 有效股票代码: 4394 个
2025-08-25 13:45:18 [INFO] [MarketDataFetcher_609109_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-08-25 13:45:19 [INFO] [MarketDataFetcher_609109_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-08-25 13:45:19 [INFO] [MarketDataFetcher_609109_main] - 正在初始化数据库管理器...
2025-08-25 13:45:19 [INFO] [MarketDataFetcher_609109_main] - [成功] 数据库管理器初始化成功
2025-08-25 13:45:19 [INFO] [MarketDataFetcher_609109_main] - 📦 使用数据库缓存机制
2025-08-25 13:45:19 [INFO] [MarketDataFetcher_609109_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-08-25 13:45:19 [INFO] [MarketDataFetcher_609109_main] - 开始加载股票列表...
2025-08-25 13:45:19 [INFO] [MarketDataFetcher_609109_main] - 正在从数据库加载股票列表...
2025-08-25 13:45:19 [INFO] [MarketDataFetcher_609109_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 13:45:19 [INFO] [MarketDataFetcher_609109_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 13:45:19 [INFO] [MarketDataFetcher_609109_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 13:45:19 [INFO] [MarketDataFetcher_609109_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 13:45:19 [INFO] [MarketDataFetcher_609109_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 13:45:19 [INFO] [MarketDataFetcher_609109_main] - 定时任务线程启动
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - [调试] 处理前数据量: 4394
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - [调试] 处理后数据量: 4394
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - [成功] 成功保存 4394/4394 条Tick数据
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - [启动] 性能优化报告:
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - 保存Tick数据完成，共 4394 条记录，耗时: 0.16秒
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - 处理完成，时间: 13:45:21, 股票数: 4394
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1933.70 毫秒，每秒处理股票数: 2272.33
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - [目标] 本次获取和保存数据总耗时: 1.93s (网络: 1.93s, 处理: -0.00s, 数据库: 1.93s)
2025-08-25 13:45:21 [WARNING] [MarketDataFetcher_609109_main] - [警告] 总耗时 1.93s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 13:45:21 [WARNING] [MarketDataFetcher_609109_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - [调试] 处理前数据量: 4394
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - [调试] 处理后数据量: 1295
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - [调试] 开始验证 5689 条tick数据
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:45:08'), 'stock_code': '000523', 'price': 3.34, 'volume': 197329, 'amount': 65928596.0, 'open': 3.3200000000000003, 'high': 3.37, 'low': 3.31, 'last_close': 3.33, 'cur_vol': 11, 'change': 0.0, 'bid1': 3.34, 'ask1': 3.35, 'bid_vol1': 3002, 'ask_vol1': 11815, 'bid2': 3.33, 'ask2': 3.36, 'bid_vol2': 7584, 'ask_vol2': 15688, 'bid3': 3.3200000000000003, 'ask3': 3.37, 'bid_vol3': 8736, 'ask_vol3': 12228, 'bid4': 3.31, 'ask4': 3.38, 'bid_vol4': 6113, 'ask_vol4': 7722, 'bid5': 3.3000000000000003, 'ask5': 3.39, 'bid_vol5': 7106, 'ask_vol5': 6306}
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:45:02'), 'stock_code': '000524', 'price': 13.27, 'volume': 159685, 'amount': 211173984.0, 'open': 13.11, 'high': 13.39, 'low': 13.07, 'last_close': 13.1, 'cur_vol': 17, 'change': 0.0, 'bid1': 13.27, 'ask1': 13.280000000000001, 'bid_vol1': 42, 'ask_vol1': 178, 'bid2': 13.26, 'ask2': 13.290000000000001, 'bid_vol2': 273, 'ask_vol2': 430, 'bid3': 13.25, 'ask3': 13.3, 'bid_vol3': 407, 'ask_vol3': 480, 'bid4': 13.24, 'ask4': 13.31, 'bid_vol4': 797, 'ask_vol4': 255, 'bid5': 13.23, 'ask5': 13.32, 'bid_vol5': 488, 'ask_vol5': 106}
2025-08-25 13:45:21 [INFO] [MarketDataFetcher_609109_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:45:06'), 'stock_code': '000525', 'price': 6.96, 'volume': 203028, 'amount': 141790192.0, 'open': 7.0200000000000005, 'high': 7.0200000000000005, 'low': 6.95, 'last_close': 7.0200000000000005, 'cur_vol': 4, 'change': 0.0, 'bid1': 6.96, 'ask1': 6.97, 'bid_vol1': 7966, 'ask_vol1': 1362, 'bid2': 6.95, 'ask2': 6.98, 'bid_vol2': 12110, 'ask_vol2': 3214, 'bid3': 6.94, 'ask3': 6.99, 'bid_vol3': 2287, 'ask_vol3': 2481, 'bid4': 6.93, 'ask4': 7.0, 'bid_vol4': 3012, 'ask_vol4': 5058, 'bid5': 6.92, 'ask5': 7.01, 'bid_vol5': 1556, 'ask_vol5': 4818}
2025-08-25 13:45:22 [INFO] [MarketDataFetcher_609109_main] - [调试] 验证完成: 原始=5689, 有效=5685, 无效=4
2025-08-25 13:45:22 [WARNING] [MarketDataFetcher_609109_main] - 过滤掉 4 条无效数据
2025-08-25 13:45:22 [INFO] [MarketDataFetcher_609109_main] - [调试] 准备插入 5685 条数据到stock_tick_data表
2025-08-25 13:45:22 [INFO] [MarketDataFetcher_609109_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:45:08'), 'stock_code': '000523', 'price': 3.34, 'volume': 197329, 'amount': 65928596.0, 'open': 3.3200000000000003, 'high': 3.37, 'low': 3.31, 'last_close': 3.33, 'cur_vol': 11, 'change': 0.0, 'bid1': 3.34, 'ask1': 3.35, 'bid_vol1': 3002, 'ask_vol1': 11815, 'bid2': 3.33, 'ask2': 3.36, 'bid_vol2': 7584, 'ask_vol2': 15688, 'bid3': 3.3200000000000003, 'ask3': 3.37, 'bid_vol3': 8736, 'ask_vol3': 12228, 'bid4': 3.31, 'ask4': 3.38, 'bid_vol4': 6113, 'ask_vol4': 7722, 'bid5': 3.3000000000000003, 'ask5': 3.39, 'bid_vol5': 7106, 'ask_vol5': 6306}
2025-08-25 13:49:35 [ERROR] [MarketDataFetcher_609109_main] - [调试] 数据库管理器插入失败: 5685 条数据
2025-08-25 13:49:35 [ERROR] [MarketDataFetcher_609109_main] - [失败] 缓冲区刷新失败: 5689 条数据
2025-08-25 13:49:35 [ERROR] [MarketDataFetcher_609109_main] - [失败] 保存Tick数据失败
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_609109_main] - [启动] 性能优化报告:
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_609109_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_609109_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_609109_main] - 保存Tick数据完成，共 1295 条记录，耗时: 253.66秒
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_609109_main] - 处理完成，时间: 13:45:21, 股票数: 4394
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_609109_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 254365.46 毫秒，每秒处理股票数: 17.27
2025-08-25 13:49:35 [INFO] [MarketDataFetcher_609109_main] - [目标] 本次获取和保存数据总耗时: 254.37s (网络: 254.37s, 处理: -0.00s, 数据库: 254.37s)
2025-08-25 13:49:35 [WARNING] [MarketDataFetcher_609109_main] - [警告] 总耗时 254.37s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 13:49:35 [WARNING] [MarketDataFetcher_609109_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 13:49:36 [INFO] [MarketDataFetcher_609109_main] - [调试] 处理前数据量: 4394
2025-08-25 13:49:36 [INFO] [MarketDataFetcher_609109_main] - [调试] 处理后数据量: 4390
2025-08-25 13:49:36 [INFO] [MarketDataFetcher_609109_main] - [调试] 开始验证 4390 条tick数据
2025-08-25 13:49:36 [INFO] [MarketDataFetcher_609109_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:48:24'), 'stock_code': '300620', 'price': 91.48, 'volume': 265391, 'amount': 2453921024.0, 'open': 91.66, 'high': 95.68, 'low': 90.17, 'last_close': 88.82000000000001, 'cur_vol': 38, 'change': 0.5300000000000011, 'bid1': 91.47, 'ask1': 91.48, 'bid_vol1': 8, 'ask_vol1': 230, 'bid2': 91.46000000000001, 'ask2': 91.49, 'bid_vol2': 128, 'ask_vol2': 10, 'bid3': 91.45, 'ask3': 91.5, 'bid_vol3': 19, 'ask_vol3': 7, 'bid4': 91.44, 'ask4': 91.55, 'bid_vol4': 5, 'ask_vol4': 1, 'bid5': 91.42, 'ask5': 91.58, 'bid_vol5': 5, 'ask_vol5': 2}
2025-08-25 13:49:36 [INFO] [MarketDataFetcher_609109_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:49:06'), 'stock_code': '300621', 'price': 9.83, 'volume': 121128, 'amount': 120233704.0, 'open': 9.68, 'high': 10.13, 'low': 9.65, 'last_close': 9.53, 'cur_vol': 38, 'change': 0.009999999999999787, 'bid1': 9.83, 'ask1': 9.84, 'bid_vol1': 225, 'ask_vol1': 103, 'bid2': 9.82, 'ask2': 9.85, 'bid_vol2': 62, 'ask_vol2': 52, 'bid3': 9.81, 'ask3': 9.86, 'bid_vol3': 87, 'ask_vol3': 113, 'bid4': 9.8, 'ask4': 9.870000000000001, 'bid_vol4': 216, 'ask_vol4': 15, 'bid5': 9.790000000000001, 'ask5': 9.88, 'bid_vol5': 48, 'ask_vol5': 40}
2025-08-25 13:49:36 [INFO] [MarketDataFetcher_609109_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:48:57'), 'stock_code': '300622', 'price': 37.08, 'volume': 192180, 'amount': 709322944.0, 'open': 36.54, 'high': 37.800000000000004, 'low': 36.27, 'last_close': 36.300000000000004, 'cur_vol': 19, 'change': 0.0899999999999963, 'bid1': 37.07, 'ask1': 37.08, 'bid_vol1': 58, 'ask_vol1': 31, 'bid2': 37.06, 'ask2': 37.09, 'bid_vol2': 27, 'ask_vol2': 143, 'bid3': 37.050000000000004, 'ask3': 37.1, 'bid_vol3': 50, 'ask_vol3': 71, 'bid4': 37.04, 'ask4': 37.12, 'bid_vol4': 23, 'ask_vol4': 65, 'bid5': 37.03, 'ask5': 37.13, 'bid_vol5': 28, 'ask_vol5': 23}
2025-08-25 13:49:36 [INFO] [MarketDataFetcher_609109_main] - [调试] 验证完成: 原始=4390, 有效=4390, 无效=0
2025-08-25 13:49:36 [INFO] [MarketDataFetcher_609109_main] - [调试] 准备插入 4390 条数据到stock_tick_data表
2025-08-25 13:49:36 [INFO] [MarketDataFetcher_609109_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 13:48:24'), 'stock_code': '300620', 'price': 91.48, 'volume': 265391, 'amount': 2453921024.0, 'open': 91.66, 'high': 95.68, 'low': 90.17, 'last_close': 88.82000000000001, 'cur_vol': 38, 'change': 0.5300000000000011, 'bid1': 91.47, 'ask1': 91.48, 'bid_vol1': 8, 'ask_vol1': 230, 'bid2': 91.46000000000001, 'ask2': 91.49, 'bid_vol2': 128, 'ask_vol2': 10, 'bid3': 91.45, 'ask3': 91.5, 'bid_vol3': 19, 'ask_vol3': 7, 'bid4': 91.44, 'ask4': 91.55, 'bid_vol4': 5, 'ask_vol4': 1, 'bid5': 91.42, 'ask5': 91.58, 'bid_vol5': 5, 'ask_vol5': 2}
