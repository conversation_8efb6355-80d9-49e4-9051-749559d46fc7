﻿2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 成功加载配置文件: config/main.toml
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 开始加载股票列表...
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 正在从数据库加载股票列表...
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - [初始化] 股票列表加载完成: 4394 只股票
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - [初始化] 有效股票代码: 4394 个
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 正在初始化数据库管理器...
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - [成功] 数据库管理器初始化成功
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 📦 使用数据库缓存机制
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 开始加载股票列表...
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 正在从数据库加载股票列表...
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 15:05:31 [INFO] [MarketDataFetcher_17659_main] - 定时任务线程启动
2025-08-25 15:05:33 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:33 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 4394
2025-08-25 15:05:33 [INFO] [MarketDataFetcher_17659_main] - [调试] 开始验证 4394 条tick数据
2025-08-25 15:05:33 [INFO] [MarketDataFetcher_17659_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 14:59:55'), 'stock_code': '000001', 'price': 12.450000000000001, 'volume': 3045087, 'amount': 3761288960.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 13828, 'change': 0.0, 'bid1': 12.450000000000001, 'ask1': 12.46, 'bid_vol1': 259, 'ask_vol1': 7466, 'bid2': 12.44, 'ask2': 12.47, 'bid_vol2': 5266, 'ask_vol2': 4096, 'bid3': 12.43, 'ask3': 12.48, 'bid_vol3': 3738, 'ask_vol3': 7977, 'bid4': 12.42, 'ask4': 12.49, 'bid_vol4': 6220, 'ask_vol4': 5876, 'bid5': 12.41, 'ask5': 12.5, 'bid_vol5': 5366, 'ask_vol5': 17778}
2025-08-25 15:05:33 [INFO] [MarketDataFetcher_17659_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 14:59:57'), 'stock_code': '000002', 'price': 7.16, 'volume': 7923897, 'amount': 5619264512.0, 'open': 6.66, 'high': 7.22, 'low': 6.65, 'last_close': 6.5600000000000005, 'cur_vol': 38732, 'change': 0.0, 'bid1': 7.15, 'ask1': 7.16, 'bid_vol1': 22269, 'ask_vol1': 19333, 'bid2': 7.140000000000001, 'ask2': 7.17, 'bid_vol2': 16169, 'ask_vol2': 11914, 'bid3': 7.13, 'ask3': 7.18, 'bid_vol3': 9583, 'ask_vol3': 14041, 'bid4': 7.12, 'ask4': 7.19, 'bid_vol4': 13287, 'ask_vol4': 11513, 'bid5': 7.11, 'ask5': 7.2, 'bid_vol5': 10242, 'ask_vol5': 24643}
2025-08-25 15:05:33 [INFO] [MarketDataFetcher_17659_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 14:59:57'), 'stock_code': '000006', 'price': 7.33, 'volume': 622575, 'amount': 454451488.0, 'open': 7.29, 'high': 7.45, 'low': 7.15, 'last_close': 7.3, 'cur_vol': 6840, 'change': 0.0, 'bid1': 7.33, 'ask1': 7.34, 'bid_vol1': 590, 'ask_vol1': 1359, 'bid2': 7.32, 'ask2': 7.3500000000000005, 'bid_vol2': 1495, 'ask_vol2': 1974, 'bid3': 7.3100000000000005, 'ask3': 7.36, 'bid_vol3': 766, 'ask_vol3': 2174, 'bid4': 7.3, 'ask4': 7.37, 'bid_vol4': 2293, 'ask_vol4': 129, 'bid5': 7.29, 'ask5': 7.38, 'bid_vol5': 505, 'ask_vol5': 1036}
2025-08-25 15:05:34 [INFO] [MarketDataFetcher_17659_main] - [调试] 验证完成: 原始=4394, 有效=4390, 无效=4
2025-08-25 15:05:34 [WARNING] [MarketDataFetcher_17659_main] - 过滤掉 4 条无效数据
2025-08-25 15:05:34 [INFO] [MarketDataFetcher_17659_main] - [调试] 准备插入 4390 条数据到stock_tick_data表
2025-08-25 15:05:34 [INFO] [MarketDataFetcher_17659_main] - [调试] 样本数据: {'trade_time': Timestamp('2025-08-25 14:59:55'), 'stock_code': '000001', 'price': 12.450000000000001, 'volume': 3045087, 'amount': 3761288960.0, 'open': 12.120000000000001, 'high': 12.530000000000001, 'low': 12.1, 'last_close': 12.06, 'cur_vol': 13828, 'change': 0.0, 'bid1': 12.450000000000001, 'ask1': 12.46, 'bid_vol1': 259, 'ask_vol1': 7466, 'bid2': 12.44, 'ask2': 12.47, 'bid_vol2': 5266, 'ask_vol2': 4096, 'bid3': 12.43, 'ask3': 12.48, 'bid_vol3': 3738, 'ask_vol3': 7977, 'bid4': 12.42, 'ask4': 12.49, 'bid_vol4': 6220, 'ask_vol4': 5876, 'bid5': 12.41, 'ask5': 12.5, 'bid_vol5': 5366, 'ask_vol5': 17778}
2025-08-25 15:05:34 [INFO] [MarketDataFetcher_17659_main] - [调试] 数据库插入成功: 4390 条数据
2025-08-25 15:05:34 [INFO] [MarketDataFetcher_17659_main] - [成功] 成功保存 4394/4394 条Tick数据
2025-08-25 15:05:34 [INFO] [MarketDataFetcher_17659_main] - [启动] 性能优化报告:
2025-08-25 15:05:34 [INFO] [MarketDataFetcher_17659_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 15:05:34 [INFO] [MarketDataFetcher_17659_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 15:05:34 [INFO] [MarketDataFetcher_17659_main] - 保存Tick数据完成，共 4394 条记录，耗时: 0.62秒
2025-08-25 15:05:34 [INFO] [MarketDataFetcher_17659_main] - 处理完成，时间: 15:05:33, 股票数: 4394
2025-08-25 15:05:34 [INFO] [MarketDataFetcher_17659_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 2501.18 毫秒，每秒处理股票数: 1756.77
2025-08-25 15:05:34 [INFO] [MarketDataFetcher_17659_main] - [目标] 本次获取和保存数据总耗时: 2.50s (网络: 2.50s, 处理: -0.00s, 数据库: 2.50s)
2025-08-25 15:05:34 [WARNING] [MarketDataFetcher_17659_main] - [警告] 总耗时 2.50s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 15:05:34 [WARNING] [MarketDataFetcher_17659_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 15:05:35 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:35 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 0
2025-08-25 15:05:35 [WARNING] [MarketDataFetcher_17659_main] - [调试] 过滤后没有有效的Tick数据需要保存
2025-08-25 15:05:36 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:36 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 0
2025-08-25 15:05:36 [WARNING] [MarketDataFetcher_17659_main] - [调试] 过滤后没有有效的Tick数据需要保存
2025-08-25 15:05:38 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:38 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 0
2025-08-25 15:05:38 [WARNING] [MarketDataFetcher_17659_main] - [调试] 过滤后没有有效的Tick数据需要保存
2025-08-25 15:05:40 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:40 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 0
2025-08-25 15:05:40 [WARNING] [MarketDataFetcher_17659_main] - [调试] 过滤后没有有效的Tick数据需要保存
2025-08-25 15:05:42 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:42 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 0
2025-08-25 15:05:42 [WARNING] [MarketDataFetcher_17659_main] - [调试] 过滤后没有有效的Tick数据需要保存
2025-08-25 15:05:44 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:44 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 0
2025-08-25 15:05:44 [WARNING] [MarketDataFetcher_17659_main] - [调试] 过滤后没有有效的Tick数据需要保存
2025-08-25 15:05:46 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:46 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 0
2025-08-25 15:05:46 [WARNING] [MarketDataFetcher_17659_main] - [调试] 过滤后没有有效的Tick数据需要保存
2025-08-25 15:05:48 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:48 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 0
2025-08-25 15:05:48 [WARNING] [MarketDataFetcher_17659_main] - [调试] 过滤后没有有效的Tick数据需要保存
2025-08-25 15:05:50 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:50 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 0
2025-08-25 15:05:50 [WARNING] [MarketDataFetcher_17659_main] - [调试] 过滤后没有有效的Tick数据需要保存
2025-08-25 15:05:52 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:52 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 0
2025-08-25 15:05:52 [WARNING] [MarketDataFetcher_17659_main] - [调试] 过滤后没有有效的Tick数据需要保存
2025-08-25 15:05:54 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:54 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 0
2025-08-25 15:05:54 [WARNING] [MarketDataFetcher_17659_main] - [调试] 过滤后没有有效的Tick数据需要保存
2025-08-25 15:05:56 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:56 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 0
2025-08-25 15:05:56 [WARNING] [MarketDataFetcher_17659_main] - [调试] 过滤后没有有效的Tick数据需要保存
2025-08-25 15:05:58 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理前数据量: 4394
2025-08-25 15:05:58 [INFO] [MarketDataFetcher_17659_main] - [调试] 处理后数据量: 0
2025-08-25 15:05:58 [WARNING] [MarketDataFetcher_17659_main] - [调试] 过滤后没有有效的Tick数据需要保存
