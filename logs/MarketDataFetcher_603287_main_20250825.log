﻿2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - 成功加载配置文件: config/main.toml
2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - 开始加载股票列表...
2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - 正在从数据库加载股票列表...
2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - [初始化] 股票列表加载完成: 4394 只股票
2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - [初始化] 有效股票代码: 4394 个
2025-08-25 13:41:03 [INFO] [MarketDataFetcher_603287_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-08-25 13:41:04 [INFO] [MarketDataFetcher_603287_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-08-25 13:41:04 [INFO] [MarketDataFetcher_603287_main] - 正在初始化数据库管理器...
2025-08-25 13:41:04 [INFO] [MarketDataFetcher_603287_main] - [成功] 数据库管理器初始化成功
2025-08-25 13:41:04 [INFO] [MarketDataFetcher_603287_main] - 📦 使用数据库缓存机制
2025-08-25 13:41:04 [INFO] [MarketDataFetcher_603287_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-08-25 13:41:04 [INFO] [MarketDataFetcher_603287_main] - 开始加载股票列表...
2025-08-25 13:41:04 [INFO] [MarketDataFetcher_603287_main] - 正在从数据库加载股票列表...
2025-08-25 13:41:04 [INFO] [MarketDataFetcher_603287_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 13:41:04 [INFO] [MarketDataFetcher_603287_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 13:41:04 [INFO] [MarketDataFetcher_603287_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 13:41:04 [INFO] [MarketDataFetcher_603287_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 13:41:04 [INFO] [MarketDataFetcher_603287_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 13:41:04 [INFO] [MarketDataFetcher_603287_main] - 定时任务线程启动
2025-08-25 13:41:05 [INFO] [MarketDataFetcher_603287_main] - [调试] 处理前数据量: 4394
2025-08-25 13:41:05 [INFO] [MarketDataFetcher_603287_main] - [调试] 处理后数据量: 4394
2025-08-25 13:41:05 [INFO] [MarketDataFetcher_603287_main] - [成功] 成功保存 4394/4394 条Tick数据
2025-08-25 13:41:05 [INFO] [MarketDataFetcher_603287_main] - [启动] 性能优化报告:
2025-08-25 13:41:05 [INFO] [MarketDataFetcher_603287_main] -    缓存: 大小=0, 命中率=0.0%
2025-08-25 13:41:05 [INFO] [MarketDataFetcher_603287_main] -    批处理: 缓冲区=0, 阈值=5000
2025-08-25 13:41:05 [INFO] [MarketDataFetcher_603287_main] - 保存Tick数据完成，共 4394 条记录，耗时: 0.13秒
2025-08-25 13:41:05 [INFO] [MarketDataFetcher_603287_main] - 处理完成，时间: 13:41:05, 股票数: 4394
2025-08-25 13:41:05 [INFO] [MarketDataFetcher_603287_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 1863.41 毫秒，每秒处理股票数: 2358.04
2025-08-25 13:41:05 [INFO] [MarketDataFetcher_603287_main] - [目标] 本次获取和保存数据总耗时: 1.86s (网络: 1.86s, 处理: -0.00s, 数据库: 1.86s)
2025-08-25 13:41:05 [WARNING] [MarketDataFetcher_603287_main] - [警告] 总耗时 1.86s 接近或超过获取间隔 2s，可能无法保证每 2s 获取一次数据
2025-08-25 13:41:05 [WARNING] [MarketDataFetcher_603287_main] - [修复] 建议：考虑增加获取间隔或优化网络连接
2025-08-25 13:41:06 [INFO] [MarketDataFetcher_603287_main] - [调试] 处理前数据量: 4394
2025-08-25 13:41:06 [INFO] [MarketDataFetcher_603287_main] - [调试] 处理后数据量: 1740
2025-08-25 13:41:06 [INFO] [MarketDataFetcher_603287_main] - [调试] 开始验证 6134 条tick数据
2025-08-25 13:41:06 [INFO] [MarketDataFetcher_603287_main] - [调试] 数据1验证通过: {'trade_time': Timestamp('2025-08-25 13:40:59'), 'stock_code': '000523', 'price': 3.34, 'volume': 194477, 'amount': 64976500.0, 'open': 3.3200000000000003, 'high': 3.37, 'low': 3.31, 'last_close': 3.33, 'cur_vol': 37, 'change': 0.0, 'bid1': 3.33, 'ask1': 3.34, 'bid_vol1': 8607, 'ask_vol1': 2468, 'bid2': 3.3200000000000003, 'ask2': 3.35, 'bid_vol2': 8626, 'ask_vol2': 12056, 'bid3': 3.31, 'ask3': 3.36, 'bid_vol3': 6148, 'ask_vol3': 15184, 'bid4': 3.3000000000000003, 'ask4': 3.37, 'bid_vol4': 7106, 'ask_vol4': 12458, 'bid5': 3.29, 'ask5': 3.38, 'bid_vol5': 4091, 'ask_vol5': 7992}
2025-08-25 13:41:06 [INFO] [MarketDataFetcher_603287_main] - [调试] 数据2验证通过: {'trade_time': Timestamp('2025-08-25 13:40:53'), 'stock_code': '000524', 'price': 13.25, 'volume': 157702, 'amount': 208544320.0, 'open': 13.11, 'high': 13.39, 'low': 13.07, 'last_close': 13.1, 'cur_vol': 3, 'change': 0.0, 'bid1': 13.24, 'ask1': 13.25, 'bid_vol1': 436, 'ask_vol1': 278, 'bid2': 13.23, 'ask2': 13.26, 'bid_vol2': 484, 'ask_vol2': 133, 'bid3': 13.22, 'ask3': 13.27, 'bid_vol3': 620, 'ask_vol3': 203, 'bid4': 13.21, 'ask4': 13.280000000000001, 'bid_vol4': 553, 'ask_vol4': 197, 'bid5': 13.200000000000001, 'ask5': 13.290000000000001, 'bid_vol5': 571, 'ask_vol5': 180}
2025-08-25 13:41:06 [INFO] [MarketDataFetcher_603287_main] - [调试] 数据3验证通过: {'trade_time': Timestamp('2025-08-25 13:40:57'), 'stock_code': '000525', 'price': 6.97, 'volume': 201803, 'amount': 140936960.0, 'open': 7.0200000000000005, 'high': 7.0200000000000005, 'low': 6.95, 'last_close': 7.0200000000000005, 'cur_vol': 10, 'change': 0.0, 'bid1': 6.96, 'ask1': 6.97, 'bid_vol1': 7577, 'ask_vol1': 1106, 'bid2': 6.95, 'ask2': 6.98, 'bid_vol2': 11511, 'ask_vol2': 3185, 'bid3': 6.94, 'ask3': 6.99, 'bid_vol3': 2232, 'ask_vol3': 2341, 'bid4': 6.93, 'ask4': 7.0, 'bid_vol4': 3006, 'ask_vol4': 5154, 'bid5': 6.92, 'ask5': 7.01, 'bid_vol5': 1461, 'ask_vol5': 4963}
2025-08-25 13:41:07 [INFO] [MarketDataFetcher_603287_main] - [调试] 验证完成: 原始=6134, 有效=6130, 无效=4
2025-08-25 13:41:07 [WARNING] [MarketDataFetcher_603287_main] - 过滤掉 4 条无效数据
