2025-08-25 15:16:44,031 - market_data_fetcher_process - INFO - 创建市场数据获取器
2025-08-25 15:16:44,034 - processes.market_data_fetcher - INFO - 配置参数: fetch_interval=2, batch_size=50, max_retries=5, retry_delay=0.3, timeout=10
2025-08-25 15:16:44,034 - processes.market_data_fetcher - INFO - [架构] 使用TimescaleDB时序数据库，服务器: 10.8.8.8:6668
2025-08-25 15:16:44,034 - processes.market_data_fetcher - INFO - [数据] 数据将直接写入TimescaleDB，支持高性能时序数据存储和查询
2025-08-25 15:16:44,034 - processes.market_data_fetcher - INFO - 计算得出最优线程数: 16
2025-08-25 15:16:44,053 - data.db_manager - INFO - 健康检查线程已启动，检查间隔: 60秒
2025-08-25 15:16:44,054 - data.db_manager - INFO - 连接池初始化完成: 10.8.8.88:6668/xystock, 池大小: 2-30
2025-08-25 15:16:44,054 - data.db_manager - INFO - 默认数据库连接池已创建: 10.8.8.88:6668/xystock
2025-08-25 15:16:44,054 - data.db_manager - INFO - 统一数据库管理器初始化完成
2025-08-25 15:16:44,084 - processes.market_data_fetcher - INFO - 初始化客户端池，池大小: 16, 超时时间: 10秒
2025-08-25 15:16:44,084 - processes.market_data_fetcher - INFO - [系统优化] Debian 系统优化已启用: {'socket_keepalive': True, 'socket_nodelay': True, 'socket_timeout': 8.0, 'connection_pool_warmup': True, 'server_health_check': True, 'adaptive_timeout': True, 'use_ipv4_only': True, 'tcp_user_timeout': 15000}
2025-08-25 15:16:44,084 - processes.market_data_fetcher - INFO - 初始化连接池，大小: 16
2025-08-25 15:16:44,084 - processes.market_data_fetcher - INFO - 开始创建 3 个初始连接，超时时间: 3秒
2025-08-25 15:16:44,515 - processes.market_data_fetcher - INFO - 连接池初始化成功，预创建连接数: 3/3
2025-08-25 15:16:44,515 - processes.market_data_fetcher - INFO - 后台线程开始创建额外的 5 个连接...
2025-08-25 15:16:44,516 - processes.market_data_fetcher - INFO - 客户端池初始化完成，当前连接数: 3, 最大连接数: 16
2025-08-25 15:16:44,516 - processes.market_data_fetcher - INFO - 创建持久化线程池，最大线程数: 16
2025-08-25 15:16:44,518 - processes.market_data_fetcher - INFO - 市场数据获取器初始化完成
2025-08-25 15:16:44,518 - processes.market_data_fetcher - INFO - ✅ 序列化异步数据库写入线程池已启用（单线程，防死锁）
2025-08-25 15:16:44,518 - processes.market_data_fetcher - INFO - [性能优化] 性能优化配置已启用:
2025-08-25 15:16:44,518 - processes.market_data_fetcher - INFO -    - 缓存机制: 清理间隔 300秒
2025-08-25 15:16:44,518 - processes.market_data_fetcher - INFO -    - 批量写入: 阈值 5000, 间隔 0.8秒
2025-08-25 15:16:44,519 - processes.market_data_fetcher - INFO -    - 数据库优化: 批量大小 10000, 异步写入 True
2025-08-25 15:16:44,519 - processes.market_data_fetcher - INFO -    - 异步线程池: 已启用
2025-08-25 15:16:44,519 - market_data_fetcher_process - INFO - 市场数据获取器创建完成
2025-08-25 15:16:44,519 - market_data_fetcher_process - INFO - 启动市场数据获取器，强制启动模式
2025-08-25 15:16:44,519 - processes.market_data_fetcher - INFO - 市场数据获取器启动中...
2025-08-25 15:16:44,519 - processes.market_data_fetcher - INFO - 开始加载股票列表...
2025-08-25 15:16:44,552 - processes.market_data_fetcher - INFO - 成功加载 4394 只股票到 self.stock_list。
2025-08-25 15:16:44,552 - processes.market_data_fetcher - INFO - [简化] 使用简化架构：专注于tick数据获取和数据库存储
2025-08-25 15:16:44,552 - processes.market_data_fetcher - INFO - [线程] 正在启动 数据获取线程
2025-08-25 15:16:44,553 - processes.market_data_fetcher - INFO - [启动] 数据获取线程启动 - 底层优化版本
2025-08-25 15:16:44,553 - processes.market_data_fetcher - INFO - [线程] 数据获取线程 启动完成，线程ID: 140421684504256
2025-08-25 15:16:44,553 - processes.market_data_fetcher - INFO - 线程配置: 最大连续错误=5
2025-08-25 15:16:44,553 - processes.market_data_fetcher - INFO - [线程] 正在启动 定时任务线程
2025-08-25 15:16:44,553 - processes.market_data_fetcher - INFO - [线程] 定时任务线程 启动完成，线程ID: 140421338494656
2025-08-25 15:16:44,553 - processes.market_data_fetcher - INFO - 市场数据获取器启动完成
2025-08-25 15:16:44,554 - processes.market_data_fetcher - INFO - [成功] TimescaleDB连接池已在初始化时建立
2025-08-25 15:16:44,554 - processes.market_data_fetcher - INFO - [连接池] TimescaleDB连接池状态: 可用连接 2/0
2025-08-25 15:16:44,554 - processes.market_data_fetcher - INFO -    - 借用次数: 0, 归还次数: 0
2025-08-25 15:16:44,554 - processes.market_data_fetcher - INFO -    - 连接池状态: 异常
2025-08-25 15:16:44,554 - market_data_fetcher_process - INFO - 市场数据获取器启动完成
2025-08-25 15:16:44,555 - market_data_fetcher_process - INFO - 进入主循环，按Ctrl+C退出
2025-08-25 15:16:45,732 - processes.market_data_fetcher - INFO - 后台线程完成创建额外连接，成功创建: 5，当前连接池大小: 8
