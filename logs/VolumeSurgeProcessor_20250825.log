﻿2025-08-25 10:31:59 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 10:31:59 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 10:31:59 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 10:31:59 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 10:31:59 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 10:33:04 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 10:33:04 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 10:33:04 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 10:33:04 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 10:33:04 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 10:33:04 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 10:33:04 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 10:33:04 [ERROR] [VolumeSurgeProcessor] - ❌ 从数据库加载股票列表失败: 'change_percent'
2025-08-25 10:33:04 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [2, 1, 1, 1]
2025-08-25 10:33:04 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 10:33:04 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 2 只股票
2025-08-25 10:33:04 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 2 只股票 (模式: 数据库)
2025-08-25 10:33:05 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1 只股票
2025-08-25 10:33:05 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1 只股票 (模式: 数据库)
2025-08-25 10:33:05 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1 只股票
2025-08-25 10:33:05 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1 只股票 (模式: 数据库)
2025-08-25 10:33:05 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1 只股票
2025-08-25 10:33:05 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1 只股票 (模式: 数据库)
2025-08-25 10:33:05 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 10:33:05 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 10:33:05 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 10:33:05 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 5 只股票
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 10:52:37 [ERROR] [VolumeSurgeProcessor] - ❌ 从数据库加载股票列表失败: 'change_percent'
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [2, 1, 1, 1]
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 2 只股票
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 2 只股票 (模式: 数据库)
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1 只股票
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1 只股票 (模式: 数据库)
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1 只股票
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1 只股票 (模式: 数据库)
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1 只股票
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1 只股票 (模式: 数据库)
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 10:52:37 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 5 只股票
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 11:41:47 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 11:43:15 [INFO] [VolumeSurgeProcessor] - 接收到信号 2，正在停止处理器...
2025-08-25 11:43:15 [INFO] [VolumeSurgeProcessor] - 🛑 停止成交量激增处理器...
2025-08-25 11:43:15 [INFO] [VolumeSurgeProcessor] - 🛑 信号处理线程结束运行
2025-08-25 11:43:30 [INFO] [VolumeSurgeProcessor] - 接收到信号 2，正在停止处理器...
2025-08-25 11:43:35 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 11:43:35 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 11:43:35 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 11:43:35 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 11:43:35 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 11:43:35 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 11:44:22 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 11:45:00 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 进入午休休市暂停模式，将在 60.0 分钟后恢复运行
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 11:50:48 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 进入午休休市暂停模式，将在 22.2 分钟后恢复运行
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 进入午休休市暂停模式，将在 22.2 分钟后恢复运行
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 进入午休休市暂停模式，将在 22.2 分钟后恢复运行
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 进入午休休市暂停模式，将在 22.2 分钟后恢复运行
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 12:37:45 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 进入午休休市暂停模式，将在 22.0 分钟后恢复运行
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 进入午休休市暂停模式，将在 22.0 分钟后恢复运行
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 进入午休休市暂停模式，将在 22.0 分钟后恢复运行
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 进入午休休市暂停模式，将在 22.0 分钟后恢复运行
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 12:37:58 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 12:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 进入午休休市暂停模式，将在 15.0 分钟后恢复运行
2025-08-25 12:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 进入午休休市暂停模式，将在 15.0 分钟后恢复运行
2025-08-25 12:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 进入午休休市暂停模式，将在 15.0 分钟后恢复运行
2025-08-25 12:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 进入午休休市暂停模式，将在 15.0 分钟后恢复运行
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 进入午休休市暂停模式，将在 10.9 分钟后恢复运行
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 进入午休休市暂停模式，将在 10.9 分钟后恢复运行
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 进入午休休市暂停模式，将在 10.9 分钟后恢复运行
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 进入午休休市暂停模式，将在 10.9 分钟后恢复运行
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 12:49:08 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:01:00
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:06:49 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:07:00
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:08:00
2025-08-25 13:07:08 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:08:00
2025-08-25 13:09:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=10, 平均周期时间=0.174s, 最近周期时间=0.129s
2025-08-25 13:09:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=10, 平均周期时间=0.173s, 最近周期时间=0.138s
2025-08-25 13:09:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=10, 平均周期时间=0.176s, 最近周期时间=0.258s
2025-08-25 13:09:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=10, 平均周期时间=0.178s, 最近周期时间=0.266s
2025-08-25 13:15:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=10, 平均周期时间=0.207s, 最近周期时间=0.116s
2025-08-25 13:15:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=10, 平均周期时间=0.211s, 最近周期时间=0.367s
2025-08-25 13:15:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=10, 平均周期时间=0.217s, 最近周期时间=0.438s
2025-08-25 13:15:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=10, 平均周期时间=0.223s, 最近周期时间=0.467s
2025-08-25 13:16:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=10, 平均周期时间=0.220s, 最近周期时间=0.110s
2025-08-25 13:16:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=10, 平均周期时间=0.218s, 最近周期时间=0.121s
2025-08-25 13:16:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=10, 平均周期时间=0.218s, 最近周期时间=0.235s
2025-08-25 13:16:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=10, 平均周期时间=0.219s, 最近周期时间=0.251s
2025-08-25 13:17:07 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 13:17:07 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 13:17:07 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 13:17:07 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 13:17:07 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 13:17:07 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 13:17:07 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:17:08 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:18:00
2025-08-25 13:19:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=20, 平均周期时间=0.180s, 最近周期时间=0.118s
2025-08-25 13:19:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=20, 平均周期时间=0.179s, 最近周期时间=0.123s
2025-08-25 13:19:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=20, 平均周期时间=0.180s, 最近周期时间=0.229s
2025-08-25 13:19:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=20, 平均周期时间=0.181s, 最近周期时间=0.247s
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 13:19:08 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:19:09 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:20:00
2025-08-25 13:19:09 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:20:00
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:23:00
2025-08-25 13:22:33 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:23:00
2025-08-25 13:28:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=10, 平均周期时间=0.206s, 最近周期时间=0.236s
2025-08-25 13:28:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=10, 平均周期时间=0.207s, 最近周期时间=0.252s
2025-08-25 13:28:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=10, 平均周期时间=0.211s, 最近周期时间=0.352s
2025-08-25 13:28:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=10, 平均周期时间=0.215s, 最近周期时间=0.368s
2025-08-25 13:29:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=30, 平均周期时间=0.181s, 最近周期时间=0.137s
2025-08-25 13:29:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=30, 平均周期时间=0.181s, 最近周期时间=0.139s
2025-08-25 13:29:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=30, 平均周期时间=0.182s, 最近周期时间=0.271s
2025-08-25 13:29:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=30, 平均周期时间=0.182s, 最近周期时间=0.268s
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:31:00
2025-08-25 13:30:33 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:31:00
2025-08-25 13:31:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=10, 平均周期时间=0.199s, 最近周期时间=0.221s
2025-08-25 13:31:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=10, 平均周期时间=0.202s, 最近周期时间=0.339s
2025-08-25 13:31:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=10, 平均周期时间=0.209s, 最近周期时间=0.461s
2025-08-25 13:31:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=10, 平均周期时间=0.218s, 最近周期时间=0.574s
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:37:00
2025-08-25 13:36:09 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:37:00
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:39:00
2025-08-25 13:38:10 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:39:00
2025-08-25 13:39:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=40, 平均周期时间=0.181s, 最近周期时间=0.135s
2025-08-25 13:39:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=40, 平均周期时间=0.181s, 最近周期时间=0.143s
2025-08-25 13:39:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=40, 平均周期时间=0.182s, 最近周期时间=0.277s
2025-08-25 13:39:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=40, 平均周期时间=0.183s, 最近周期时间=0.296s
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:41:03 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:42:00
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:45:18 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:46:00
2025-08-25 13:47:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=10, 平均周期时间=0.209s, 最近周期时间=0.107s
2025-08-25 13:47:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=10, 平均周期时间=0.209s, 最近周期时间=0.225s
2025-08-25 13:47:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=10, 平均周期时间=0.211s, 最近周期时间=0.282s
2025-08-25 13:47:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=10, 平均周期时间=0.214s, 最近周期时间=0.332s
2025-08-25 13:49:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=50, 平均周期时间=0.181s, 最近周期时间=0.102s
2025-08-25 13:49:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=50, 平均周期时间=0.181s, 最近周期时间=0.109s
2025-08-25 13:49:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=50, 平均周期时间=0.181s, 最近周期时间=0.204s
2025-08-25 13:49:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=50, 平均周期时间=0.181s, 最近周期时间=0.221s
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:49:54 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:50:00
2025-08-25 13:54:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=10, 平均周期时间=0.235s, 最近周期时间=0.120s
2025-08-25 13:54:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=10, 平均周期时间=0.235s, 最近周期时间=0.232s
2025-08-25 13:54:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=10, 平均周期时间=0.237s, 最近周期时间=0.333s
2025-08-25 13:54:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=10, 平均周期时间=0.242s, 最近周期时间=0.430s
2025-08-25 13:54:54 [INFO] [VolumeSurgeProcessor] - 接收到信号 15，正在停止处理器...
2025-08-25 13:54:54 [INFO] [VolumeSurgeProcessor] - 🛑 停止成交量激增处理器...
2025-08-25 13:54:54 [INFO] [VolumeSurgeProcessor] - 🛑 信号处理线程结束运行
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] - 🛑 工作线程 2 结束运行
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] - 🛑 工作线程 3 结束运行
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] - 🛑 工作线程 1 结束运行
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] - 🛑 工作线程 0 结束运行
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] - 📊 最终统计信息:
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    运行时间: 2:09:59.733598
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    总周期数: 220
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    总信号数: 0
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    平均周期时间: 0.183秒
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    处理股票数: 241670
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    错误次数: 0
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] -    平均执行一轮时间: 0.183秒
2025-08-25 13:55:00 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器已停止
2025-08-25 13:55:24 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 13:55:24 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 13:55:24 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 13:55:24 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 13:55:24 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 13:55:24 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 13:55:24 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:55:39 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 13:56:00
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 13:59:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 14:00:00
2025-08-25 14:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 14:01:00
2025-08-25 14:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 14:01:00
2025-08-25 14:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 14:01:00
2025-08-25 14:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 14:01:00
2025-08-25 14:08:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=10, 平均周期时间=0.139s, 最近周期时间=0.081s
2025-08-25 14:08:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=10, 平均周期时间=0.137s, 最近周期时间=0.084s
2025-08-25 14:08:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=10, 平均周期时间=0.138s, 最近周期时间=0.161s
2025-08-25 14:08:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=10, 平均周期时间=0.139s, 最近周期时间=0.167s
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 14:12:12 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 14:12:13 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 14:13:00
2025-08-25 14:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=20, 平均周期时间=84.478s, 最近周期时间=0.093s
2025-08-25 14:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=20, 平均周期时间=83.396s, 最近周期时间=0.095s
2025-08-25 14:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=20, 平均周期时间=82.342s, 最近周期时间=0.097s
2025-08-25 14:45:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=20, 平均周期时间=81.314s, 最近周期时间=0.098s
2025-08-25 14:48:41 [INFO] [VolumeSurgeProcessor] - 飞书配置读取:
2025-08-25 14:48:41 [INFO] [VolumeSurgeProcessor] -   webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/9303bf07-0844-4df7-9d77-cd97006925c0
2025-08-25 14:48:41 [INFO] [VolumeSurgeProcessor] -   secret: Hs357aRJgy...r1fqb
2025-08-25 14:48:41 [INFO] [VolumeSurgeProcessor] - ✅ 飞书通知器初始化成功 - 启用签名验证
2025-08-25 14:48:41 [INFO] [VolumeSurgeProcessor] - 飞书通知器初始化完成 - 启用签名验证
2025-08-25 14:48:41 [INFO] [VolumeSurgeProcessor] - 成交量激增处理器初始化完成
2025-08-25 14:48:41 [INFO] [VolumeSurgeProcessor] - 🚀 启动成交量激增处理器...
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - 📊 从数据库加载 4394 只股票（所有股票）
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - 📊 股票分组完成: 4 个线程组，每组股票数: [1099, 1099, 1098, 1098]
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - ✅ 使用数据库模式获取数据
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 0 开始运行，处理 1099 只股票
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 0，处理 1099 只股票 (模式: 数据库)
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 1 开始运行，处理 1099 只股票
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 1，处理 1099 只股票 (模式: 数据库)
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 2 开始运行，处理 1098 只股票
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 2，处理 1098 只股票 (模式: 数据库)
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - 🔄 工作线程 3 开始运行，处理 1098 只股票
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - ✅ 启动工作线程 3，处理 1098 只股票 (模式: 数据库)
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - ✅ 所有 4 个工作线程启动完成
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - 🔄 信号处理线程开始运行
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - ✅ 信号处理线程启动完成
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - ✅ 成交量激增处理器启动成功，监控 4394 只股票
2025-08-25 14:48:42 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 14:49:00
2025-08-25 14:57:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 3 性能统计: 周期=10, 平均周期时间=0.126s, 最近周期时间=0.085s
2025-08-25 14:57:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 性能统计: 周期=10, 平均周期时间=0.125s, 最近周期时间=0.089s
2025-08-25 14:57:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 1 性能统计: 周期=10, 平均周期时间=0.126s, 最近周期时间=0.162s
2025-08-25 14:57:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 0 性能统计: 周期=10, 平均周期时间=0.127s, 最近周期时间=0.168s
2025-08-25 15:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 15:01:00
2025-08-25 15:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 15:01:00
2025-08-25 15:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 15:01:00
2025-08-25 15:00:00 [INFO] [VolumeSurgeProcessor] - ⏰ 等待到下一个整分钟执行: 15:01:00
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] - 🔄 线程 2 到达15:05收盘时间，准备退出进程
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] - 🛑 VolumeSurgeProcessor到达15:05收盘时间，开始清理资源并退出进程
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] - 📊 最终统计信息:
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] -    运行时间: 0:16:17.985753
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] - 🛑 工作线程 3 结束运行
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] - 🛑 工作线程 0 结束运行
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] - 🛑 工作线程 1 结束运行
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] -    总周期数: 68
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] -    总信号数: 0
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] -    平均周期时间: 0.127秒
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] -    处理股票数: 74698
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] -    错误次数: 0
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] -    平均执行一轮时间: 0.127秒
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] - 🧹 开始清理VolumeSurgeProcessor所有资源...
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] - ✅ VolumeSurgeProcessor资源清理完成
2025-08-25 15:05:00 [INFO] [VolumeSurgeProcessor] - 🔥 VolumeSurgeProcessor进程即将强制退出
