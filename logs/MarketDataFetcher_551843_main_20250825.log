﻿2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 成功加载配置文件: config/main.toml
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 成功加载市场数据获取器配置文件: config/market_data_fetcher.toml
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - [成功] MarketDataFetcher重构完成：专注于tick数据获取和数据库存储
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 计算得到最优线程数: 16 (CPU核心数: 8, 配置最大线程数: 100)
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 开始加载股票列表...
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 正在从数据库加载股票列表...
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - [初始化] 股票列表加载完成: 4394 只股票
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - [初始化] 有效股票代码: 4394 个
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - [初始化] 前5个股票代码: ['000001', '000002', '000006', '000007', '000008']
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 创建了 88 个股票批次，每批最多 50 只股票
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 正在初始化数据库管理器...
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - [成功] 数据库管理器初始化成功
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 📦 使用数据库缓存机制
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 重置tick数据记录，清空 0 只股票的历史数据（包含成交量和价格）
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 开始加载股票列表...
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 正在从数据库加载股票列表...
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - [调试] 执行数据库查询: SELECT stock_code, stock_name FROM stock_info ORDER BY stock_code
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - [调试] 数据库查询结果数量: 4394
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - [成功] 从数据库成功获取 4394 只股票
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - [数据库] 从PostgreSQL xystock数据库加载 4394 只股票
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 成功将 4394 只股票保存到配置文件: config/stock_list.toml
2025-08-25 12:37:59 [INFO] [MarketDataFetcher_551843_main] - 定时任务线程启动
