﻿2025-08-23 13:17:12 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 651.39 毫秒，每秒处理股票数: 6745.60
2025-08-23 13:17:12 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.65s (网络: 0.65s, 处理: -0.00s, 数据库: 0.65s)
2025-08-23 13:17:14 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:14 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:14 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:14 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:14, 股票数: 4394
2025-08-23 13:17:14 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 666.00 毫秒，每秒处理股票数: 6597.63
2025-08-23 13:17:14 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.67s (网络: 0.67s, 处理: -0.00s, 数据库: 0.67s)
2025-08-23 13:17:16 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:16 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:16 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:16 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:16, 股票数: 4394
2025-08-23 13:17:16 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 690.21 毫秒，每秒处理股票数: 6366.22
2025-08-23 13:17:16 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: -0.00s, 数据库: 0.69s)
2025-08-23 13:17:18 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:18 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:18 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:18 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:18, 股票数: 4394
2025-08-23 13:17:18 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 691.01 毫秒，每秒处理股票数: 6358.77
2025-08-23 13:17:18 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: -0.00s, 数据库: 0.69s)
2025-08-23 13:17:20 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:20 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:20 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:20 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:20, 股票数: 4394
2025-08-23 13:17:20 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 762.40 毫秒，每秒处理股票数: 5763.41
2025-08-23 13:17:20 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.76s (网络: 0.76s, 处理: 0.00s, 数据库: 0.76s)
2025-08-23 13:17:22 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:22 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:22 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:22 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:22, 股票数: 4394
2025-08-23 13:17:22 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 701.77 毫秒，每秒处理股票数: 6261.30
2025-08-23 13:17:22 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.70s (网络: 0.70s, 处理: -0.00s, 数据库: 0.70s)
2025-08-23 13:17:24 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:24 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:24 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:24 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:24, 股票数: 4394
2025-08-23 13:17:24 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 720.42 毫秒，每秒处理股票数: 6099.25
2025-08-23 13:17:24 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.72s (网络: 0.72s, 处理: 0.00s, 数据库: 0.72s)
2025-08-23 13:17:26 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:26 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:26 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:26 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:26, 股票数: 4394
2025-08-23 13:17:26 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 627.73 毫秒，每秒处理股票数: 6999.84
2025-08-23 13:17:26 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.63s (网络: 0.63s, 处理: -0.00s, 数据库: 0.63s)
2025-08-23 13:17:28 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:28 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:28 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:28 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:28, 股票数: 4394
2025-08-23 13:17:28 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 659.95 毫秒，每秒处理股票数: 6658.04
2025-08-23 13:17:28 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.66s (网络: 0.66s, 处理: -0.00s, 数据库: 0.66s)
2025-08-23 13:17:30 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:30 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:30 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:30 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:30, 股票数: 4394
2025-08-23 13:17:30 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 688.40 毫秒，每秒处理股票数: 6382.87
2025-08-23 13:17:30 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: 0.00s, 数据库: 0.69s)
2025-08-23 13:17:32 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:32 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:32 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:32 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:32, 股票数: 4394
2025-08-23 13:17:32 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 671.44 毫秒，每秒处理股票数: 6544.16
2025-08-23 13:17:32 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.67s (网络: 0.67s, 处理: -0.00s, 数据库: 0.67s)
2025-08-23 13:17:34 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:34 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:34 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:34 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:34, 股票数: 4394
2025-08-23 13:17:34 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 628.95 毫秒，每秒处理股票数: 6986.25
2025-08-23 13:17:34 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.63s (网络: 0.63s, 处理: -0.00s, 数据库: 0.63s)
2025-08-23 13:17:36 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:36 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:36 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:36 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:36, 股票数: 4394
2025-08-23 13:17:36 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 654.22 毫秒，每秒处理股票数: 6716.44
2025-08-23 13:17:36 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.65s (网络: 0.65s, 处理: -0.00s, 数据库: 0.65s)
2025-08-23 13:17:38 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:38 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:38 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:38 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:38, 股票数: 4394
2025-08-23 13:17:38 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 706.01 毫秒，每秒处理股票数: 6223.73
2025-08-23 13:17:38 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.71s (网络: 0.71s, 处理: -0.00s, 数据库: 0.71s)
2025-08-23 13:17:40 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:40 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:40 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:40 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:40, 股票数: 4394
2025-08-23 13:17:40 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 657.10 毫秒，每秒处理股票数: 6686.94
2025-08-23 13:17:40 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.66s (网络: 0.66s, 处理: 0.00s, 数据库: 0.66s)
2025-08-23 13:17:42 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:42 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:42 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:42 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:42, 股票数: 4394
2025-08-23 13:17:42 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 770.04 毫秒，每秒处理股票数: 5706.21
2025-08-23 13:17:42 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.77s (网络: 0.77s, 处理: -0.00s, 数据库: 0.77s)
2025-08-23 13:17:44 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:44 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:44 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:44 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:44, 股票数: 4394
2025-08-23 13:17:44 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 747.63 毫秒，每秒处理股票数: 5877.21
2025-08-23 13:17:44 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.75s (网络: 0.75s, 处理: -0.00s, 数据库: 0.75s)
2025-08-23 13:17:46 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:46 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:46 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:46 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:46, 股票数: 4394
2025-08-23 13:17:46 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 703.68 毫秒，每秒处理股票数: 6244.28
2025-08-23 13:17:46 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.70s (网络: 0.70s, 处理: -0.00s, 数据库: 0.70s)
2025-08-23 13:17:48 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:48 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:48 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:48 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:48, 股票数: 4394
2025-08-23 13:17:48 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 661.64 毫秒，每秒处理股票数: 6641.08
2025-08-23 13:17:48 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.66s (网络: 0.66s, 处理: -0.00s, 数据库: 0.66s)
2025-08-23 13:17:50 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:50 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:50 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:50 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:50, 股票数: 4394
2025-08-23 13:17:50 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 709.44 毫秒，每秒处理股票数: 6193.62
2025-08-23 13:17:50 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.71s (网络: 0.71s, 处理: -0.00s, 数据库: 0.71s)
2025-08-23 13:17:52 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:52 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:52 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:52 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:52, 股票数: 4394
2025-08-23 13:17:52 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 669.69 毫秒，每秒处理股票数: 6561.28
2025-08-23 13:17:52 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.67s (网络: 0.67s, 处理: -0.00s, 数据库: 0.67s)
2025-08-23 13:17:54 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:54 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:54 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:54 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:54, 股票数: 4394
2025-08-23 13:17:54 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 677.76 毫秒，每秒处理股票数: 6483.14
2025-08-23 13:17:54 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.68s (网络: 0.68s, 处理: -0.00s, 数据库: 0.68s)
2025-08-23 13:17:56 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:56 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:56 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:56 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:56, 股票数: 4394
2025-08-23 13:17:56 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 706.00 毫秒，每秒处理股票数: 6223.84
2025-08-23 13:17:56 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.71s (网络: 0.71s, 处理: -0.00s, 数据库: 0.71s)
2025-08-23 13:17:58 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:17:58 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:17:58 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:17:58 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:17:58, 股票数: 4394
2025-08-23 13:17:58 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 671.88 毫秒，每秒处理股票数: 6539.90
2025-08-23 13:17:58 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.67s (网络: 0.67s, 处理: -0.00s, 数据库: 0.67s)
2025-08-23 13:18:00 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:00 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:00 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:00 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:00, 股票数: 4394
2025-08-23 13:18:00 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 665.45 毫秒，每秒处理股票数: 6603.00
2025-08-23 13:18:00 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.67s (网络: 0.67s, 处理: -0.00s, 数据库: 0.67s)
2025-08-23 13:18:02 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:02 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:02 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:02 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:02, 股票数: 4394
2025-08-23 13:18:02 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 682.75 毫秒，每秒处理股票数: 6435.69
2025-08-23 13:18:02 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.68s (网络: 0.68s, 处理: -0.00s, 数据库: 0.68s)
2025-08-23 13:18:04 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:04 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:04 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:04 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:04, 股票数: 4394
2025-08-23 13:18:04 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 804.15 毫秒，每秒处理股票数: 5464.14
2025-08-23 13:18:04 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.80s (网络: 0.80s, 处理: 0.00s, 数据库: 0.80s)
2025-08-23 13:18:06 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:06 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:06 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:06 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:06, 股票数: 4394
2025-08-23 13:18:06 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 670.91 毫秒，每秒处理股票数: 6549.27
2025-08-23 13:18:06 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.67s (网络: 0.67s, 处理: -0.00s, 数据库: 0.67s)
2025-08-23 13:18:08 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:08 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:08 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:08 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:08, 股票数: 4394
2025-08-23 13:18:08 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 666.49 毫秒，每秒处理股票数: 6592.73
2025-08-23 13:18:08 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.67s (网络: 0.67s, 处理: -0.00s, 数据库: 0.67s)
2025-08-23 13:18:10 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:10 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:10 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:10 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:10, 股票数: 4394
2025-08-23 13:18:10 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 675.22 毫秒，每秒处理股票数: 6507.53
2025-08-23 13:18:10 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.68s (网络: 0.68s, 处理: -0.00s, 数据库: 0.68s)
2025-08-23 13:18:12 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:12 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:12 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:12 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:12, 股票数: 4394
2025-08-23 13:18:12 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 683.07 毫秒，每秒处理股票数: 6432.76
2025-08-23 13:18:12 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.68s (网络: 0.68s, 处理: -0.00s, 数据库: 0.68s)
2025-08-23 13:18:14 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:14 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:14 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:14 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:14, 股票数: 4394
2025-08-23 13:18:14 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 635.19 毫秒，每秒处理股票数: 6917.57
2025-08-23 13:18:14 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.64s (网络: 0.64s, 处理: -0.00s, 数据库: 0.64s)
2025-08-23 13:18:16 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:16 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:16 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:16 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:16, 股票数: 4394
2025-08-23 13:18:16 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 701.24 毫秒，每秒处理股票数: 6266.04
2025-08-23 13:18:16 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.70s (网络: 0.70s, 处理: -0.00s, 数据库: 0.70s)
2025-08-23 13:18:18 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:18 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:18 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:18 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:18, 股票数: 4394
2025-08-23 13:18:18 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 658.44 毫秒，每秒处理股票数: 6673.37
2025-08-23 13:18:18 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.66s (网络: 0.66s, 处理: -0.00s, 数据库: 0.66s)
2025-08-23 13:18:20 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:20 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:20 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:20 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:20, 股票数: 4394
2025-08-23 13:18:20 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 738.77 毫秒，每秒处理股票数: 5947.69
2025-08-23 13:18:20 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.74s (网络: 0.74s, 处理: 0.00s, 数据库: 0.74s)
2025-08-23 13:18:22 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:22 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:22 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:22 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:22, 股票数: 4394
2025-08-23 13:18:22 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 700.63 毫秒，每秒处理股票数: 6271.48
2025-08-23 13:18:22 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.70s (网络: 0.70s, 处理: -0.00s, 数据库: 0.70s)
2025-08-23 13:18:24 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:24 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:24 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:24 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:24, 股票数: 4394
2025-08-23 13:18:24 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 663.98 毫秒，每秒处理股票数: 6617.70
2025-08-23 13:18:24 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.66s (网络: 0.66s, 处理: -0.00s, 数据库: 0.66s)
2025-08-23 13:18:26 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:26 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:26 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:26 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:26, 股票数: 4394
2025-08-23 13:18:26 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 807.72 毫秒，每秒处理股票数: 5439.99
2025-08-23 13:18:26 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.81s (网络: 0.81s, 处理: 0.00s, 数据库: 0.81s)
2025-08-23 13:18:28 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:28 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:28 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:28 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:28, 股票数: 4394
2025-08-23 13:18:28 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 716.75 毫秒，每秒处理股票数: 6130.45
2025-08-23 13:18:28 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.72s (网络: 0.72s, 处理: -0.00s, 数据库: 0.72s)
2025-08-23 13:18:30 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:30 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:30 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:30 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:30, 股票数: 4394
2025-08-23 13:18:30 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 670.94 毫秒，每秒处理股票数: 6549.07
2025-08-23 13:18:30 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.67s (网络: 0.67s, 处理: -0.00s, 数据库: 0.67s)
2025-08-23 13:18:32 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:32 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:32 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:32 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:32, 股票数: 4394
2025-08-23 13:18:32 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 697.91 毫秒，每秒处理股票数: 6295.91
2025-08-23 13:18:32 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.70s (网络: 0.70s, 处理: -0.00s, 数据库: 0.70s)
2025-08-23 13:18:34 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:34 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:34 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:34 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:34, 股票数: 4394
2025-08-23 13:18:34 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 730.55 毫秒，每秒处理股票数: 6014.61
2025-08-23 13:18:34 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.73s (网络: 0.73s, 处理: -0.00s, 数据库: 0.73s)
2025-08-23 13:18:36 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:36 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:36 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:36 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:36, 股票数: 4394
2025-08-23 13:18:36 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 686.74 毫秒，每秒处理股票数: 6398.36
2025-08-23 13:18:36 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: -0.00s, 数据库: 0.69s)
2025-08-23 13:18:38 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:38 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:38 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:38 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:38, 股票数: 4394
2025-08-23 13:18:38 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 693.24 毫秒，每秒处理股票数: 6338.35
2025-08-23 13:18:38 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: 0.00s, 数据库: 0.69s)
2025-08-23 13:18:40 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:40 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:40 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:40 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:40, 股票数: 4394
2025-08-23 13:18:40 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 697.63 毫秒，每秒处理股票数: 6298.51
2025-08-23 13:18:40 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.70s (网络: 0.70s, 处理: -0.00s, 数据库: 0.70s)
2025-08-23 13:18:42 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:42 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:42 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:42 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:42, 股票数: 4394
2025-08-23 13:18:42 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 682.98 毫秒，每秒处理股票数: 6433.53
2025-08-23 13:18:42 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.68s (网络: 0.68s, 处理: -0.00s, 数据库: 0.68s)
2025-08-23 13:18:44 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:44 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:44 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:44 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:44, 股票数: 4394
2025-08-23 13:18:44 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 684.99 毫秒，每秒处理股票数: 6414.66
2025-08-23 13:18:44 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: 0.00s, 数据库: 0.69s)
2025-08-23 13:18:46 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:46 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:46 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:46 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:46, 股票数: 4394
2025-08-23 13:18:46 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 710.47 毫秒，每秒处理股票数: 6184.62
2025-08-23 13:18:46 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.71s (网络: 0.71s, 处理: 0.00s, 数据库: 0.71s)
2025-08-23 13:18:48 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:48 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:48 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:48 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:48, 股票数: 4394
2025-08-23 13:18:48 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 791.83 毫秒，每秒处理股票数: 5549.14
2025-08-23 13:18:48 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.79s (网络: 0.79s, 处理: -0.00s, 数据库: 0.79s)
2025-08-23 13:18:50 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:50 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:50 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:50 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:50, 股票数: 4394
2025-08-23 13:18:50 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 703.16 毫秒，每秒处理股票数: 6248.98
2025-08-23 13:18:50 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.70s (网络: 0.70s, 处理: -0.00s, 数据库: 0.70s)
2025-08-23 13:18:52 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:52 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:52 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:52 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:52, 股票数: 4394
2025-08-23 13:18:52 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 705.70 毫秒，每秒处理股票数: 6226.48
2025-08-23 13:18:52 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.71s (网络: 0.71s, 处理: -0.00s, 数据库: 0.71s)
2025-08-23 13:18:54 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:54 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:54 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:54 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:54, 股票数: 4394
2025-08-23 13:18:54 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 672.69 毫秒，每秒处理股票数: 6532.02
2025-08-23 13:18:54 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.67s (网络: 0.67s, 处理: -0.00s, 数据库: 0.67s)
2025-08-23 13:18:56 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:56 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:56 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:56 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:56, 股票数: 4394
2025-08-23 13:18:56 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 725.17 毫秒，每秒处理股票数: 6059.24
2025-08-23 13:18:56 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.73s (网络: 0.73s, 处理: 0.00s, 数据库: 0.73s)
2025-08-23 13:18:58 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:18:58 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:18:58 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:18:58 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:18:58, 股票数: 4394
2025-08-23 13:18:58 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 727.50 毫秒，每秒处理股票数: 6039.84
2025-08-23 13:18:58 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.73s (网络: 0.73s, 处理: -0.00s, 数据库: 0.73s)
2025-08-23 13:19:00 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:00 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:00 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:00 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:00, 股票数: 4394
2025-08-23 13:19:00 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 712.92 毫秒，每秒处理股票数: 6163.35
2025-08-23 13:19:00 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.71s (网络: 0.71s, 处理: -0.00s, 数据库: 0.71s)
2025-08-23 13:19:02 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:02 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:02 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:02 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:02, 股票数: 4394
2025-08-23 13:19:02 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 735.33 毫秒，每秒处理股票数: 5975.55
2025-08-23 13:19:02 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.74s (网络: 0.74s, 处理: -0.00s, 数据库: 0.74s)
2025-08-23 13:19:04 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:04 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:04 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:04 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:04, 股票数: 4394
2025-08-23 13:19:04 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 698.81 毫秒，每秒处理股票数: 6287.86
2025-08-23 13:19:04 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.70s (网络: 0.70s, 处理: -0.00s, 数据库: 0.70s)
2025-08-23 13:19:06 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:06 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:06 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:06 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:06, 股票数: 4394
2025-08-23 13:19:06 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 686.79 毫秒，每秒处理股票数: 6397.84
2025-08-23 13:19:06 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: -0.00s, 数据库: 0.69s)
2025-08-23 13:19:08 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:08 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:08 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:08 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:08, 股票数: 4394
2025-08-23 13:19:08 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 690.55 毫秒，每秒处理股票数: 6363.09
2025-08-23 13:19:08 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: 0.00s, 数据库: 0.69s)
2025-08-23 13:19:10 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:10 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:10 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:10 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:10, 股票数: 4394
2025-08-23 13:19:10 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 785.56 毫秒，每秒处理股票数: 5593.43
2025-08-23 13:19:10 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.79s (网络: 0.79s, 处理: 0.00s, 数据库: 0.79s)
2025-08-23 13:19:12 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:12 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:12 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:12 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:12, 股票数: 4394
2025-08-23 13:19:12 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 732.14 毫秒，每秒处理股票数: 6001.60
2025-08-23 13:19:12 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.73s (网络: 0.73s, 处理: -0.00s, 数据库: 0.73s)
2025-08-23 13:19:14 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:14 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:14 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:14 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:14, 股票数: 4394
2025-08-23 13:19:14 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 715.48 毫秒，每秒处理股票数: 6141.29
2025-08-23 13:19:14 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.72s (网络: 0.72s, 处理: -0.00s, 数据库: 0.72s)
2025-08-23 13:19:16 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:16 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:16 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:16 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:16, 股票数: 4394
2025-08-23 13:19:16 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 665.35 毫秒，每秒处理股票数: 6604.06
2025-08-23 13:19:16 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.67s (网络: 0.67s, 处理: -0.00s, 数据库: 0.67s)
2025-08-23 13:19:18 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:18 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:18 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:18 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:18, 股票数: 4394
2025-08-23 13:19:18 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 691.39 毫秒，每秒处理股票数: 6355.31
2025-08-23 13:19:18 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: 0.00s, 数据库: 0.69s)
2025-08-23 13:19:20 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:20 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:20 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:20 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:20, 股票数: 4394
2025-08-23 13:19:20 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 688.51 毫秒，每秒处理股票数: 6381.90
2025-08-23 13:19:20 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: -0.00s, 数据库: 0.69s)
2025-08-23 13:19:22 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:22 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:22 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:22 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:22, 股票数: 4394
2025-08-23 13:19:22 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 686.71 毫秒，每秒处理股票数: 6398.64
2025-08-23 13:19:22 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: 0.00s, 数据库: 0.69s)
2025-08-23 13:19:24 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:24 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:24 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:24 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:24, 股票数: 4394
2025-08-23 13:19:24 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 670.19 毫秒，每秒处理股票数: 6556.39
2025-08-23 13:19:24 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.67s (网络: 0.67s, 处理: -0.00s, 数据库: 0.67s)
2025-08-23 13:19:26 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:26 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:26 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:26 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:26, 股票数: 4394
2025-08-23 13:19:26 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 657.46 毫秒，每秒处理股票数: 6683.27
2025-08-23 13:19:26 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.66s (网络: 0.66s, 处理: -0.00s, 数据库: 0.66s)
2025-08-23 13:19:28 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:28 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:28 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:28 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:28, 股票数: 4394
2025-08-23 13:19:28 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 693.21 毫秒，每秒处理股票数: 6338.65
2025-08-23 13:19:28 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: -0.00s, 数据库: 0.69s)
2025-08-23 13:19:30 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:30 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:30 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:30 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:30, 股票数: 4394
2025-08-23 13:19:30 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 686.66 毫秒，每秒处理股票数: 6399.07
2025-08-23 13:19:30 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.69s (网络: 0.69s, 处理: -0.00s, 数据库: 0.69s)
2025-08-23 13:19:32 [WARNING] [MarketDataFetcher_592874_main] - 向量化时间解析失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'，使用标准化的当前时间
2025-08-23 13:19:32 [ERROR] [MarketDataFetcher_592874_main] - 保存Tick数据失败: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'
2025-08-23 13:19:32 [ERROR] [MarketDataFetcher_592874_main] - Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4157, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(raw_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4175, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 3672, in _fetch_realtime_quotes
    combined_df.loc[:, 'trade_time'] = self._parse_server_time_vectorized(combined_df)
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/Program/xystock/processes/market_data_fetcher.py", line 4183, in _parse_server_time_vectorized
    standardized_time = self._adjust_to_trading_time(current_time)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MarketDataFetcher' object has no attribute '_adjust_to_trading_time'. Did you mean: '_adjust_to_trading_time_cached'?

2025-08-23 13:19:32 [INFO] [MarketDataFetcher_592874_main] - 处理完成，时间: 13:19:32, 股票数: 4394
2025-08-23 13:19:32 [INFO] [MarketDataFetcher_592874_main] - 获取实时行情数据: 成功获取 4394 只股票的行情，成功批次: 88，失败批次: 0，耗时: 781.33 毫秒，每秒处理股票数: 5623.75
2025-08-23 13:19:32 [INFO] [MarketDataFetcher_592874_main] - [目标] 本次获取和保存数据总耗时: 0.78s (网络: 0.78s, 处理: -0.00s, 数据库: 0.78s)
